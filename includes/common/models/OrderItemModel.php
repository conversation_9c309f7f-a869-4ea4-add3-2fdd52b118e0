<?php
/**
 * Modèle pour la gestion des items de commande
 * 
 * @package TechCMS
 * @version 1.1.0
 */

namespace TechCMS\Common\Models;

use PDO;
use TechCMS\Common\Core\Logger;

class OrderItemModel extends BaseModel {
    protected $table = 'order_items';
    
    protected $fillable = [
        'order_id',
        'template_id',
        'quantity',
        'unit_price',
        'total_price',
        'customizations',
        'license_id'
    ];

    protected $casts = [
        'unit_price' => 'decimal',
        'total_price' => 'decimal',
        'quantity' => 'int',
        'customizations' => 'json'
    ];

    /**
     * Crée plusieurs items de commande en une transaction
     */
    public function createMultiple($orderId, array $items) {
        try {
            $this->db->beginTransaction();
            
            $createdItems = [];
            
            foreach ($items as $item) {
                $itemData = [
                    'order_id' => $orderId,
                    'template_id' => $item['template_id'],
                    'quantity' => $item['quantity'] ?? 1,
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['total_price'],
                    'customizations' => $item['customizations'] ?? null
                ];
                
                $createdItem = $this->create($itemData);
                if ($createdItem) {
                    $createdItems[] = $createdItem;
                } else {
                    throw new \Exception("Erreur lors de la création de l'item de commande");
                }
            }
            
            $this->db->commit();
            
            Logger::channel('api')->info('Items de commande créés', [
                'order_id' => $orderId,
                'items_count' => count($createdItems)
            ]);
            
            return $createdItems;
        } catch (\Exception $e) {
            $this->db->rollBack();
            
            Logger::channel('api')->error('Erreur lors de la création des items de commande', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Récupère les items d'une commande avec les détails des templates
     */
    public function getOrderItems($orderId) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    oi.*,
                    lt.name as template_name,
                    lt.description as template_description,
                    lt.domain_limit,
                    lt.installation_limit,
                    lt.update_permissions,
                    lt.billing_cycle,
                    l.license_key,
                    l.status as license_status
                FROM {$this->table} oi
                LEFT JOIN license_templates lt ON oi.template_id = lt.id
                LEFT JOIN licenses l ON oi.license_id = l.id
                WHERE oi.order_id = :order_id
                ORDER BY oi.id
            ");
            
            $stmt->execute(['order_id' => $orderId]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Décoder les customisations JSON
            foreach ($items as &$item) {
                if (isset($item['customizations']) && is_string($item['customizations'])) {
                    $item['customizations'] = json_decode($item['customizations'], true);
                }
            }

            return $items;
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des items de commande', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Met à jour l'ID de licence pour un item
     */
    public function updateLicenseId($itemId, $licenseId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE {$this->table} 
                SET license_id = :license_id, updated_at = :updated_at 
                WHERE id = :id
            ");
            
            $result = $stmt->execute([
                'license_id' => $licenseId,
                'updated_at' => date('Y-m-d H:i:s'),
                'id' => $itemId
            ]);

            Logger::channel('api')->info('Licence associée à l\'item de commande', [
                'item_id' => $itemId,
                'license_id' => $licenseId
            ]);

            return $result;
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de l\'association de la licence', [
                'item_id' => $itemId,
                'license_id' => $licenseId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Récupère les items non provisionnés d'une commande
     */
    public function getUnprovisionedItems($orderId) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    oi.*,
                    lt.name as template_name,
                    lt.domain_limit,
                    lt.installation_limit,
                    lt.update_permissions
                FROM {$this->table} oi
                LEFT JOIN license_templates lt ON oi.template_id = lt.id
                WHERE oi.order_id = :order_id AND oi.license_id IS NULL
                ORDER BY oi.id
            ");
            
            $stmt->execute(['order_id' => $orderId]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Décoder les customisations JSON
            foreach ($items as &$item) {
                if (isset($item['customizations']) && is_string($item['customizations'])) {
                    $item['customizations'] = json_decode($item['customizations'], true);
                }
            }

            return $items;
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des items non provisionnés', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Calcule le total d'un item en fonction du template et des customisations
     */
    public function calculateItemTotal($templateId, $quantity = 1, $customizations = []) {
        try {
            // Récupérer le template
            $stmt = $this->db->prepare("SELECT * FROM license_templates WHERE id = :id");
            $stmt->execute(['id' => $templateId]);
            $template = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$template) {
                throw new \Exception("Template non trouvé");
            }

            $basePrice = (float)$template['price'];
            $setupFee = (float)($template['setup_fee'] ?? 0);

            // Calculer les coûts supplémentaires basés sur les customisations
            $additionalCost = 0;

            // Coût pour domaines supplémentaires (au-delà de la limite du template)
            if (isset($customizations['additional_domains']) && $customizations['additional_domains'] > 0) {
                $additionalCost += $customizations['additional_domains'] * 10; // 10€ par domaine supplémentaire
            }

            // Coût pour installations supplémentaires
            if (isset($customizations['additional_installations']) && $customizations['additional_installations'] > 0) {
                $additionalCost += $customizations['additional_installations'] * 5; // 5€ par installation supplémentaire
            }

            // Calculer le nombre total de domaines (principal + supplémentaires)
            $totalDomains = 1; // Domaine principal toujours présent
            if (isset($customizations['additional_domains_list']) && is_array($customizations['additional_domains_list'])) {
                $totalDomains += count(array_filter($customizations['additional_domains_list']));
            }

            // Calculer les domaines payants (au-delà de la limite du template)
            $templateDomainLimit = (int)$template['domain_limit'];
            $paidDomainsFromList = max(0, $totalDomains - $templateDomainLimit);

            // Ajouter le coût des domaines au-delà de la limite
            if ($paidDomainsFromList > 0) {
                $additionalCost += $paidDomainsFromList * 10; // 10€ par domaine supplémentaire
            }

            $unitPrice = $basePrice + $additionalCost;
            $totalPrice = ($unitPrice * $quantity) + $setupFee;

            Logger::channel('api')->info('Calcul prix item détaillé', [
                'template_id' => $templateId,
                'template_name' => $template['name'],
                'base_price' => $basePrice,
                'setup_fee' => $setupFee,
                'template_domain_limit' => $templateDomainLimit,
                'total_domains' => $totalDomains,
                'paid_domains_from_list' => $paidDomainsFromList,
                'additional_domains_manual' => $customizations['additional_domains'] ?? 0,
                'additional_installations' => $customizations['additional_installations'] ?? 0,
                'additional_cost' => $additionalCost,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'customizations' => $customizations
            ]);

            return [
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'setup_fee' => $setupFee,
                'additional_cost' => $additionalCost
            ];
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du calcul du total de l\'item', [
                'template_id' => $templateId,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Vérifie si tous les items d'une commande sont provisionnés
     */
    public function areAllItemsProvisioned($orderId) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total, 
                       COUNT(license_id) as provisioned 
                FROM {$this->table} 
                WHERE order_id = :order_id
            ");
            
            $stmt->execute(['order_id' => $orderId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['total'] > 0 && $result['total'] == $result['provisioned'];
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la vérification du provisionnement', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
