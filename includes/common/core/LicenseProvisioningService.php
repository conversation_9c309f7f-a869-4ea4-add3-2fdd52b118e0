<?php
/**
 * Service de provisionnement automatique des licences
 * 
 * @package TechCMS
 * @version 1.1.0
 */

namespace TechCMS\Common\Core;

use TechCMS\Common\Models\OrderModel;
use TechCMS\Common\Models\OrderItemModel;
use TechCMS\Common\Models\LicenseTemplateModel;
use TechCMS\Common\Core\License\LicenseManager;
use TechCMS\Common\Core\EmailNotificationService;
use TechCMS\Common\Core\Database;
use TechCMS\Common\Core\Logger;

class LicenseProvisioningService {
    private static $instance = null;
    private $orderModel;
    private $orderItemModel;
    private $templateModel;
    private $licenseManager;
    private $emailService;

    private function __construct() {
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->templateModel = new LicenseTemplateModel();
        $this->licenseManager = LicenseManager::getInstance();
        $this->emailService = EmailNotificationService::getInstance();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Provisionne automatiquement les licences pour une commande
     */
    public function provisionOrder($orderId) {
        try {
            Logger::channel('api')->info('Début du provisionnement automatique', [
                'order_id' => $orderId
            ]);

            // Récupérer la commande avec ses items
            $order = $this->orderModel->getOrderWithItems($orderId);
            if (!$order) {
                throw new \Exception("Commande non trouvée: {$orderId}");
            }

            // Vérifier que la commande est payée
            if ($order['payment_status'] !== 'paid') {
                throw new \Exception("Commande non payée, provisionnement impossible");
            }

            // Vérifier que la commande n'est pas déjà provisionnée
            if ($order['provisioned_at']) {
                // Récupérer les licences déjà créées pour cette commande
                $existingLicenses = [];
                foreach ($order['items'] as $item) {
                    if ($item['license_id']) {
                        // Récupérer les détails de la licence
                        $db = Database::getInstance()->getConnection();
                        $stmt = $db->prepare("SELECT * FROM licenses WHERE id = :id");
                        $stmt->execute(['id' => $item['license_id']]);
                        $license = $stmt->fetch(\PDO::FETCH_ASSOC);
                        if ($license) {
                            $existingLicenses[] = $license;
                        }
                    }
                }

                Logger::channel('api')->warning('Commande déjà provisionnée', [
                    'order_id' => $orderId,
                    'provisioned_at' => $order['provisioned_at'],
                    'existing_licenses' => count($existingLicenses)
                ]);
                return [
                    'success' => true,
                    'message' => 'Commande déjà provisionnée',
                    'licenses' => $existingLicenses
                ];
            }

            // Récupérer les items non provisionnés
            $unprovisionedItems = $this->orderItemModel->getUnprovisionedItems($orderId);
            if (empty($unprovisionedItems)) {
                Logger::channel('api')->info('Aucun item à provisionner', [
                    'order_id' => $orderId
                ]);
                return [
                    'success' => true,
                    'message' => 'Aucun item à provisionner',
                    'licenses' => []
                ];
            }

            $provisionedLicenses = [];
            $errors = [];

            // Provisionner chaque item
            foreach ($unprovisionedItems as $item) {
                try {
                    $license = $this->provisionItem($order, $item);
                    if ($license) {
                        $provisionedLicenses[] = $license;
                        
                        // Associer la licence à l'item
                        $this->orderItemModel->updateLicenseId($item['id'], $license['id']);
                        
                        Logger::channel('api')->info('Item provisionné avec succès', [
                            'order_id' => $orderId,
                            'item_id' => $item['id'],
                            'license_id' => $license['id'],
                            'license_key' => $license['license_key']
                        ]);
                    }
                } catch (\Exception $e) {
                    $errors[] = [
                        'item_id' => $item['id'],
                        'template_name' => $item['template_name'],
                        'error' => $e->getMessage()
                    ];
                    
                    Logger::channel('api')->error('Erreur lors du provisionnement d\'item', [
                        'order_id' => $orderId,
                        'item_id' => $item['id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Marquer la commande comme provisionnée si tous les items sont provisionnés
            if ($this->orderItemModel->areAllItemsProvisioned($orderId)) {
                $this->orderModel->markAsProvisioned($orderId);
                
                Logger::channel('api')->info('Commande entièrement provisionnée', [
                    'order_id' => $orderId,
                    'licenses_count' => count($provisionedLicenses)
                ]);
            }

            // Envoyer la notification au client
            if (!empty($provisionedLicenses)) {
                $this->sendProvisioningNotification($order, $provisionedLicenses);
            }

            return [
                'success' => true,
                'message' => 'Provisionnement terminé',
                'licenses' => $provisionedLicenses,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du provisionnement automatique', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'licenses' => [],
                'errors' => []
            ];
        }
    }

    /**
     * Provisionne un item de commande spécifique
     */
    private function provisionItem($order, $item) {
        try {
            // Récupérer le template
            $template = $this->templateModel->find($item['template_id']);
            if (!$template) {
                throw new \Exception("Template non trouvé: {$item['template_id']}");
            }

            // Préparer les données de licence basées sur le template
            $licenseData = [
                'client_id' => $order['client_id'],
                'domain_limit' => $template['domain_limit'],
                'installation_limit' => $template['installation_limit'],
                'update_permissions' => $template['update_permissions'],
                'domains' => [],
                'ips' => []
            ];

            // Appliquer les customisations
            if (!empty($item['customizations'])) {
                $customizations = is_string($item['customizations'])
                    ? json_decode($item['customizations'], true)
                    : $item['customizations'];

                Logger::channel('api')->info('Provisionnement - Customisations détectées', [
                    'order_id' => $order['id'],
                    'item_id' => $item['id'],
                    'customizations' => $customizations
                ]);

                // Gestion des domaines avec déduplication
                $domainsToAdd = [];

                // Nouveau format (prioritaire)
                if (isset($customizations['primary_domain']) && !empty($customizations['primary_domain'])) {
                    $domainsToAdd[] = $customizations['primary_domain'];
                    Logger::channel('api')->info('Provisionnement - Domaine principal ajouté', [
                        'order_id' => $order['id'],
                        'primary_domain' => $customizations['primary_domain']
                    ]);

                    // Domaines supplémentaires de la liste
                    if (isset($customizations['additional_domains_list']) && is_array($customizations['additional_domains_list'])) {
                        Logger::channel('api')->info('Provisionnement - Domaines supplémentaires détectés', [
                            'order_id' => $order['id'],
                            'additional_domains_list' => $customizations['additional_domains_list']
                        ]);

                        foreach ($customizations['additional_domains_list'] as $domain) {
                            if (!empty($domain) && !in_array($domain, $domainsToAdd)) {
                                $domainsToAdd[] = $domain;
                                Logger::channel('api')->info('Provisionnement - Domaine supplémentaire ajouté', [
                                    'order_id' => $order['id'],
                                    'additional_domain' => $domain
                                ]);
                            }
                        }
                    }
                }
                // Ancien format (fallback si nouveau format absent)
                else if (isset($customizations['domain_name']) && !empty($customizations['domain_name'])) {
                    $domainsToAdd[] = $customizations['domain_name'];
                    Logger::channel('api')->info('Provisionnement - Domaine principal (ancien format) ajouté', [
                        'order_id' => $order['id'],
                        'domain_name' => $customizations['domain_name']
                    ]);
                }

                // Ajouter tous les domaines uniques à la licence
                foreach ($domainsToAdd as $domain) {
                    $licenseData['domains'][] = $domain;
                }

                Logger::channel('api')->info('Provisionnement - Domaines finaux', [
                    'order_id' => $order['id'],
                    'domains_count' => count($domainsToAdd),
                    'domains_list' => $domainsToAdd
                ]);

                // Augmenter les limites selon les customisations
                if (isset($customizations['additional_domains']) && $customizations['additional_domains'] > 0) {
                    $licenseData['domain_limit'] += (int)$customizations['additional_domains'];
                }

                if (isset($customizations['additional_installations']) && $customizations['additional_installations'] > 0) {
                    $licenseData['installation_limit'] += (int)$customizations['additional_installations'];
                }
            }

            // Calculer la date d'expiration basée sur le cycle de facturation
            $licenseData['expiry_date'] = $this->calculateExpiryDate($template['billing_cycle']);

            Logger::channel('api')->info('Provisionnement - Données licence finales avant création', [
                'order_id' => $order['id'],
                'item_id' => $item['id'],
                'license_data' => $licenseData,
                'domains_count' => count($licenseData['domains']),
                'domains_list' => $licenseData['domains']
            ]);

            // Créer la licence via le LicenseManager
            $license = $this->licenseManager->createLicense($licenseData);

            if (!$license) {
                Logger::channel('api')->error('Provisionnement - Échec création licence', [
                    'order_id' => $order['id'],
                    'item_id' => $item['id'],
                    'license_data' => $licenseData
                ]);
                throw new \Exception("Échec de la création de licence via LicenseManager");
            }

            Logger::channel('api')->info('Provisionnement - Licence créée avec succès', [
                'order_id' => $order['id'],
                'item_id' => $item['id'],
                'license_id' => $license['id'],
                'license_key' => $license['license_key'],
                'client_id' => $order['client_id'],
                'template_id' => $item['template_id'],
                'template_name' => $item['template_name'],
                'allowed_domains' => $license['allowed_domains'] ?? 'none',
                'domain_limit' => $license['domain_limit'] ?? 'none'
            ]);

            return $license;

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du provisionnement d\'item', [
                'item_id' => $item['id'],
                'template_id' => $item['template_id'],
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Calcule la date d'expiration basée sur le cycle de facturation
     */
    private function calculateExpiryDate($billingCycle) {
        $now = new \DateTime();
        
        switch ($billingCycle) {
            case 'monthly':
                $now->add(new \DateInterval('P1M'));
                break;
            case 'quarterly':
                $now->add(new \DateInterval('P3M'));
                break;
            case 'semi_annually':
                $now->add(new \DateInterval('P6M'));
                break;
            case 'annually':
                $now->add(new \DateInterval('P1Y'));
                break;
            case 'biennially':
                $now->add(new \DateInterval('P2Y'));
                break;
            case 'triennially':
                $now->add(new \DateInterval('P3Y'));
                break;
            case 'one_time':
            default:
                // Licence à vie ou pas d'expiration
                return null;
        }
        
        return $now->format('Y-m-d');
    }

    /**
     * Envoie une notification au client avec les nouvelles licences
     */
    private function sendProvisioningNotification($order, $licenses) {
        try {
            Logger::channel('api')->info('Envoi de notification de provisionnement', [
                'order_id' => $order['id'],
                'order_number' => $order['order_number'],
                'client_id' => $order['client_id'],
                'licenses_count' => count($licenses),
                'license_keys' => array_column($licenses, 'license_key')
            ]);

            // Envoyer l'email de notification
            $result = $this->emailService->sendLicenseProvisioningNotification($order, $licenses);

            if ($result) {
                Logger::channel('api')->info('Notification de provisionnement envoyée avec succès', [
                    'order_id' => $order['id'],
                    'licenses_count' => count($licenses)
                ]);
            } else {
                Logger::channel('api')->warning('Échec de l\'envoi de notification', [
                    'order_id' => $order['id']
                ]);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de l\'envoi de notification', [
                'order_id' => $order['id'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Provisionne manuellement une commande (pour les cas d'échec)
     */
    public function retryProvisioning($orderId) {
        Logger::channel('api')->info('Tentative de reprovisionnement manuel', [
            'order_id' => $orderId
        ]);

        return $this->provisionOrder($orderId);
    }

    /**
     * Vérifie si une commande peut être provisionnée
     */
    public function canProvisionOrder($orderId) {
        try {
            $order = $this->orderModel->find($orderId);
            if (!$order) {
                return false;
            }

            // Vérifier le statut de paiement
            if ($order['payment_status'] !== 'paid') {
                return false;
            }

            // Vérifier s'il y a des items non provisionnés
            $unprovisionedItems = $this->orderItemModel->getUnprovisionedItems($orderId);
            return !empty($unprovisionedItems);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la vérification de provisionnement', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
