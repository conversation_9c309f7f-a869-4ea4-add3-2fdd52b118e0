<?php
/**
 * Gestionnaire de licences pour le site commercial
 * Permet la création et la gestion des licences clients
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Common\Core\License;

use TechCMS\Common\Core\Database;
use TechCMS\Common\Core\Logger;

class LicenseManager {
    private static $instance = null;
    private $db;
    private $ablyKey;
    private $ablyConfig = null;

    public function __construct() {
        $this->db = Database::getInstance();
        $configPath = defined('ROOTPATH') ? ROOTPATH . '/config/config.php' : dirname(dirname(dirname(dirname(__FILE__)))) . '/config/config.php';

        if (!file_exists($configPath)) {
            throw new \Exception('Fichier de configuration non trouvé: ' . $configPath);
        }
        
        $config = require $configPath;
        
        if (!is_array($config) || !isset($config['ably']) || !isset($config['ably']['key'])) {
            throw new \Exception('Format de configuration invalide');
        }
        
        $this->ablyKey = $config['ably']['key'];
        
        // Configuration Ably pour le site commercial
        $this->ablyConfig = [
            'key' => $this->ablyKey,
            'enabled' => true
        ];
    }

    public function __destruct() {}

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Récupère la configuration Ably
     * Cette méthode est utilisée par le site commercial lui-même
     */
    public function getAblyConfig() {
        return $this->ablyConfig;
    }

    /**
     * Crée une nouvelle licence
     */
    public function createLicense(array $data) {
        if (empty($data['client_id'])) {
            throw new \Exception('Le client est requis pour créer une licence');
        }

        $license = [
            'license_key' => $this->generateLicenseKey(),
            'client_id' => (int)$data['client_id'],
            'domain_limit' => $data['domain_limit'] ?? 1,
            'installation_limit' => $data['installation_limit'] ?? 1,
            'allowed_domains' => is_array($data['domains']) ? implode(",", $data['domains']) : '',
            'allowed_ips' => is_array($data['ips']) ? implode(",", $data['ips']) : '',
            'ably_key' => defined('ABLY_KEY') ? ABLY_KEY : $this->ablyKey,
            'status' => 'active',
            'update_permissions' => isset($data['update_permissions']) ? (int)$data['update_permissions'] : 0,
            'expires_at' => !empty($data['expiry_date']) ? $data['expiry_date'] . ' 23:59:59' : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            $this->debug('Tentative de création de licence', $license);

            // Préparer la requête d'insertion
            $columns = implode(', ', array_keys($license));
            $placeholders = ':' . implode(', :', array_keys($license));
            $sql = "INSERT INTO licenses ({$columns}) VALUES ({$placeholders})";

            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($license);

            if ($result) {
                $license['id'] = $this->db->lastInsertId();
                $this->debug('Licence créée avec succès', $license);
                return $license; // Retourner l'objet licence au lieu de true
            } else {
                $error = [
                    'error' => 'insert_failed',
                    'message' => $this->db->error(),
                    'sql_error' => $this->db->lastError()
                ];
                $this->debug('Échec de la création de licence', $error, 'error');
                return false;
            }
        } catch (\Exception $e) {
            $error = [
                'error' => 'exception',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
            $this->debug('Exception lors de la création de licence', $error, 'error');
            return false;
        }
    }

    /**
     * Récupère toutes les licences actives
     */
    public function getActiveLicenses() {
        return $this->db->select('licenses', ['status' => 'active']);
    }

    /**
     * Récupère une licence par son ID
     */
    public function getLicense($id) {
        try {
            // Si l'ID est un tableau, extraire l'ID réel
            $licenseId = is_array($id) ? $id['id'] : $id;
            
            $this->debug('Récupération de la licence', ['id' => $licenseId]);

            $stmt = $this->db->prepare("SELECT * FROM licenses WHERE id = ?");
            $stmt->execute([$licenseId]);
            $license = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($license) {
                $this->debug('Licence trouvée', $license);
                return $license;
            } else {
                $this->debug('Licence non trouvée', ['id' => $licenseId], 'warning');
                return null;
            }
        } catch (\Exception $e) {
            $error = [
                'error' => 'exception',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
            $this->debug('Exception lors de la récupération de la licence', $error, 'error');
            return null;
        }
    }

    /**
     * Met à jour une licence existante
     */
    public function updateLicense($id, array $data) {
        try {
            $this->debug('Tentative de mise à jour de la licence', ['id' => $id, 'data' => $data]);
            
            $license = [
                'domain_limit' => (int)$data['domain_limit'],
                'installation_limit' => (int)$data['installation_limit'],
                'allowed_domains' => $data['allowed_domains'],
                'allowed_ips' => $data['allowed_ips'],
                'status' => $data['status'],
                'expires_at' => $data['expires_at'] ?? null,
                'update_permissions' => isset($data['update_permissions']) ? (int)$data['update_permissions'] : 0,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Préparer la requête de mise à jour
            $setParts = [];
            foreach ($license as $key => $value) {
                $setParts[] = "{$key} = :{$key}";
            }
            $setClause = implode(', ', $setParts);
            $sql = "UPDATE licenses SET {$setClause} WHERE id = :id";

            $license['id'] = $id; // Ajouter l'ID pour la condition WHERE

            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($license);

            if ($result) {
                $this->debug('Licence mise à jour avec succès', ['id' => $id, 'license' => $license]);
            } else {
                $error = [
                    'error' => 'update_failed',
                    'message' => 'Erreur lors de la mise à jour',
                    'sql_error' => 'Échec de l\'exécution de la requête'
                ];
                $this->debug('Échec de la mise à jour de la licence', $error, 'error');
            }
            
            return $result;
        } catch (\Exception $e) {
            $error = [
                'error' => 'exception',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
            $this->debug('Exception lors de la mise à jour de la licence', $error, 'error');
            return false;
        }
    }

    /**
     * Supprime une licence
     * 
     * @param int $id ID de la licence à supprimer
     * @return bool True si la suppression a réussi, False sinon
     */
    public function deleteLicense($id) {
        try {
            $this->debug('Tentative de suppression de la licence', ['id' => $id]);
            
            // Vérifier si la licence existe
            $license = $this->getLicense($id);
            if (!$license) {
                $this->debug('Licence non trouvée', ['id' => $id], 'warning');
                return false;
            }

            // Supprimer la licence
            $sql = "DELETE FROM licenses WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute(['id' => $id]);

            if ($result && $stmt->rowCount() > 0) {
                $this->debug('Licence supprimée avec succès', ['id' => $id]);
                return true;
            } else {
                $error = [
                    'error' => 'delete_failed',
                    'message' => 'Erreur lors de la suppression',
                    'sql_error' => 'Aucune ligne affectée ou échec de l\'exécution'
                ];
                $this->debug('Échec de la suppression de la licence', $error, 'error');
                return false;
            }
        } catch (\Exception $e) {
            $error = [
                'error' => 'exception',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
            $this->debug('Exception lors de la suppression de la licence', $error, 'error');
            return false;
        }
    }

    /**
     * Vérifie si une installation est valide
     */
    public function validateInstallation($licenseKey, $domain, $ip) {
        $license = $this->db->fetchOne('licenses', ['license_key' => $licenseKey]);
        
        if (!$license || $license['status'] !== 'active') {
            return ['valid' => false, 'message' => 'Licence invalide ou inactive'];
        }

        // Vérification du domaine
        if (!empty($license['allowed_domains']) && !in_array($domain, explode(",", $license['allowed_domains']))) {
            return ['valid' => false, 'message' => 'Domaine non autorisé'];
        }

        // Vérification de l'IP
        if (!empty($license['allowed_ips']) && !in_array($ip, explode(",", $license['allowed_ips']))) {
            return ['valid' => false, 'message' => 'IP non autorisée'];
        }

        // Vérification du nombre d'installations
        $installations = $this->db->count('installations', ['license_id' => $license['id']]);
        if ($installations >= $license['installation_limit']) {
            return ['valid' => false, 'message' => 'Limite d\'installations atteinte'];
        }

        return [
            'valid' => true, 
            'license' => [
                'license_key' => $license['license_key'],
                'ably_key' => $license['ably_key'],
                'domain_limit' => $license['domain_limit'],
                'installation_limit' => $license['installation_limit']
            ]
        ];
    }

    /**
     * Vérifie la validité d'une licence
     */
    public function verifyLicense($licenseKey, $domain) {
        if (empty($licenseKey) || empty($domain)) {
            return [
                'success' => false,
                'message' => 'Clé de licence et domaine requis'
            ];
        }

        $license = $this->db->fetchOne(
            "SELECT * FROM licenses WHERE license_key = ? AND status = 'active'",
            [$licenseKey]
        );

        if (!$license) {
            return [
                'success' => false,
                'message' => 'Licence invalide ou inactive'
            ];
        }

        // Vérification du domaine avec gestion des sous-domaines
        $allowedDomains = explode(',', $license['allowed_domains']);
        $domainAllowed = false;
        
        foreach ($allowedDomains as $allowedDomain) {
            if ($this->isDomainAllowed($domain, $allowedDomain)) {
                $domainAllowed = true;
                break;
            }
        }
        
        if (!$domainAllowed) {
            return [
                'success' => false,
                'message' => 'Domaine non autorisé'
            ];
        }

        return [
            'success' => true,
            'message' => 'Licence valide',
            'ably_key' => $this->ablyKey,
            'domain_limit' => (int)$license['domain_limit'],
            'installation_limit' => (int)$license['installation_limit']
        ];
    }

    /**
     * Récupère le statut d'une licence
     */
    public function getLicenseStatus($licenseKey) {
        if (empty($licenseKey)) {
            return [
                'success' => false,
                'message' => 'Clé de licence requise'
            ];
        }

        $license = $this->db->fetchOne(
            "SELECT * FROM licenses WHERE license_key = ?",
            [$licenseKey]
        );

        if (!$license) {
            return [
                'success' => false,
                'message' => 'Licence introuvable'
            ];
        }

        return [
            'success' => true,
            'status' => $license['status'],
            'domain_limit' => (int)$license['domain_limit'],
            'installation_limit' => (int)$license['installation_limit'],
            'allowed_domains' => explode(',', $license['allowed_domains']),
            'allowed_ips' => !empty($license['allowed_ips']) ? explode(',', $license['allowed_ips']) : []
        ];
    }

    /**
     * Récupère les licences d'un client
     */
    public function getClientLicenses($email) {
        try {
            return $this->db->select(
                'licenses', 
                ['client_email' => $email],
                'ORDER BY created_at DESC'
            );
        } catch (\Exception $e) {
            $this->debug('Erreur lors de la récupération des licences client', [
                'email' => $email,
                'error' => $e->getMessage()
            ], 'error');
            return [];
        }
    }

    /**
     * Vérifie si un domaine correspond à un domaine autorisé
     * Gère aussi les sous-domaines
     */
    private function isDomainAllowed($domain, $allowedDomain) {
        // Nettoyage des domaines
        $domain = strtolower(trim($domain));
        $allowedDomain = strtolower(trim($allowedDomain));
        
        // Si les domaines sont identiques
        if ($domain === $allowedDomain) {
            return true;
        }
        
        // Vérifie si c'est un sous-domaine
        if (substr($domain, -strlen($allowedDomain) - 1) === '.' . $allowedDomain) {
            return true;
        }
        
        return false;
    }

    /**
     * Génère une clé de licence unique
     */
    private function generateLicenseKey() {
        return bin2hex(random_bytes(16));
    }

    private function debug($message, $data = [], $level = 'info') {
        $context = [
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        switch ($level) {
            case 'error':
                Logger::channel('app')->error("LicenseManager: " . $message, $context);
                break;
            case 'warning':
                Logger::channel('app')->warning("LicenseManager: " . $message, $context);
                break;
            default:
                Logger::channel('app')->info("LicenseManager: " . $message, $context);
                break;
        }
    }
}
