# Structure du Projet TechCMS Commercial

## Vue d'ensemble
TechCMS Commercial est un écosystème complet avec 3 applications intégrées :
- **Admin** : Interface d'administration pour la gestion des templates, licences et système
- **Website** : Vitrine publique pour présenter TechCMS et ses fonctionnalités
- **Client** : Application unifiée avec boutique publique + espace client privé (architecture WHMCS)

**Architecture Unifiée Client :**
- **Mode Public** : Boutique accessible sans connexion (/shop, /cart, /checkout)
- **Mode Privé** : Espace client après authentification (/dashboard, /licenses, /updates)
- **Transition Fluide** : Navigation seamless entre modes public/privé avec panier persistant

Architecture moderne basée sur Vue.js 3 + TypeScript, API REST PHP 8.1+ et MySQL.

## Structure des Dossiers

```
/
├── admin/                          # Interface d'administration (Vue.js 3 + TypeScript)
│   ├── src/
│   │   ├── components/            # Composants Vue.js réutilisables
│   │   │   ├── common/           # Composants communs (ConfirmModal, etc.)
│   │   │   ├── layout/           # Composants de layout (Header, Sidebar)
│   │   │   └── template/         # Composants spécifiques aux templates
│   │   │       └── TemplateFormModal.vue # Modal création/édition templates
│   │   ├── views/                 # Vues/Pages de l'interface admin
│   │   │   ├── dashboard/        # Tableau de bord admin
│   │   │   ├── license/          # Gestion des licences
│   │   │   ├── template/         # Gestion des templates de licences
│   │   │   │   └── TemplatesView.vue # Vue principale templates
│   │   │   └── system/           # Administration système
│   │   ├── stores/                # Stores Pinia pour la gestion d'état
│   │   │   ├── auth.ts           # Authentification admin
│   │   │   └── templates.ts      # Gestion des templates
│   │   └── types/                # Types TypeScript
│   │       └── template.ts       # Types pour les templates
│   └── dist/                      # Build de production
├── website/                        # Vitrine publique (Vue.js 3 + TypeScript + i18n)
│   ├── src/
│   │   ├── components/
│   │   │   ├── common/           # Composants communs
│   │   │   │   ├── LanguageSelector.vue # Sélecteur de langue avec dropdown
│   │   │   │   └── PaginationComponent.vue # Pagination traduite
│   │   │   └── layout/           # Layout vitrine
│   │   │       ├── WebsiteHeader.vue # Header avec navigation + sélecteur langue
│   │   │       └── WebsiteFooter.vue # Footer avec newsletter traduit
│   │   ├── views/                # Pages vitrine (entièrement traduites)
│   │   │   ├── home/             # Page d'accueil
│   │   │   │   └── HomeView.vue  # Hero + fonctionnalités + templates
│   │   │   ├── features/         # Page fonctionnalités
│   │   │   │   └── FeaturesView.vue # Comparaison + spécifications
│   │   │   ├── pricing/          # Page tarifs
│   │   │   │   └── PricingView.vue # Affichage dynamique templates
│   │   │   ├── contact/          # Page contact
│   │   │   │   └── ContactView.vue # Formulaire + validation
│   │   │   └── about/            # Page à propos
│   │   │       └── AboutView.vue # Histoire + équipe + valeurs
│   │   ├── stores/               # Stores Pinia
│   │   │   └── language.ts       # Store langue avec détection navigateur
│   │   ├── i18n/                 # Système de traduction
│   │   │   ├── index.ts          # Configuration vue-i18n + détection auto
│   │   │   ├── fr.json           # Traductions françaises (défaut)
│   │   │   └── en.json           # Traductions anglaises
│   │   └── types/                # Types TypeScript vitrine
│   │       └── template.ts       # Types templates publics
│   └── dist/                     # Build vitrine

├── client/                         # Application unifiée (Vue.js 3 + TypeScript) - Architecture WHMCS
│   ├── src/
│   │   ├── components/layout/    # Layouts adaptatifs
│   │   │   ├── AppLayout.vue     # Layout espace privé (authentifié)
│   │   │   └── PublicLayout.vue  # Layout public (boutique, panier)
│   │   ├── views/
│   │   │   ├── shop/             # Boutique publique + panier
│   │   │   │   ├── ShopView.vue  # Catalogue produits public
│   │   │   │   ├── CartView.vue  # Panier avec résumé commande
│   │   │   │   └── CheckoutView.vue # Finalisation commande
│   │   │   ├── auth/             # Authentification
│   │   │   ├── license/          # Gestion licences client (privé)
│   │   │   └── updates/          # Téléchargement mises à jour (privé)
│   │   │       └── UpdatesView.vue # Interface téléchargement complète
│   │   ├── stores/               # Stores unifiés
│   │   │   ├── auth.ts           # Authentification + transition public/privé
│   │   │   ├── cart.ts           # Panier persistant + calculs TVA
│   │   │   └── updates.ts        # Gestion téléchargements + progression
│   │   └── types/                # Types client
│   │       └── updates.ts        # Types versions CMS + téléchargements
│   └── dist/                     # Build client
├── api/                           # API REST v1 modulaire
│   └── v1/
│       ├── controllers/           # Contrôleurs API organisés par domaine
│       │   ├── admin/            # Contrôleurs administration
│       │   │   ├── TemplateController.php # CRUD templates de licences
│       │   │   ├── system/       # Contrôleurs système admin
│       │   │   │   ├── AdminVersionController.php    # Gestion versions CMS
│       │   │   │   └── AdminUpdateController.php     # Gestion mises à jour
│       │   │   └── license/      # Contrôleurs licences admin
│       │   │       └── AdminLicenseUpdateController.php # Permissions update
│       │   ├── client/           # Contrôleurs client + boutique unifiée
│       │   │   └── UpdatesController.php # Téléchargements sécurisés
│       │   ├── website/          # Contrôleurs vitrine publique
│       │   │   ├── TemplateController.php # Templates publics + featured
│       │   │   ├── ContactController.php  # Formulaire contact + anti-spam
│       │   │   ├── WebsiteOrderController.php # 🆕 Gestion commandes
│       │   │   └── WebsitePaymentWebhookController.php # 🆕 Webhooks paiement
│       │   └── UpdateApiController.php # API publique mises à jour
│       ├── middleware/           # Middlewares d'authentification et validation
│       ├── models/              # Modèles API spécifiques
│       └── routes/              # Routes modulaires
│           ├── admin/           # Routes administration
│           │   └── admin_routes.php # Routes admin avec templates
│           ├── client/          # Routes client
│           │   ├── client_routes.php # Routes principales client
│           │   └── updates/     # Routes mises à jour
│           │       └── updates_routes.php # Routes téléchargements
│           └── website/         # Routes vitrine
│               ├── website_routes.php # Routes publiques + commandes
│               └── webhook_routes.php # 🆕 Routes webhooks paiement

├── config/                      # Configuration
│   └── config.php              # Configuration principale
├── includes/                    # Code PHP backend
│   ├── common/                 # Code partagé
│   │   ├── core/              # Classes core (Database, Router, etc.)
│   │   │   ├── LicenseProvisioningService.php # 🆕 Service provisionnement automatique
│   │   │   └── EmailNotificationService.php   # 🆕 Service notifications email
│   │   ├── models/            # Modèles de base de données
│   │   │   ├── BaseModel.php          # Modèle de base avec CRUD
│   │   │   ├── LicenseModel.php       # Gestion des licences (avec permissions update)
│   │   │   ├── LicenseTemplateModel.php # Gestion templates licences (nouveau)
│   │   │   ├── OrderModel.php         # 🆕 Gestion des commandes
│   │   │   ├── OrderItemModel.php     # 🆕 Gestion des items de commande
│   │   │   ├── CmsVersionModel.php    # Gestion des versions CMS (nouveau)
│   │   │   ├── InstallationUpdateModel.php # Suivi des mises à jour (nouveau)
│   │   │   ├── UpdateDownloadModel.php # Téléchargements sécurisés (nouveau)
│   │   │   ├── ClientModel.php        # Gestion des clients
│   │   │   ├── InvoiceModel.php       # Gestion de la facturation
│   │   │   ├── TicketModel.php        # Gestion du support
│   │   │   └── ...                    # Autres modèles existants
│   │   ├── services/          # Services métier (nouveau)
│   │   │   ├── UpdateDownloadService.php # Gestion sécurisée des téléchargements
│   │   │   └── UpdateFileService.php     # Gestion des fichiers de mise à jour
│   │   ├── helpers/           # Fonctions utilitaires
│   │   └── middleware/        # Middlewares partagés
│   ├── admin/                 # Code spécifique admin
│   ├── client/                # Code spécifique client
│   └── provisioning/          # Système de provisioning
├── install/                    # Installation et migration
│   ├── schema.sql             # Structure de base de données
│   └── installer.php          # Script d'installation
├── modules/                    # Modules système
│   ├── gateways/              # Passerelles de paiement
│   ├── servers/               # Modules serveurs
│   ├── notifications/         # Système de notifications
│   └── support/               # Module support
├── storage/                    # Stockage de fichiers
│   ├── logs/                  # Logs système
│   ├── cms-updates/           # Fichiers de mise à jour CMS
│   └── product-images/        # Images produits
└── vendor/                     # Dépendances Composer
```

## Architecture de Base de Données

### Tables Principales
- `clients` - Gestion des clients
- `products` - Catalogue produits
- `services` - Services clients
- `invoices` - Facturation
- `licenses` - Système de licences (avec permissions de mise à jour)
- `tickets` - Support client

### Nouvelles Tables (Système de Mise à Jour)
- `cms_versions` - Versions CMS disponibles
- `installation_updates` - Suivi des mises à jour d'installations
- `update_downloads` - Gestion sécurisée des téléchargements

## Architecture API

### Structure Modulaire
- **Admin API** : `/api/v1/admin/*` - Gestion administrative
- **Client API** : `/api/v1/client/*` - Interface client
- **Public API** : `/api/v1/license/*` - Vérification licences (sans auth)
- **Update API** : `/api/v1/updates/*` - Système de mise à jour automatique (nouveau)
  - `GET/POST /updates/check` - Vérification des mises à jour disponibles
  - `GET /updates/download/{token}` - Téléchargement sécurisé avec tokens
  - `POST /updates/status` - Rapport de statut d'installation

### Authentification
- JWT pour l'authentification admin
- Middleware de validation des tokens
- Système de permissions granulaires

## Technologies Utilisées

### Backend
- **PHP 7.4+** - Langage principal
- **MySQL 8.0** - Base de données
- **Composer** - Gestionnaire de dépendances PHP

### Frontend
- **Vue.js 3** - Framework JavaScript
- **Pinia** - Gestion d'état
- **Vite** - Build tool

### Outils de Développement
- **PHPStan** - Analyse statique PHP
- **ESLint** - Linting JavaScript
- **Monolog** - Système de logs

## Patterns et Standards

### Modèles
- Héritage de `BaseModel` pour les opérations CRUD
- Propriétés `$fillable` pour la sécurité
- Timestamps automatiques
- Validation des données

### Contrôleurs
- Héritage de `ApiBaseController`
- Réponses JSON standardisées
- Gestion d'erreurs centralisée
- Middleware d'authentification

### Routage
- Routes modulaires par domaine
- Groupes avec middleware
- Préfixes et namespaces organisés

## Sécurité

### Authentification
- Tokens JWT avec expiration
- Refresh tokens
- Validation des permissions

### Validation
- Validation des entrées utilisateur
- Protection CSRF
- Sanitisation des données

### Licences
- Vérification des domaines autorisés
- Limitation des installations
- Contrôle des permissions de mise à jour

## Nouvelles Fonctionnalités (2025-07-09)

### Gestion des templates de licences
- Création et gestion des templates de produits/licences
- Configuration des prix et cycles de facturation (mensuel, annuel, etc.)
- Gestion des fonctionnalités et limites par template
- Interface admin complète avec CRUD et pagination
- API publique pour affichage vitrine et boutique

### Écosystème multi-applications
- **Website** : Vitrine publique avec pages dynamiques (accueil, fonctionnalités, tarifs, contact, à propos)
- **Client** : Application unifiée WHMCS avec boutique publique + espace client privé, panier intelligent et téléchargements sécurisés
- **Admin** : Interface d'administration centralisée avec gestion templates et licences

### Système de mises à jour amélioré
- Téléchargements avec tokens temporaires et authentification renforcée
- Interface client avec progression temps réel (XMLHttpRequest)
- Protection anti-téléchargement non autorisé
- Gestion des canaux par licence (Dev, Beta, Stable, Obsolète)

### Vitrine et e-commerce
- Pages vitrine dynamiques avec données API temps réel
- Formulaire de contact avec protection anti-spam
- Affichage des templates avec filtres et tri
- Panier e-commerce avec localStorage et calculs automatiques
- Design responsive moderne avec animations

### Architecture technique
- Vue.js 3 + TypeScript pour toutes les applications
- API REST modulaire avec routes séparées par domaine
- Stores Pinia centralisés avec gestion d'erreurs
- Design system cohérent avec variables CSS
- Protection anti-spam et validation côté serveur

---

*Dernière mise à jour : 2025-07-09*
