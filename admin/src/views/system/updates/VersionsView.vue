<template>
  <div id="versionsView" class="view-container">
    <!-- En-tête avec titre et bouton d'ajout -->
    <div class="header-box">
      <div>
        <h1 class="page-title">{{ t('updates.versions.title') }}</h1>
        <span class="page-description">{{ t('updates.versions.description') }}</span>
      </div>
      <button class="btn btn-gradient" @click="handleAddVersion">
        <i class="fas fa-plus"></i>
        {{ t('updates.versions.add_new') }}
      </button>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-code-branch"></i>
        </div>
        <div class="stat-number">{{ versionsStore.stats?.total_versions || 0 }}</div>
        <div class="stat-label">{{ t('updates.versions.stats.total') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ versionsStore.stats?.stable_versions || 0 }}</div>
        <div class="stat-label">{{ t('updates.versions.stats.stable') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-flask"></i>
        </div>
        <div class="stat-number">{{ versionsStore.stats?.beta_versions || 0 }}</div>
        <div class="stat-label">{{ t('updates.versions.stats.beta') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-file-archive"></i>
        </div>
        <div class="stat-number">{{ versionsStore.stats?.total_files || 0 }}</div>
        <div class="stat-label">{{ t('updates.versions.stats.with_files') }}</div>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="filter-box">
      <div class="filter-grid">
        <div class="filter-group">
          <label class="filter-label">{{ t('updates.versions.filters.search') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-search"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              class="filter-input" 
              :placeholder="t('updates.versions.filters.search_placeholder')"
              @input="debouncedSearch"
            >
          </div>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">{{ t('updates.versions.filters.status') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-filter"></i>
            <select v-model="statusFilter" class="filter-input" @change="applyFilters">
              <option value="">{{ t('updates.versions.status.all') }}</option>
              <option value="dev">{{ t('updates.versions.status.dev') }}</option>
              <option value="beta">{{ t('updates.versions.status.beta') }}</option>
              <option value="stable">{{ t('updates.versions.status.stable') }}</option>
              <option value="obsolete">{{ t('updates.versions.status.obsolete') }}</option>
            </select>
          </div>
        </div>

        <div class="filter-group">
          <label class="filter-label">{{ t('updates.versions.filters.file_status') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-file"></i>
            <select v-model="fileFilter" class="filter-input" @change="applyFilters">
              <option value="">{{ t('updates.versions.filters.all_files') }}</option>
              <option value="with_file">{{ t('updates.versions.filters.with_file') }}</option>
              <option value="without_file">{{ t('updates.versions.filters.without_file') }}</option>
            </select>
          </div>
        </div>

        <div class="filter-actions">
          <button class="btn btn-outline btn-sm" @click="resetFilters">
            <i class="fas fa-undo"></i>
            {{ t('common.reset') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Liste des versions -->
    <div v-if="versionsStore.loading" class="loading-state box">
      <div class="spinner"></div>
      <p>{{ t('common.loading') }}</p>
    </div>

    <div v-else-if="versionsStore.error" class="error-state box">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3>{{ t('common.error') }}</h3>
      <p>{{ versionsStore.error }}</p>
      <button class="btn btn-primary" @click="loadVersions">
        {{ t('common.retry') }}
      </button>
    </div>

    <div v-else-if="filteredVersions.length === 0" class="empty-state box">
      <div class="empty-icon">
        <i class="fas fa-code-branch"></i>
      </div>
      <h3>{{ t('updates.versions.empty.title') }}</h3>
      <p>{{ t('updates.versions.empty.description') }}</p>
      <button class="btn btn-primary" @click="handleAddVersion">
        <i class="fas fa-plus"></i>
        {{ t('updates.versions.add_new') }}
      </button>
    </div>

    <div v-else class="content-box">
      <div class="versions-grid">
        <div 
          v-for="version in filteredVersions" 
          :key="version.id"
          class="version-card card-box"
          @click="viewVersionDetail(version.id)"
        >
          <div class="version-header">
            <div class="version-number">
              <i class="fas fa-code-branch"></i>
              <span>{{ version.version }}</span>
            </div>
            <div class="version-status">
              <span 
                class="status-badge"
                :class="`status-${version.status}`"
              >
                {{ t(`updates.versions.status.${version.status}`) }}
              </span>
            </div>
          </div>

          <div class="version-info">
            <div v-if="version.changelog" class="version-description">
              {{ version.changelog }}
            </div>
            <div class="version-meta">
              <div class="meta-item">
                <i class="fas fa-calendar"></i>
                <span>{{ formatDate(version.release_date) }}</span>
              </div>
              <div v-if="version.file_exists" class="meta-item">
                <i class="fas fa-file-archive text-success"></i>
                <span>{{ t('updates.versions.file_available') }}</span>
              </div>
              <div v-else class="meta-item">
                <i class="fas fa-file-times text-warning"></i>
                <span>{{ t('updates.versions.file_missing') }}</span>
              </div>
            </div>
          </div>

          <div class="version-stats">
            <div class="stat-item">
              <i class="fas fa-download"></i>
              <span>{{ version.file_size ? Math.floor(version.file_size / 1024) + 'KB' : '0' }} {{ t('updates.versions.file_size') }}</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-clock"></i>
              <span>{{ formatRelativeTime(version.created_at) }}</span>
            </div>
          </div>

          <div class="version-actions">
            <button 
              class="btn btn-sm btn-outline"
              :title="t('updates.versions.actions.edit')"
              @click.stop="editVersion(version)"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              class="btn btn-sm btn-primary"
              :title="t('updates.versions.actions.upload')"
              @click.stop="uploadFile(version)"
            >
              <i class="fas fa-upload"></i>
            </button>
            <button 
              class="btn btn-sm btn-danger"
              :title="t('updates.versions.actions.delete')"
              @click.stop="confirmDelete(version)"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination-box">
        <div class="pagination">
          <button 
            class="pagination-btn" 
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <span class="pagination-info">
            {{ t('common.pagination.page') }} {{ currentPage }} {{ t('common.pagination.of') }} {{ totalPages }}
          </span>
          
          <button 
            class="pagination-btn" 
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de création/édition -->
    <VersionFormModal
      v-if="showCreateModal || showEditModal"
      :show="showCreateModal || showEditModal"
      :version="editingVersion"
      @close="closeModals"
      @saved="onVersionSaved"
    />

    <!-- Modal d'upload de fichier -->
    <FileUploadModal
      v-if="showUploadModal"
      :show="showUploadModal"
      :version="uploadingVersion"
      @close="closeUploadModal"
      @uploaded="onFileUploaded"
    />

    <!-- Modal de confirmation de suppression -->
    <ConfirmModal
      v-if="showDeleteModal"
      :show="showDeleteModal"
      :title="t('updates.versions.delete.title')"
      :message="t('updates.versions.delete.message', { version: deletingVersion?.version })"
      @confirm="deleteVersion"
      @cancel="showDeleteModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useVersionsStore, type CmsVersion } from '@/stores/versions'
import { useNotificationStore } from '@/stores/notifications'
import logger from '@/services/logger'

// Composants
import VersionFormModal from '@/components/updates/VersionFormModal.vue'
import FileUploadModal from '@/components/updates/FileUploadModal.vue'
import ConfirmModal from '@/components/common/ConfirmModal.vue'

// Traduction et navigation
const { t } = useI18n()
const router = useRouter()

// Stores
const versionsStore = useVersionsStore()
const notificationStore = useNotificationStore()

// État local
const searchQuery = ref('')
const statusFilter = ref('')
const fileFilter = ref('')
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showUploadModal = ref(false)
const showDeleteModal = ref(false)
const editingVersion = ref<CmsVersion | null>(null)
const uploadingVersion = ref<CmsVersion | null>(null)
const deletingVersion = ref<CmsVersion | null>(null)

// Computed
const filteredVersions = computed(() => {
  let versions = versionsStore.versions || []
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    versions = versions.filter(version =>
      version.version.toLowerCase().includes(query) ||
      version.changelog?.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value) {
    versions = versions.filter(version => version.status === statusFilter.value)
  }
  
  if (fileFilter.value === 'with_file') {
    versions = versions.filter(version => version.file_exists)
  } else if (fileFilter.value === 'without_file') {
    versions = versions.filter(version => !version.file_exists)
  }
  
  return versions
})

const currentPage = computed(() => versionsStore.currentPage || 1)
const totalPages = computed(() => versionsStore.totalPages || 1)

// Méthodes
const loadVersions = async () => {
  await Promise.all([
    versionsStore.fetchVersions(),
    versionsStore.fetchStats()
  ])
}

const handleAddVersion = () => {
  editingVersion.value = null
  showCreateModal.value = true
}

const editVersion = (version: CmsVersion) => {
  editingVersion.value = version
  showEditModal.value = true
}

const viewVersionDetail = (versionId: number) => {
  router.push(`/system/updates/versions/${versionId}`)
}

const uploadFile = (version: CmsVersion) => {
  uploadingVersion.value = version
  showUploadModal.value = true
}

const confirmDelete = (version: CmsVersion) => {
  deletingVersion.value = version
  showDeleteModal.value = true
}

const deleteVersion = async () => {
  if (deletingVersion.value) {
    try {
      await versionsStore.deleteVersion(deletingVersion.value.id)
      showDeleteModal.value = false
      deletingVersion.value = null
    } catch {
      // L'erreur est gérée par le store
    }
  }
}

const closeModals = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingVersion.value = null
}

const closeUploadModal = () => {
  showUploadModal.value = false
  uploadingVersion.value = null
}

const onVersionSaved = async (versionData: any) => {
  try {
    if (editingVersion.value) {
      // Mode édition
      await versionsStore.updateVersion(editingVersion.value.id, versionData)
      notificationStore.addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('updates.versions.update_success')
      })
    } else {
      // Mode création
      await versionsStore.createVersion(versionData)
      notificationStore.addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('updates.versions.create_success')
      })
    }

    closeModals()
    await versionsStore.fetchVersions(currentPage.value)
    await versionsStore.fetchStats()
  } catch (error) {
    logger.error('[VERSIONS VIEW] Erreur lors de la sauvegarde', { error })
    notificationStore.addNotification({
      type: 'error',
      title: t('common.error'),
      message: t('updates.versions.save_error')
    })
  }
}

const onFileUploaded = () => {
  closeUploadModal()
  versionsStore.fetchVersions(currentPage.value)
}

// Fonction debounce native
const debounce = (func: (...args: any[]) => void, wait: number) => {
  let timeout: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }
}

const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

const applyFilters = () => {
  // Appliquer les filtres localement pour l'instant
  versionsStore.fetchVersions(1)
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  fileFilter.value = ''
  versionsStore.fetchVersions(1)
}

const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    versionsStore.fetchVersions(page)
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

const formatRelativeTime = (date: string) => {
  const now = new Date()
  const versionDate = new Date(date)
  const diffMs = now.getTime() - versionDate.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return t('updates.versions.today')
  } else if (diffDays === 1) {
    return t('updates.versions.yesterday')
  } else if (diffDays < 30) {
    return t('updates.versions.days_ago', { days: diffDays })
  } else {
    return formatDate(date)
  }
}

// Lifecycle
onMounted(async () => {
  await loadVersions()
})
</script>

<style scoped>
/* Fix pour les icônes des cartes statistiques */
.stat-card .stat-icon {
  font-size: 2rem !important;
  color: #06f !important;
  margin-bottom: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 60px !important;
  background: rgb(0 102 255 / 10%) !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
}

.stat-card .stat-icon i {
  color: #06f !important;
  font-size: 1.5rem !important;
  display: block !important;
  line-height: 1 !important;
}

.stat-card .stat-icon i::before {
  color: #06f !important;
}

/* Fallback pour FontAwesome si non chargé */
.stat-card .stat-icon i.fa-code-branch::before {
  content: "🌿" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-check-circle::before {
  content: "✅" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-flask::before {
  content: "🧪" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-file-archive::before {
  content: "📦" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.versions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.version-card {
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgb(255 255 255 / 8%);
  background: rgb(16 20 35 / 30%);
  backdrop-filter: blur(20px);
}

.version-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgb(0 102 255 / 15%);
  border-color: rgb(0 102 255 / 30%);
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgb(255 255 255 / 8%);
}

.version-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
}

.version-number i {
  color: var(--primary-blue);
}

.version-info {
  margin-bottom: 1rem;
}

.version-description {
  color: rgb(255 255 255 / 80%);
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.version-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 70%);
}

.meta-item i {
  width: 1rem;
  color: var(--accent-blue);
}

.version-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: rgb(255 255 255 / 3%);
  border-radius: 6px;
  border: 1px solid rgb(255 255 255 / 5%);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: rgb(255 255 255 / 70%);
}

.version-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.version-actions .btn {
  padding: 0.5rem;
  min-width: 2.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-dev {
  background: linear-gradient(135deg, rgb(156 163 175 / 20%), rgb(156 163 175 / 10%));
  color: #9ca3af;
  border: 1px solid rgb(156 163 175 / 30%);
}

.status-beta {
  background: linear-gradient(135deg, rgb(245 158 11 / 20%), rgb(245 158 11 / 10%));
  color: #f59e0b;
  border: 1px solid rgb(245 158 11 / 30%);
}

.status-stable {
  background: linear-gradient(135deg, rgb(0 184 125 / 20%), rgb(0 184 125 / 10%));
  color: #00b87d;
  border: 1px solid rgb(0 184 125 / 30%);
}

.status-obsolete {
  background: linear-gradient(135deg, rgb(239 68 68 / 20%), rgb(239 68 68 / 10%));
  color: #ef4444;
  border: 1px solid rgb(239 68 68 / 30%);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
}

.pagination-box {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgb(16 20 35 / 30%);
  border-radius: 12px;
  border: 1px solid rgb(255 255 255 / 8%);
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: rgb(0 102 255 / 20%);
  border-color: rgb(0 102 255 / 40%);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: rgb(255 255 255 / 70%);
  font-size: 0.9rem;
}

@media (width <= 768px) {
  .versions-grid {
    grid-template-columns: 1fr;
  }

  .version-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
