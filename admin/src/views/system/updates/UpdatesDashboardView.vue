<template>
  <div id="updatesDashboard" class="view-container">
    <!-- En-tête avec titre et actions -->
    <div class="header-box">
      <div>
        <h1 class="page-title">{{ t('updates.dashboard.title') }}</h1>
        <span class="page-description">{{ t('updates.dashboard.description') }}</span>
      </div>
      <div class="header-actions">
        <select v-model="selectedPeriod" class="filter-input" @change="onPeriodChange">
          <option value="7">{{ t('updates.dashboard.period_7_days') }}</option>
          <option value="30">{{ t('updates.dashboard.period_30_days') }}</option>
          <option value="90">{{ t('updates.dashboard.period_90_days') }}</option>
        </select>
        <button class="btn btn-gradient" :disabled="loading" @click="refreshData">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          {{ t('common.refresh') }}
        </button>
      </div>
    </div>

    <!-- Statistiques principales -->
    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-download"></i>
        </div>
        <div class="stat-number">{{ updateStats?.total_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.total_updates') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ updateStats?.completed_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.completed_updates') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-number">{{ updateStats?.active_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.active_updates') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stat-number">{{ updateStats?.failed_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.failed_updates') }}</div>
      </div>
    </div>

    <!-- Statistiques secondaires -->
    <div class="secondary-stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-file-archive"></i>
        </div>
        <div class="stat-number">{{ downloadStats?.total_tokens || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.total_downloads') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-key"></i>
        </div>
        <div class="stat-number">{{ downloadStats?.unique_licenses || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.unique_licenses') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-code-branch"></i>
        </div>
        <div class="stat-number">{{ downloadStats?.unique_versions || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.unique_versions') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <div class="stat-number">{{ permissionStats?.licenses_with_update_permission || 0 }}</div>
        <div class="stat-label">{{ t('updates.dashboard.stats.licenses_with_permission') }}</div>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="dashboard-grid box-grid">
      <!-- Activité récente -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-history"></i>
            {{ t('updates.dashboard.recent_activity') }}
          </h3>
          <router-link to="/system/updates/monitoring" class="btn btn-sm btn-outline">
            {{ t('common.view_all') }}
          </router-link>
        </div>
        <div class="card-body">
          <div v-if="loading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <div v-else-if="recentUpdates.length === 0" class="empty-state">
            <i class="fas fa-download"></i>
            <p>{{ t('updates.dashboard.no_recent_activity') }}</p>
          </div>
          <div v-else class="activity-list">
            <div 
              v-for="update in recentUpdates" 
              :key="update.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <i :class="getUpdateStatusIcon(update.status)"></i>
              </div>
              <div class="activity-content">
                <div class="activity-title">
                  {{ t('updates.dashboard.update_to') }} {{ update.target_version }}
                </div>
                <div class="activity-meta">
                  <span class="activity-domain">{{ update.domain || update.license_key }}</span>
                  <span class="activity-time">{{ formatRelativeTime(update.created_at) }}</span>
                </div>
              </div>
              <div class="activity-status">
                <span :class="getStatusBadgeClass(update.status)">
                  {{ t(`updates.status.${update.status}`) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Versions populaires -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ t('updates.dashboard.popular_versions') }}
          </h3>
          <router-link to="/system/updates/versions" class="btn btn-sm btn-outline">
            {{ t('common.manage') }}
          </router-link>
        </div>
        <div class="card-body">
          <div v-if="loading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <div v-else-if="popularVersions.length === 0" class="empty-state">
            <i class="fas fa-code-branch"></i>
            <p>{{ t('updates.dashboard.no_versions') }}</p>
          </div>
          <div v-else class="versions-list">
            <div 
              v-for="version in popularVersions" 
              :key="version.version"
              class="version-item"
            >
              <div class="version-info">
                <div class="version-number">{{ version.version }}</div>
                <div class="version-meta">
                  <span class="version-status" :class="`status-${version.status}`">
                    {{ t(`updates.versions.status.${version.status}`) }}
                  </span>
                  <span class="version-date">{{ formatDate(version.release_date) }}</span>
                </div>
              </div>
              <div class="version-stats">
                <div class="download-count">
                  <i class="fas fa-download"></i>
                  {{ version.file_size ? Math.floor(version.file_size / 1024) + 'KB' : 'N/A' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions rapides -->
    <div class="quick-actions-box">
      <h3 class="quick-actions-title">{{ t('updates.dashboard.quick_actions') }}</h3>
      <div class="quick-actions-grid">
        <router-link to="/system/updates/versions" class="quick-action-card">
          <div class="quick-action-icon">
            <i class="fas fa-code-branch"></i>
          </div>
          <div class="quick-action-content">
            <h4>{{ t('updates.dashboard.manage_versions') }}</h4>
            <p>{{ t('updates.dashboard.manage_versions_desc') }}</p>
          </div>
        </router-link>

        <router-link to="/system/updates/monitoring" class="quick-action-card">
          <div class="quick-action-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="quick-action-content">
            <h4>{{ t('updates.dashboard.monitor_updates') }}</h4>
            <p>{{ t('updates.dashboard.monitor_updates_desc') }}</p>
          </div>
        </router-link>

        <router-link to="/licenses" class="quick-action-card">
          <div class="quick-action-icon">
            <i class="fas fa-key"></i>
          </div>
          <div class="quick-action-content">
            <h4>{{ t('updates.dashboard.manage_permissions') }}</h4>
            <p>{{ t('updates.dashboard.manage_permissions_desc') }}</p>
          </div>
        </router-link>

        <button class="quick-action-card" :disabled="loading" @click="performCleanup">
          <div class="quick-action-icon">
            <i class="fas fa-broom"></i>
          </div>
          <div class="quick-action-content">
            <h4>{{ t('updates.dashboard.cleanup_data') }}</h4>
            <p>{{ t('updates.dashboard.cleanup_data_desc') }}</p>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUpdatesStore } from '@/stores/updates'
import { useVersionsStore } from '@/stores/versions'
import { useLicenseUpdatesStore } from '@/stores/license-updates'
import { useNotificationStore } from '@/stores/notifications'
import logger from '@/services/logger'

// Traduction
const { t } = useI18n()

// Stores
const updatesStore = useUpdatesStore()
const versionsStore = useVersionsStore()
const licenseUpdatesStore = useLicenseUpdatesStore()
const notificationStore = useNotificationStore()

// État local
const loading = ref(false)
const selectedPeriod = ref(30)
const autoRefreshInterval = ref<NodeJS.Timeout | null>(null)

// Computed
const updateStats = computed(() => updatesStore.stats?.updates)
const downloadStats = computed(() => updatesStore.stats?.downloads)
const permissionStats = computed(() => updatesStore.stats?.permissions)

const recentUpdates = computed(() => 
  updatesStore.updates.slice(0, 5)
)

const popularVersions = computed(() =>
  versionsStore.versions.slice(0, 5)
)

// Méthodes
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      updatesStore.fetchStats(selectedPeriod.value),
      updatesStore.fetchUpdates(1),
      versionsStore.fetchVersions(1),
      licenseUpdatesStore.fetchStats()
    ])
    logger.info('[UPDATES DASHBOARD] Données actualisées')
  } catch (error) {
    logger.error('[UPDATES DASHBOARD] Erreur lors de l\'actualisation', { error })
    notificationStore.addNotification({
      type: 'error',
      title: t('common.error'),
      message: t('updates.dashboard.error_loading')
    })
  } finally {
    loading.value = false
  }
}

const onPeriodChange = () => {
  refreshData()
}

const performCleanup = async () => {
  if (!confirm(t('updates.dashboard.confirm_cleanup'))) {
    return
  }

  loading.value = true
  try {
    await updatesStore.cleanup(90) // Nettoyer les données de plus de 90 jours
    notificationStore.addNotification({
      type: 'success',
      title: t('common.success'),
      message: t('updates.dashboard.cleanup_success')
    })
    await refreshData()
  } catch (error) {
    logger.error('[UPDATES DASHBOARD] Erreur lors du nettoyage', { error })
    notificationStore.addNotification({
      type: 'error',
      title: t('common.error'),
      message: t('updates.dashboard.cleanup_error')
    })
  } finally {
    loading.value = false
  }
}

const getUpdateStatusIcon = (status: string) => {
  const icons = {
    pending: 'fas fa-clock text-warning',
    downloading: 'fas fa-download text-info',
    installing: 'fas fa-cog fa-spin text-info',
    completed: 'fas fa-check-circle text-success',
    failed: 'fas fa-exclamation-triangle text-danger'
  }
  return icons[status as keyof typeof icons] || 'fas fa-question-circle'
}

const getStatusBadgeClass = (status: string) => {
  return updatesStore.getStatusBadgeClass(status)
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

const formatRelativeTime = (date: string) => {
  const now = new Date()
  const updateDate = new Date(date)
  const diffMs = now.getTime() - updateDate.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 60) {
    return t('updates.dashboard.time_ago_minutes', { minutes: diffMins })
  } else if (diffHours < 24) {
    return t('updates.dashboard.time_ago_hours', { hours: diffHours })
  } else {
    return t('updates.dashboard.time_ago_days', { days: diffDays })
  }
}

// Lifecycle
onMounted(async () => {
  await refreshData()

  // Auto-refresh toutes les 30 secondes
  autoRefreshInterval.value = setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
  }
})
</script>

<style scoped>
/* Fix pour les icônes des cartes statistiques */
.stat-card .stat-icon {
  font-size: 2rem !important;
  color: #06f !important;
  margin-bottom: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 60px !important;
  background: rgb(0 102 255 / 10%) !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
}

.stat-card .stat-icon i {
  color: #06f !important;
  font-size: 1.5rem !important;
  display: block !important;
  line-height: 1 !important;
}

.stat-card .stat-icon i::before {
  color: #06f !important;
}

/* Fallback pour FontAwesome si non chargé */
.stat-card .stat-icon i.fa-download::before {
  content: "⬇️" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-check-circle::before {
  content: "✅" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-clock::before {
  content: "⏰" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-exclamation-triangle::before {
  content: "⚠️" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.secondary-stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  margin-bottom: 2rem;
}

.dashboard-grid {
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background: rgb(255 255 255 / 3%);
  border: 1px solid rgb(255 255 255 / 8%);
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: rgb(255 255 255 / 5%);
  border-color: rgb(0 102 255 / 20%);
}

.activity-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 102 255 / 10%);
  border: 1px solid rgb(0 102 255 / 20%);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.activity-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 70%);
}

.activity-status {
  display: flex;
  align-items: center;
}

.versions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 6px;
  background: rgb(255 255 255 / 3%);
  border: 1px solid rgb(255 255 255 / 8%);
}

.version-number {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.version-meta {
  display: flex;
  gap: 0.5rem;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.version-status {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.download-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 70%);
}

.quick-actions-box {
  margin-top: 2rem;
}

.quick-actions-title {
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.quick-action-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  background: rgb(16 20 35 / 30%);
  border: 1px solid rgb(255 255 255 / 8%);
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  cursor: pointer;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgb(0 102 255 / 15%);
  border-color: rgb(0 102 255 / 30%);
}

.quick-action-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  color: #fff;
  font-size: 1.25rem;
}

.quick-action-content h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.quick-action-content p {
  margin: 0;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 70%);
}

@media (width <= 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
