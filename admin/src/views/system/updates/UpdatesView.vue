<template>
  <div id="updatesView" class="view-container">
    <!-- En-tête avec titre et actions -->
    <div class="header-box">
      <div>
        <h1 class="page-title">{{ t('updates.monitoring.title') }}</h1>
        <span class="page-description">{{ t('updates.monitoring.description') }}</span>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline" :disabled="loading" @click="performCleanup">
          <i class="fas fa-broom"></i>
          {{ t('updates.monitoring.cleanup') }}
        </button>
        <button class="btn btn-gradient" :disabled="loading" @click="refreshData">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          {{ t('common.refresh') }}
        </button>
      </div>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-download"></i>
        </div>
        <div class="stat-number">{{ updatesStore.stats?.updates?.total_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.monitoring.stats.total_updates') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-number">{{ updatesStore.stats?.updates?.active_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.monitoring.stats.active_updates') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ updatesStore.stats?.updates?.completed_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.monitoring.stats.completed_updates') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stat-number">{{ updatesStore.stats?.updates?.failed_updates || 0 }}</div>
        <div class="stat-label">{{ t('updates.monitoring.stats.failed_updates') }}</div>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="filter-box">
      <div class="filter-grid">
        <div class="filter-group">
          <label class="filter-label">{{ t('updates.monitoring.filters.search') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-search"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              class="filter-input" 
              :placeholder="t('updates.monitoring.filters.search_placeholder')"
              @input="debouncedSearch"
            >
          </div>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">{{ t('updates.monitoring.filters.status') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-filter"></i>
            <select v-model="statusFilter" class="filter-input" @change="applyFilters">
              <option value="">{{ t('updates.monitoring.status.all') }}</option>
              <option value="pending">{{ t('updates.monitoring.status.pending') }}</option>
              <option value="downloading">{{ t('updates.monitoring.status.downloading') }}</option>
              <option value="installing">{{ t('updates.monitoring.status.installing') }}</option>
              <option value="completed">{{ t('updates.monitoring.status.completed') }}</option>
              <option value="failed">{{ t('updates.monitoring.status.failed') }}</option>
            </select>
          </div>
        </div>

        <div class="filter-group">
          <label class="filter-label">{{ t('updates.monitoring.filters.license') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-key"></i>
            <input 
              v-model="licenseFilter" 
              type="text" 
              class="filter-input" 
              :placeholder="t('updates.monitoring.filters.license_placeholder')"
              @input="debouncedSearch"
            >
          </div>
        </div>

        <div class="filter-actions">
          <button class="btn btn-outline btn-sm" @click="resetFilters">
            <i class="fas fa-undo"></i>
            {{ t('common.reset') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Liste des mises à jour -->
    <div v-if="updatesStore.loading" class="loading-state box">
      <div class="spinner"></div>
      <p>{{ t('common.loading') }}</p>
    </div>

    <div v-else-if="updatesStore.error" class="error-state box">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3>{{ t('common.error') }}</h3>
      <p>{{ updatesStore.error }}</p>
      <button class="btn btn-primary" @click="loadUpdates">
        {{ t('common.retry') }}
      </button>
    </div>

    <div v-else-if="filteredUpdates.length === 0" class="empty-state box">
      <div class="empty-icon">
        <i class="fas fa-download"></i>
      </div>
      <h3>{{ t('updates.monitoring.empty.title') }}</h3>
      <p>{{ t('updates.monitoring.empty.description') }}</p>
    </div>

    <div v-else class="content-box">
      <div class="updates-list">
        <div 
          v-for="update in filteredUpdates" 
          :key="update.id"
          class="update-card card-box"
          @click="viewUpdateDetail(update.id)"
        >
          <div class="update-header">
            <div class="update-license">
              <i class="fas fa-key"></i>
              <span class="license-key">{{ update.license_key }}</span>
            </div>
            <div class="update-status">
              <span 
                class="status-badge"
                :class="`status-${update.status}`"
              >
                {{ t(`updates.monitoring.status.${update.status}`) }}
              </span>
            </div>
          </div>

          <div class="update-info">
            <div v-if="update.domain" class="update-domain">
              <i class="fas fa-globe"></i>
              <span>{{ update.domain }}</span>
            </div>
            <div v-if="update.client_name" class="update-client">
              <i class="fas fa-user"></i>
              <span>{{ update.client_name }}</span>
            </div>
            <div class="update-versions">
              <div class="version-info">
                <span class="version-label">{{ t('updates.monitoring.from') }}:</span>
                <span class="version-number">{{ update.current_version || t('updates.monitoring.unknown') }}</span>
              </div>
              <i class="fas fa-arrow-right version-arrow"></i>
              <div class="version-info">
                <span class="version-label">{{ t('updates.monitoring.to') }}:</span>
                <span class="version-number">{{ update.target_version }}</span>
              </div>
            </div>
          </div>

          <div class="update-meta">
            <div class="meta-item">
              <i class="fas fa-calendar"></i>
              <span>{{ formatDate(update.created_at) }}</span>
            </div>
            <div v-if="update.completed_at" class="meta-item">
              <i class="fas fa-clock"></i>
              <span>{{ formatDuration(update.started_at || '', update.completed_at || '') }}</span>
            </div>
            <div v-else-if="update.started_at" class="meta-item">
              <i class="fas fa-hourglass-half"></i>
              <span>{{ formatRelativeTime(update.started_at) }}</span>
            </div>
          </div>

          <div class="update-actions">
            <button 
              class="btn btn-sm btn-outline"
              :title="t('updates.monitoring.actions.view')"
              @click.stop="viewUpdateDetail(update.id)"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button 
              v-if="update.status === 'failed'"
              class="btn btn-sm btn-warning"
              :title="t('updates.monitoring.actions.retry')"
              @click.stop="retryUpdate(update)"
            >
              <i class="fas fa-redo"></i>
            </button>
            <button 
              v-if="['pending', 'downloading', 'installing'].includes(update.status)"
              class="btn btn-sm btn-danger"
              :title="t('updates.monitoring.actions.cancel')"
              @click.stop="cancelUpdate(update)"
            >
              <i class="fas fa-stop"></i>
            </button>
          </div>

          <div v-if="update.error_message" class="update-error">
            <i class="fas fa-exclamation-circle"></i>
            <span>{{ update.error_message }}</span>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination-box">
        <div class="pagination">
          <button 
            class="pagination-btn" 
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <span class="pagination-info">
            {{ t('common.pagination.page') }} {{ currentPage }} {{ t('common.pagination.of') }} {{ totalPages }}
          </span>
          
          <button 
            class="pagination-btn" 
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de détails de mise à jour -->
    <UpdateDetailsModal
      v-if="showDetailsModal"
      :show="showDetailsModal"
      :update="selectedUpdate"
      @close="closeDetailsModal"
    />

    <!-- Modal de confirmation d'annulation -->
    <ConfirmModal
      v-if="showCancelModal"
      :show="showCancelModal"
      :title="t('updates.monitoring.cancel.title')"
      :message="t('updates.monitoring.cancel.message')"
      @confirm="confirmCancel"
      @cancel="showCancelModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useUpdatesStore } from '@/stores/updates'
import { useNotificationStore } from '@/stores/notifications'
import type { InstallationUpdate } from '@/stores/updates'

// Composants
import UpdateDetailsModal from '@/components/updates/UpdateDetailsModal.vue'
import ConfirmModal from '@/components/common/ConfirmModal.vue'

// Traduction et navigation
const { t } = useI18n()

// Stores
const updatesStore = useUpdatesStore()
const notificationStore = useNotificationStore()

// État local
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const licenseFilter = ref('')
const showDetailsModal = ref(false)
const showCancelModal = ref(false)
const selectedUpdate = ref<InstallationUpdate | null>(null)
const cancelingUpdate = ref<InstallationUpdate | null>(null)

// Computed
const filteredUpdates = computed(() => {
  let updates = updatesStore.updates || []
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    updates = updates.filter(update => 
      update.license_key?.toLowerCase().includes(query) ||
      update.domain?.toLowerCase().includes(query) ||
      update.client_name?.toLowerCase().includes(query) ||
      update.target_version.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value) {
    updates = updates.filter(update => update.status === statusFilter.value)
  }
  
  if (licenseFilter.value) {
    const licenseQuery = licenseFilter.value.toLowerCase()
    updates = updates.filter(update => 
      update.license_key?.toLowerCase().includes(licenseQuery)
    )
  }
  
  return updates
})

const currentPage = computed(() => updatesStore.currentPage || 1)
const totalPages = computed(() => updatesStore.totalPages || 1)

// Méthodes
const loadUpdates = async () => {
  await Promise.all([
    updatesStore.fetchUpdates(),
    updatesStore.fetchStats()
  ])
}

const refreshData = async () => {
  loading.value = true
  try {
    await loadUpdates()
  } finally {
    loading.value = false
  }
}

const viewUpdateDetail = (updateId: number) => {
  const update = updatesStore.updates.find(u => u.id === updateId)
  if (update) {
    selectedUpdate.value = update
    showDetailsModal.value = true
  }
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedUpdate.value = null
}

const retryUpdate = async (update: InstallationUpdate) => {
  try {
    await updatesStore.updateStatus(update.id, 'pending')
    notificationStore.addNotification({
      type: 'success',
      title: t('common.success'),
      message: t('updates.monitoring.retry_success')
    })
    await loadUpdates()
  } catch {
    notificationStore.addNotification({
      type: 'error',
      title: t('common.error'),
      message: t('updates.monitoring.retry_error')
    })
  }
}

const cancelUpdate = (update: InstallationUpdate) => {
  cancelingUpdate.value = update
  showCancelModal.value = true
}

const confirmCancel = async () => {
  if (cancelingUpdate.value) {
    try {
      await updatesStore.updateStatus(cancelingUpdate.value.id, 'failed')
      notificationStore.addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('updates.monitoring.cancel_success')
      })
      await loadUpdates()
    } catch {
      notificationStore.addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('updates.monitoring.cancel_error')
      })
    }
  }
  showCancelModal.value = false
  cancelingUpdate.value = null
}

const performCleanup = async () => {
  if (!confirm(t('updates.monitoring.confirm_cleanup'))) {
    return
  }

  loading.value = true
  try {
    await updatesStore.cleanup(90)
    notificationStore.addNotification({
      type: 'success',
      title: t('common.success'),
      message: t('updates.monitoring.cleanup_success')
    })
    await loadUpdates()
  } catch {
    notificationStore.addNotification({
      type: 'error',
      title: t('common.error'),
      message: t('updates.monitoring.cleanup_error')
    })
  } finally {
    loading.value = false
  }
}

// Fonction debounce native
const debounce = (func: (...args: any[]) => void, wait: number) => {
  let timeout: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }
}

const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

const applyFilters = () => {
  // Les filtres sont appliqués localement via computed
  updatesStore.fetchUpdates(1)
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  licenseFilter.value = ''
  updatesStore.fetchUpdates(1)
}

const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    updatesStore.fetchUpdates(page)
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

const formatRelativeTime = (date: string) => {
  const now = new Date()
  const updateDate = new Date(date)
  const diffMs = now.getTime() - updateDate.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

  if (diffMins < 60) {
    return t('updates.monitoring.time_ago_minutes', { minutes: diffMins })
  } else {
    return t('updates.monitoring.time_ago_hours', { hours: diffHours })
  }
}

const formatDuration = (startDate: string, endDate: string) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffMs = end.getTime() - start.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffSecs = Math.floor(diffMs / 1000)

  if (diffMins > 0) {
    return t('updates.monitoring.duration_minutes', { minutes: diffMins })
  } else {
    return t('updates.monitoring.duration_seconds', { seconds: diffSecs })
  }
}

// Lifecycle
onMounted(async () => {
  await loadUpdates()
})
</script>

<style scoped>
/* Fix pour les icônes des cartes statistiques */
.stat-card .stat-icon {
  font-size: 2rem !important;
  color: #06f !important;
  margin-bottom: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 60px !important;
  background: rgb(0 102 255 / 10%) !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
}

.stat-card .stat-icon i {
  color: #06f !important;
  font-size: 1.5rem !important;
  display: block !important;
  line-height: 1 !important;
}

.stat-card .stat-icon i::before {
  color: #06f !important;
}

/* Fallback pour FontAwesome si non chargé */
.stat-card .stat-icon i.fa-download::before {
  content: "⬇️" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-clock::before {
  content: "⏰" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-check-circle::before {
  content: "✅" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-exclamation-triangle::before {
  content: "⚠️" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.updates-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.update-card {
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgb(255 255 255 / 8%);
  background: rgb(16 20 35 / 30%);
  backdrop-filter: blur(20px);
}

.update-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgb(0 102 255 / 15%);
  border-color: rgb(0 102 255 / 30%);
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgb(255 255 255 / 8%);
}

.update-license {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.update-license i {
  color: var(--primary-blue);
}

.license-key {
  font-family: 'Courier New', monospace;
  background: rgb(0 102 255 / 10%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgb(0 102 255 / 20%);
}

.update-info {
  margin-bottom: 1rem;
}

.update-domain,
.update-client {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: rgb(255 255 255 / 80%);
}

.update-domain i,
.update-client i {
  color: var(--accent-blue);
  width: 1rem;
}

.update-versions {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: rgb(255 255 255 / 3%);
  border-radius: 6px;
  border: 1px solid rgb(255 255 255 / 5%);
  margin-top: 0.75rem;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.version-label {
  font-size: 0.7rem;
  color: rgb(255 255 255 / 60%);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.version-number {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--primary-blue);
}

.version-arrow {
  color: var(--accent-blue);
  font-size: 0.8rem;
}

.update-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 70%);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.meta-item i {
  color: var(--accent-blue);
}

.update-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.update-actions .btn {
  padding: 0.5rem;
  min-width: 2.5rem;
}

.update-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgb(239 68 68 / 10%);
  border: 1px solid rgb(239 68 68 / 20%);
  border-radius: 6px;
  color: #ef4444;
  font-size: 0.85rem;
}

.update-error i {
  color: #ef4444;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: linear-gradient(135deg, rgb(156 163 175 / 20%), rgb(156 163 175 / 10%));
  color: #9ca3af;
  border: 1px solid rgb(156 163 175 / 30%);
}

.status-downloading {
  background: linear-gradient(135deg, rgb(59 130 246 / 20%), rgb(59 130 246 / 10%));
  color: #3b82f6;
  border: 1px solid rgb(59 130 246 / 30%);
}

.status-installing {
  background: linear-gradient(135deg, rgb(245 158 11 / 20%), rgb(245 158 11 / 10%));
  color: #f59e0b;
  border: 1px solid rgb(245 158 11 / 30%);
}

.status-completed {
  background: linear-gradient(135deg, rgb(0 184 125 / 20%), rgb(0 184 125 / 10%));
  color: #00b87d;
  border: 1px solid rgb(0 184 125 / 30%);
}

.status-failed {
  background: linear-gradient(135deg, rgb(239 68 68 / 20%), rgb(239 68 68 / 10%));
  color: #ef4444;
  border: 1px solid rgb(239 68 68 / 30%);
}

.pagination-box {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgb(16 20 35 / 30%);
  border-radius: 12px;
  border: 1px solid rgb(255 255 255 / 8%);
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: rgb(0 102 255 / 20%);
  border-color: rgb(0 102 255 / 40%);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: rgb(255 255 255 / 70%);
  font-size: 0.9rem;
}

@media (width <= 768px) {
  .update-versions {
    flex-direction: column;
    text-align: center;
  }

  .version-arrow {
    transform: rotate(90deg);
  }

  .update-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
