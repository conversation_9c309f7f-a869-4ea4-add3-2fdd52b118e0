<template>
  <div class="edit-template-view">
    <!-- En-tête -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <button 
            class="btn btn-ghost btn-sm"
            @click="goBack"
          >
            <i class="fas fa-arrow-left"></i>
            {{ $t('common.back') }}
          </button>
          <h1 class="page-title">{{ $t('templates.edit_template') }}</h1>
        </div>
      </div>
    </div>

    <!-- Chargement -->
    <div v-if="loadingTemplate" class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      {{ $t('common.loading') }}
    </div>

    <!-- Formulaire -->
    <div v-else-if="template" class="page-content">
      <form class="template-form" @submit.prevent="handleSubmit">
<!-- Informations de base -->
        <div class="form-section">
          <h2 class="section-title">{{ $t('templates.basic_info') }}</h2>
          
          <div class="form-grid">
            <div class="form-group">
              <label for="name" class="form-label required">
                {{ $t('templates.name') }}
              </label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                class="form-input"
                :class="{ 'error': errors.name }"
                :placeholder="$t('templates.name_placeholder')"
                required
              />
              <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
            </div>

            <div class="form-group">
              <label for="status" class="form-label required">
                {{ $t('templates.status') }}
              </label>
              <select
                id="status"
                v-model="form.status"
                class="form-select"
                :class="{ 'error': errors.status }"
                required
              >
                <option value="draft">{{ $t('templates.status.draft') }}</option>
                <option value="active">{{ $t('templates.status.active') }}</option>
                <option value="inactive">{{ $t('templates.status.inactive') }}</option>
              </select>
              <span v-if="errors.status" class="error-message">{{ errors.status }}</span>
            </div>
          </div>

          <div class="form-group">
            <label for="description" class="form-label">
              {{ $t('templates.description') }}
            </label>
            <textarea
              id="description"
              v-model="form.description"
              class="form-textarea"
              :class="{ 'error': errors.description }"
              :placeholder="$t('templates.description_placeholder')"
              rows="3"
            ></textarea>
            <span v-if="errors.description" class="error-message">{{ errors.description }}</span>
          </div>
        </div>

        <!-- Tarification -->
        <div class="form-section">
          <h2 class="section-title">{{ $t('templates.pricing') }}</h2>
          
          <div class="form-grid">
            <div class="form-group">
              <label for="price" class="form-label required">
                {{ $t('templates.price') }}
              </label>
              <div class="input-group">
                <input
                  id="price"
                  v-model.number="form.price"
                  type="number"
                  step="0.01"
                  min="0"
                  class="form-input"
                  :class="{ 'error': errors.price }"
                  placeholder="0.00 (gratuit autorisé)"
                  required
                />
                <span class="input-suffix">€</span>
              </div>
              <span v-if="errors.price" class="error-message">{{ errors.price }}</span>
            </div>

            <div class="form-group">
              <label for="setup_fee" class="form-label">
                {{ $t('templates.setup_fee') }}
              </label>
              <div class="input-group">
                <input
                  id="setup_fee"
                  v-model.number="form.setup_fee"
                  type="number"
                  step="0.01"
                  min="0"
                  class="form-input"
                  :class="{ 'error': errors.setup_fee }"
                  :placeholder="$t('templates.setup_fee_placeholder')"
                />
                <span class="input-suffix">€</span>
              </div>
              <span v-if="errors.setup_fee" class="error-message">{{ errors.setup_fee }}</span>
            </div>

            <div class="form-group">
              <label for="renewal_price" class="form-label">
                {{ $t('templates.renewal_price') }}
              </label>
              <div class="input-group">
                <input
                  id="renewal_price"
                  v-model.number="form.renewal_price"
                  type="number"
                  step="0.01"
                  min="0"
                  class="form-input"
                  :class="{ 'error': errors.renewal_price }"
                  :placeholder="$t('templates.renewal_price_placeholder')"
                />
                <span class="input-suffix">€</span>
              </div>
              <span v-if="errors.renewal_price" class="error-message">{{ errors.renewal_price }}</span>
            </div>

            <div class="form-group">
              <label for="billing_cycle" class="form-label required">
                {{ $t('templates.billing_cycle') }}
              </label>
              <select
                id="billing_cycle"
                v-model="form.billing_cycle"
                class="form-select"
                :class="{ 'error': errors.billing_cycle }"
                required
              >
                <option value="monthly">{{ $t('templates.billing_cycle.monthly') }}</option>
                <option value="quarterly">{{ $t('templates.billing_cycle.quarterly') }}</option>
                <option value="semi_annually">{{ $t('templates.billing_cycle.semi_annually') }}</option>
                <option value="annually">{{ $t('templates.billing_cycle.annually') }}</option>
                <option value="biennially">{{ $t('templates.billing_cycle.biennially') }}</option>
                <option value="triennially">{{ $t('templates.billing_cycle.triennially') }}</option>
                <option value="one_time">{{ $t('templates.billing_cycle.one_time') }}</option>
              </select>
              <span v-if="errors.billing_cycle" class="error-message">{{ errors.billing_cycle }}</span>
            </div>
          </div>
        </div>

        <!-- Limites -->
        <div class="form-section">
          <h2 class="section-title">{{ $t('templates.limits') }}</h2>
          
          <div class="form-grid">
            <div class="form-group">
              <label for="domain_limit" class="form-label required">
                {{ $t('templates.domain_limit') }}
              </label>
              <input
                id="domain_limit"
                v-model.number="form.domain_limit"
                type="number"
                min="1"
                class="form-input"
                :class="{ 'error': errors.domain_limit }"
                :placeholder="$t('templates.domain_limit_placeholder')"
                required
              />
              <span v-if="errors.domain_limit" class="error-message">{{ errors.domain_limit }}</span>
            </div>

            <div class="form-group">
              <label for="installation_limit" class="form-label required">
                {{ $t('templates.installation_limit') }}
              </label>
              <input
                id="installation_limit"
                v-model.number="form.installation_limit"
                type="number"
                min="1"
                class="form-input"
                :class="{ 'error': errors.installation_limit }"
                :placeholder="$t('templates.installation_limit_placeholder')"
                required
              />
              <span v-if="errors.installation_limit" class="error-message">{{ errors.installation_limit }}</span>
            </div>
          </div>
        </div>

        <!-- Permissions -->
        <div class="form-section">
          <h2 class="section-title">{{ $t('templates.permissions') }}</h2>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="form.update_permissions"
                type="checkbox"
                class="form-checkbox"
              />
              <span class="checkbox-text">{{ $t('templates.update_permissions') }}</span>
            </label>
          </div>
        </div>

        <!-- Options d'affichage -->
        <div class="form-section">
          <h2 class="section-title">{{ $t('templates.display_options') }}</h2>
          
          <div class="form-grid">
            <div class="form-group">
              <label for="sort_order" class="form-label">
                {{ $t('templates.sort_order') }}
              </label>
              <input
                id="sort_order"
                v-model.number="form.sort_order"
                type="number"
                min="0"
                class="form-input"
                :class="{ 'error': errors.sort_order }"
                :placeholder="$t('templates.sort_order_placeholder')"
              />
              <span v-if="errors.sort_order" class="error-message">{{ errors.sort_order }}</span>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="form.is_featured"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span class="checkbox-text">{{ $t('templates.is_featured') }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            :disabled="loading"
            @click="goBack"
          >
            {{ $t('common.cancel') }}
          </button>
          
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="loading"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            {{ loading ? $t('common.updating') : $t('templates.update_template') }}
          </button>
        </div>
      </form>
    </div>

    <!-- Erreur -->
    <div v-else class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      {{ $t('templates.template_not_found') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useTemplatesStore } from '@/stores/templates'
import type { LicenseTemplate, LicenseTemplateForm } from '@/types/template'
import logger from '@/services/logger'

// Composables
const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const templatesStore = useTemplatesStore()

// État local
const loading = ref(false)
const loadingTemplate = ref(true)
const errors = ref<Record<string, string>>({})
const template = ref<LicenseTemplate | null>(null)

// Formulaire
const form = reactive<LicenseTemplateForm>({
  name: '',
  description: '',
  price: 0,
  setup_fee: 0,
  renewal_price: 0,
  billing_cycle: 'monthly',
  domain_limit: 1,
  installation_limit: 1,
  update_permissions: true,
  status: 'draft',
  sort_order: 0,
  is_featured: false,
  features: []
})

// Méthodes
const goBack = () => {
  router.push('/templates')
}

const loadTemplate = async () => {
  const templateId = route.params.id as string
  
  try {
    loadingTemplate.value = true
    template.value = await templatesStore.fetchTemplateById(parseInt(templateId))
    
    if (template.value) {
      // Remplir le formulaire avec les données du template
      Object.assign(form, {
        name: template.value.name,
        description: template.value.description || '',
        price: template.value.price,
        setup_fee: template.value.setup_fee || 0,
        renewal_price: template.value.renewal_price || 0,
        billing_cycle: template.value.billing_cycle,
        domain_limit: template.value.domain_limit,
        installation_limit: template.value.installation_limit,
        update_permissions: template.value.update_permissions,
        status: template.value.status,
        sort_order: template.value.sort_order,
        is_featured: template.value.is_featured,
        features: template.value.features || []
      })
    }
  } catch (error: any) {
    logger.error('[EditTemplateView] Erreur lors du chargement', { error })
  } finally {
    loadingTemplate.value = false
  }
}

const validateForm = (): boolean => {
  errors.value = {}
  
  if (!form.name?.trim()) {
    errors.value.name = t('templates.errors.name_required')
  }
  
  if (form.price < 0) {
    errors.value.price = t('templates.errors.price_invalid')
  }
  
  if (form.domain_limit < 1) {
    errors.value.domain_limit = t('templates.errors.domain_limit_invalid')
  }
  
  if (form.installation_limit < 1) {
    errors.value.installation_limit = t('templates.errors.installation_limit_invalid')
  }
  
  return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    const templateId = parseInt(route.params.id as string)
    const updatedTemplate = await templatesStore.updateTemplate(templateId, form)
    
    logger.info('[EditTemplateView] Template mis à jour avec succès', {
      template_id: updatedTemplate.id,
      name: updatedTemplate.name
    })
    
    // Rediriger vers la liste des templates
    router.push('/templates')
    
  } catch (error: any) {
    logger.error('[EditTemplateView] Erreur lors de la mise à jour', { error })
    
    // Afficher l'erreur (vous pouvez utiliser un toast ou notification)
    console.error('Erreur lors de la mise à jour du template:', error.message)
    
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadTemplate()
})
</script>

<style scoped>
/* Réutiliser les mêmes styles que CreateTemplateView */
.edit-template-view {
  min-height: 100vh;
  background: var(--bg-primary);
}

.page-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.page-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.template-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--color-danger);
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--color-danger);
}

.input-group {
  display: flex;
  align-items: center;
}

.input-suffix {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-left: none;
  padding: 0.75rem;
  border-radius: 0 6px 6px 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.form-checkbox {
  width: 1rem;
  height: 1rem;
}

.checkbox-text {
  color: var(--text-primary);
  font-size: 0.875rem;
}

.error-message {
  color: var(--color-danger);
  font-size: 0.75rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-tertiary);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-quaternary);
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-dark);
}
</style>
