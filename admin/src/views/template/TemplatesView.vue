<template>
  <div id="templates">
    <!-- En-tête avec actions -->
    <div class="header-box">
      <div class="header-content">
        <div class="header-left">
          <h1>{{ $t('templates.title') }}</h1>
          <p class="header-description">{{ $t('templates.description') }}</p>
        </div>
        <div class="header-actions">
          <router-link to="/templates/create" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            {{ $t('templates.create_template') }}
          </router-link>
        </div>
      </div>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-layer-group"></i>
        </div>
        <div class="stat-number">{{ templatesStore.stats.total }}</div>
        <div class="stat-label">{{ $t('templates.stats.total') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ templatesStore.stats.active }}</div>
        <div class="stat-label">{{ $t('templates.stats.active') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-star"></i>
        </div>
        <div class="stat-number">{{ templatesStore.stats.featured }}</div>
        <div class="stat-label">{{ $t('templates.stats.featured') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-edit"></i>
        </div>
        <div class="stat-number">{{ templatesStore.stats.draft }}</div>
        <div class="stat-label">{{ $t('templates.stats.draft') }}</div>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="filters-box card-box">
      <div class="filters-content">
        <div class="search-box">
          <div class="input-group">
            <i class="fas fa-search"></i>
            <input 
              v-model="searchQuery" 
              type="text"
              :placeholder="$t('templates.search_placeholder')"
              class="form-control"
              @input="handleSearch"
            >
          </div>
        </div>

        <div class="filter-controls">
          <select v-model="statusFilter" class="form-select" @change="handleFilterChange">
            <option value="">{{ $t('templates.filters.all_status') }}</option>
            <option value="active">{{ $t('templates.filters.active') }}</option>
            <option value="inactive">{{ $t('templates.filters.inactive') }}</option>
            <option value="draft">{{ $t('templates.filters.draft') }}</option>
          </select>

          <select v-model="sortBy" class="form-select" @change="handleSortChange">
            <option value="created_at">{{ $t('templates.sort.newest') }}</option>
            <option value="name">{{ $t('templates.sort.name') }}</option>
            <option value="price">{{ $t('templates.sort.price') }}</option>
            <option value="updated_at">{{ $t('templates.sort.updated') }}</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Liste des templates -->
    <div class="templates-list card-box">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-layer-group"></i>
          {{ $t('templates.list_title') }}
        </h3>
        <div class="card-actions">
          <span class="results-count">
            {{ templatesStore.pagination.total }} {{ $t('templates.results') }}
          </span>
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="templatesStore.loading" class="loading-state">
        <div class="spinner"></div>
        <p>{{ $t('common.loading') }}</p>
      </div>

      <!-- Error state -->
      <div v-else-if="templatesStore.error" class="error-state">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <p>{{ templatesStore.error }}</p>
        <button class="btn btn-outline" @click="loadTemplates">
          {{ $t('common.retry') }}
        </button>
      </div>

      <!-- Templates grid -->
      <div v-else-if="templatesStore.templates.length > 0" class="templates-grid">
        <div 
          v-for="template in templatesStore.templates" 
          :key="template.id"
          class="template-card"
        >
          <div class="template-header">
            <div class="template-status">
              <span 
                class="status-badge" 
                :class="`status-${template.status}`"
              >
                {{ $t(`templates.status.${template.status}`) }}
              </span>
              <span v-if="template.is_featured" class="featured-badge">
                <i class="fas fa-star"></i>
                {{ $t('templates.featured') }}
              </span>
            </div>
            <div class="template-actions">
              <button 
                class="btn btn-sm btn-outline"
                :title="$t('common.edit')"
                @click="editTemplate(template.id)"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button 
                class="btn btn-sm btn-danger"
                :title="$t('common.delete')"
                @click="deleteTemplate(template.id)"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <div class="template-content">
            <h4 class="template-name">{{ template.name }}</h4>
            <p class="template-description">{{ template.description }}</p>
            
            <div class="template-details">
              <div class="detail-item">
                <i class="fas fa-euro-sign"></i>
                <span>{{ formatPrice(template.price) }}</span>
                <small>/ {{ $t(`billing.cycles.${template.billing_cycle}`) }}</small>
              </div>
              
              <div class="detail-item">
                <i class="fas fa-globe"></i>
                <span>{{ template.domain_limit }} domaine(s)</span>
              </div>
              
              <div class="detail-item">
                <i class="fas fa-download"></i>
                <span>{{ template.installation_limit }} installation(s)</span>
              </div>
              
              <div v-if="template.update_permissions" class="detail-item">
                <i class="fas fa-sync"></i>
                <span>{{ $t('templates.updates_included') }}</span>
              </div>
            </div>

            <div class="template-footer">
              <small class="text-muted">
                {{ $t('templates.created') }} {{ formatDate(template.created_at) }}
              </small>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-layer-group"></i>
        </div>
        <h3>{{ $t('templates.empty.title') }}</h3>
        <p>{{ $t('templates.empty.description') }}</p>
        <router-link to="/templates/create" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          {{ $t('templates.create_first') }}
        </router-link>
      </div>

      <!-- Pagination -->
      <div v-if="templatesStore.templates.length > 0" class="pagination-box">
        <div class="pagination-info">
          {{ $t('pagination.showing') }} 
          {{ (templatesStore.pagination.current_page - 1) * templatesStore.pagination.per_page + 1 }}
          {{ $t('pagination.to') }}
          {{ Math.min(templatesStore.pagination.current_page * templatesStore.pagination.per_page, templatesStore.pagination.total) }}
          {{ $t('pagination.of') }}
          {{ templatesStore.pagination.total }}
          {{ $t('pagination.results') }}
        </div>
        
        <div class="pagination-controls">
          <button 
            :disabled="templatesStore.pagination.current_page <= 1"
            class="btn btn-sm btn-outline"
            @click="changePage(templatesStore.pagination.current_page - 1)"
          >
            <i class="fas fa-chevron-left"></i>
            {{ $t('pagination.previous') }}
          </button>
          
          <span class="page-info">
            {{ $t('pagination.page') }} {{ templatesStore.pagination.current_page }}
          </span>
          
          <button 
            :disabled="templatesStore.pagination.current_page >= Math.ceil(templatesStore.pagination.total / templatesStore.pagination.per_page)"
            class="btn btn-sm btn-outline"
            @click="changePage(templatesStore.pagination.current_page + 1)"
          >
            {{ $t('pagination.next') }}
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useTemplatesStore } from '@/stores/templates'
import logger from '@/services/logger'

// Composables
const router = useRouter()
const { t } = useI18n()
const templatesStore = useTemplatesStore()

// État local
const searchQuery = ref('')
const statusFilter = ref('')
const sortBy = ref('created_at')

// Méthodes
const loadTemplates = async () => {
  try {
    await templatesStore.fetchTemplates(1, 15, statusFilter.value)
    await templatesStore.fetchStats()
  } catch (error) {
    logger.error('[TemplatesView] Erreur lors du chargement', { error })
  }
}

const handleSearch = () => {
  loadTemplates()
}

const handleFilterChange = () => {
  loadTemplates()
}

const handleSortChange = () => {
  loadTemplates()
}

const changePage = (page: number) => {
  templatesStore.fetchTemplates(page, 15, statusFilter.value)
}

const editTemplate = (id: number) => {
  router.push(`/templates/${id}/edit`)
}

const deleteTemplate = async (id: number) => {
  if (confirm(t('templates.confirm_delete'))) {
    try {
      await templatesStore.deleteTemplate(id)
      logger.info('[TemplatesView] Template supprimé', { id })
    } catch (error) {
      logger.error('[TemplatesView] Erreur suppression', { error })
    }
  }
}

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('fr-FR')
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
/* Styles spécifiques à la vue templates */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.header-left h1 {
  margin: 0 0 0.5rem;
}

.header-description {
  color: var(--text-secondary);
  margin: 0;
}

.filters-content {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.filter-controls {
  display: flex;
  gap: 1rem;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem 0;
}

.template-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.template-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.template-status {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background: rgb(34 197 94 / 10%);
  color: #22c55e;
}

.status-inactive {
  background: rgb(239 68 68 / 10%);
  color: #ef4444;
}

.status-draft {
  background: rgb(251 191 36 / 10%);
  color: #fbbf24;
}

.featured-badge {
  background: rgb(147 51 234 / 10%);
  color: #9333ea;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.template-actions {
  display: flex;
  gap: 0.5rem;
}

.template-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.template-description {
  color: var(--text-secondary);
  margin: 0 0 1rem;
  line-height: 1.5;
}

.template-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.detail-item i {
  color: var(--primary-color);
  width: 16px;
}

.template-footer {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.pagination-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0 0;
  border-top: 1px solid var(--border-color);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-info {
  font-weight: 600;
  color: var(--text-primary);
}

/* États spéciaux */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
}

.loading-state .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state .error-icon,
.empty-state .empty-icon {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

/* Responsive */
@media (width <= 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .filters-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .pagination-box {
    flex-direction: column;
    gap: 1rem;
  }
}


</style>
