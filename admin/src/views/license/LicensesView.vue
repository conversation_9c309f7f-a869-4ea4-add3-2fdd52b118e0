<template>
  <div id="licensesList" class="view-container">
    <!-- En-tête avec titre et bouton d'ajout -->
    <div class="header-box">
      <div>
        <h1 class="page-title">{{ t('licenses.title') }}</h1>
        <span class="page-description">{{ t('licenses.description') }}</span>
      </div>
      <button class="btn btn-gradient" @click="handleAddLicense">
        <i class="fas fa-plus"></i>
        {{ t('licenses.add_new') }}
      </button>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-key"></i>
        </div>
        <div class="stat-number">{{ licenseStore.stats?.total || 0 }}</div>
        <div class="stat-label">{{ t('licenses.stats.total') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ licenseStore.stats?.active || 0 }}</div>
        <div class="stat-label">{{ t('licenses.stats.active') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-number">{{ licenseStore.stats?.expiring_soon || 0 }}</div>
        <div class="stat-label">{{ t('licenses.stats.expiring_soon') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-number">{{ licenseStore.stats?.expired || 0 }}</div>
        <div class="stat-label">{{ t('licenses.stats.expired') }}</div>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="filter-box">
      <div class="filter-grid">
        <div class="filter-group">
          <label class="filter-label">{{ t('licenses.filters.search') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-search"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              class="filter-input" 
              :placeholder="t('licenses.filters.search_placeholder')"
              @input="debouncedSearch"
            >
          </div>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">{{ t('licenses.filters.status') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-filter"></i>
            <select v-model="statusFilter" class="filter-input" @change="applyFilters">
              <option value="">{{ t('licenses.status.all') }}</option>
              <option value="active">{{ t('licenses.status.active') }}</option>
              <option value="inactive">{{ t('licenses.status.inactive') }}</option>
              <option value="expired">{{ t('licenses.status.expired') }}</option>
              <option value="suspended">{{ t('licenses.status.suspended') }}</option>
            </select>
          </div>
        </div>

        <div class="filter-group">
          <label class="filter-label">{{ t('licenses.filters.client') }}</label>
          <div class="filter-input-wrapper">
            <i class="fas fa-user"></i>
            <select v-model="clientFilter" class="filter-input" @change="applyFilters">
              <option value="">{{ t('licenses.filters.all_clients') }}</option>
              <option v-for="client in clientsStore.clients" :key="client.id" :value="client.id">
                {{ client.firstname }} {{ client.lastname }}
              </option>
            </select>
          </div>
        </div>

        <div class="filter-actions">
          <button class="btn btn-outline btn-sm" @click="resetFilters">
            <i class="fas fa-undo"></i>
            {{ t('common.reset') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Liste des licences -->
    <div v-if="licenseStore.loading" class="loading-state box">
      <div class="spinner"></div>
      <p>{{ t('common.loading') }}</p>
    </div>

    <div v-else-if="licenseStore.error" class="error-state box">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3>{{ t('common.error') }}</h3>
      <p>{{ licenseStore.error }}</p>
      <button class="btn btn-primary" @click="loadLicenses">
        {{ t('common.retry') }}
      </button>
    </div>

    <div v-else-if="filteredLicenses.length === 0" class="empty-state box">
      <div class="empty-icon">
        <i class="fas fa-key"></i>
      </div>
      <h3>{{ t('licenses.empty.title') }}</h3>
      <p>{{ t('licenses.empty.description') }}</p>
      <button class="btn btn-primary" @click="handleAddLicense">
        <i class="fas fa-plus"></i>
        {{ t('licenses.add_new') }}
      </button>
    </div>

    <div v-else class="content-box">
      <div class="licenses-grid">
        <div 
          v-for="license in filteredLicenses" 
          :key="license.id"
          class="license-card card-box"
          @click="viewLicenseDetail(license.id)"
        >
          <div class="license-header">
            <div class="license-key">
              <i class="fas fa-key"></i>
              <code>{{ license.license_key }}</code>
            </div>
            <div class="license-status">
              <span 
                class="status-badge"
                :class="`status-${license.status}`"
              >
                {{ t(`licenses.status.${license.status}`) }}
              </span>
            </div>
          </div>

          <div class="license-client">
            <i class="fas fa-user"></i>
            <span>{{ license.client?.name || t('licenses.no_client') }}</span>
          </div>

          <div class="license-details">
            <div class="license-info">
              <div class="info-item">
                <i class="fas fa-globe"></i>
                <span>{{ t('licenses.domain_limit') }}: {{ license.domain_limit }}</span>
              </div>
              <div class="info-item">
                <i class="fas fa-download"></i>
                <span>{{ t('licenses.installation_limit') }}: {{ license.installation_limit }}</span>
              </div>
              <div class="info-item">
                <i class="fas fa-calendar"></i>
                <span>{{ formatExpiryDate(license.expires_at) }}</span>
              </div>
            </div>
          </div>

          <div class="license-actions">
            <button 
              class="btn btn-sm btn-outline"
              :title="t('licenses.actions.edit')"
              @click.stop="editLicense(license)"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button 
              class="btn btn-sm btn-primary"
              :title="t('licenses.actions.view')"
              @click.stop="viewLicenseDetail(license.id)"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button 
              class="btn btn-sm btn-danger"
              :title="t('licenses.actions.delete')"
              @click.stop="confirmDelete(license)"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination-box">
        <div class="pagination">
          <button 
            class="pagination-btn" 
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <span class="pagination-info">
            {{ t('common.pagination.page') }} {{ currentPage }} {{ t('common.pagination.of') }} {{ totalPages }}
          </span>
          
          <button 
            class="pagination-btn" 
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de création/édition -->
    <LicenseFormModal
      v-if="showCreateModal || showEditModal"
      :show="showCreateModal || showEditModal"
      :license="editingLicense"
      @close="closeModals"
      @saved="onLicenseSaved"
    />

    <!-- Modal de confirmation de suppression -->
    <ConfirmModal
      v-if="showDeleteModal"
      :show="showDeleteModal"
      :title="t('licenses.delete.title')"
      :message="t('licenses.delete.message', { key: deletingLicense?.license_key })"
      @confirm="deleteLicense"
      @cancel="showDeleteModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useLicenseManagementStore } from '@/stores/license-management'
import { useClientsStore } from '@/stores/clients'
import type { License } from '@/types/license-management'

// Composants
import LicenseFormModal from '@/components/licenses/LicenseFormModal.vue'
import ConfirmModal from '@/components/common/ConfirmModal.vue'

// Traduction et navigation
const { t } = useI18n()
const router = useRouter()

// Stores
const licenseStore = useLicenseManagementStore()
const clientsStore = useClientsStore()

// État local
const searchQuery = ref('')
const statusFilter = ref('')
const clientFilter = ref('')
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const editingLicense = ref<License | null>(null)
const deletingLicense = ref<License | null>(null)

// Computed
const filteredLicenses = computed(() => {
  let licenses = licenseStore.licenses || []
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    licenses = licenses.filter(license => 
      license.license_key.toLowerCase().includes(query) ||
      license.client?.name?.toLowerCase().includes(query) ||
      license.allowed_domains?.toLowerCase().includes(query)
    )
  }
  
  if (statusFilter.value) {
    licenses = licenses.filter(license => license.status === statusFilter.value)
  }
  
  if (clientFilter.value) {
    licenses = licenses.filter(license => license.client_id === parseInt(clientFilter.value))
  }
  
  return licenses
})

const currentPage = computed(() => licenseStore.currentPage || 1)
const totalPages = computed(() => licenseStore.totalPages || 1)

// Méthodes
const loadLicenses = async () => {
  await Promise.all([
    licenseStore.fetchLicenses(),
    licenseStore.fetchStats(),
    clientsStore.fetchClients()
  ])
}

const handleAddLicense = () => {
  editingLicense.value = null
  showCreateModal.value = true
}

const editLicense = (license: License) => {
  editingLicense.value = license
  showEditModal.value = true
}

const viewLicenseDetail = (licenseId: number) => {
  router.push(`/licenses/${licenseId}`)
}

const confirmDelete = (license: License) => {
  deletingLicense.value = license
  showDeleteModal.value = true
}

const deleteLicense = async () => {
  if (deletingLicense.value) {
    try {
      await licenseStore.deleteLicense(deletingLicense.value.id)
      showDeleteModal.value = false
      deletingLicense.value = null
    } catch {
      // L'erreur est gérée par le store
    }
  }
}

const closeModals = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingLicense.value = null
}

const onLicenseSaved = () => {
  closeModals()
  licenseStore.fetchLicenses(currentPage.value)
}

// Fonction debounce native
const debounce = (func: (...args: any[]) => void, wait: number) => {
  let timeout: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }
}

const debouncedSearch = debounce(() => {
  applyFilters()
}, 300)

const applyFilters = () => {
  licenseStore.setFilters({
    search: searchQuery.value,
    status: statusFilter.value || undefined,
    client_id: clientFilter.value ? parseInt(clientFilter.value) : undefined
  })
  licenseStore.fetchLicenses(1)
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  clientFilter.value = ''
  licenseStore.resetFilters()
  licenseStore.fetchLicenses(1)
}

const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    licenseStore.fetchLicenses(page)
  }
}

const formatExpiryDate = (expiryDate: string | null) => {
  if (!expiryDate) {
    return t('licenses.no_expiry')
  }

  const date = new Date(expiryDate)
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return t('licenses.expired')
  } else if (diffDays === 0) {
    return t('licenses.expires_today')
  } else if (diffDays <= 30) {
    return t('licenses.expires_in_days', { days: diffDays })
  } else {
    return date.toLocaleDateString()
  }
}

// Lifecycle
onMounted(async () => {
  await loadLicenses()
})
</script>

<style scoped>
/* Fix pour les icônes des cartes statistiques */
.stat-card .stat-icon {
  font-size: 2rem !important;
  color: #06f !important;
  margin-bottom: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 60px !important;
  background: rgb(0 102 255 / 10%) !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
}

.stat-card .stat-icon i {
  color: #06f !important;
  font-size: 1.5rem !important;
  display: block !important;
  line-height: 1 !important;
}

.stat-card .stat-icon i::before {
  color: #06f !important;
}

/* Fallback pour FontAwesome si non chargé */
.stat-card .stat-icon i.fa-key::before {
  content: "🔑" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-check-circle::before {
  content: "✅" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-clock::before {
  content: "⏰" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.stat-card .stat-icon i.fa-times-circle::before {
  content: "❌" !important;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
}

.licenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.license-card {
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgb(255 255 255 / 8%);
  background: rgb(16 20 35 / 30%);
  backdrop-filter: blur(20px);
}

.license-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgb(0 102 255 / 15%);
  border-color: rgb(0 102 255 / 30%);
}

.license-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgb(255 255 255 / 8%);
}

.license-key {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.license-key i {
  color: var(--primary-blue);
}

.license-key code {
  background: rgb(0 102 255 / 10%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  border: 1px solid rgb(0 102 255 / 20%);
}

.license-client {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: rgb(255 255 255 / 70%);
  font-size: 0.9rem;
}

.license-client i {
  color: var(--accent-blue);
}

.license-details {
  margin-bottom: 1.5rem;
}

.license-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 70%);
}

.info-item i {
  width: 1rem;
  color: var(--accent-blue);
}

.license-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.license-actions .btn {
  padding: 0.5rem;
  min-width: 2.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: linear-gradient(135deg, rgb(0 184 125 / 20%), rgb(0 184 125 / 10%));
  color: #00b87d;
  border: 1px solid rgb(0 184 125 / 30%);
}

.status-inactive {
  background: linear-gradient(135deg, rgb(156 163 175 / 20%), rgb(156 163 175 / 10%));
  color: #9ca3af;
  border: 1px solid rgb(156 163 175 / 30%);
}

.status-expired {
  background: linear-gradient(135deg, rgb(239 68 68 / 20%), rgb(239 68 68 / 10%));
  color: #ef4444;
  border: 1px solid rgb(239 68 68 / 30%);
}

.status-suspended {
  background: linear-gradient(135deg, rgb(245 158 11 / 20%), rgb(245 158 11 / 10%));
  color: #f59e0b;
  border: 1px solid rgb(245 158 11 / 30%);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
}

.pagination-box {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgb(16 20 35 / 30%);
  border-radius: 12px;
  border: 1px solid rgb(255 255 255 / 8%);
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: rgb(0 102 255 / 20%);
  border-color: rgb(0 102 255 / 40%);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: rgb(255 255 255 / 70%);
  font-size: 0.9rem;
}
</style>
