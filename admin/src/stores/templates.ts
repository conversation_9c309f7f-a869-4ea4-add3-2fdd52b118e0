import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '@/services/api'
import logger from '@/services/logger'
import type {
  LicenseTemplate,
  LicenseTemplateForm,
  LicenseTemplateStats
} from '@/types/template'

export const useTemplatesStore = defineStore('templates', () => {
  // État
  const templates = ref<LicenseTemplate[]>([])
  const currentTemplate = ref<LicenseTemplate | null>(null)
  const stats = ref<LicenseTemplateStats>({
    total: 0,
    active: 0,
    inactive: 0,
    draft: 0,
    featured: 0
  })
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Pagination
  const pagination = ref({
    current_page: 1,
    per_page: 15,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  })

  // Filtres
  const filters = ref({
    status: '',
    search: ''
  })

  // Getters
  const getTemplateById = computed(() => (id: number) => {
    return templates.value.find(template => template.id === id) || null
  })

  const activeTemplates = computed(() => 
    templates.value.filter(template => template.status === 'active')
  )

  const featuredTemplates = computed(() =>
    templates.value.filter(template => template.is_featured && template.status === 'active')
  )

  const hasTemplates = computed(() => templates.value.length > 0)

  // Actions
  const fetchTemplates = async (page = 1, perPage = 15, status = '') => {
    loading.value = true
    error.value = null

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: perPage.toString()
      })

      if (status) {
        params.append('status', status)
      }

      const response = await ApiService.routes.admin.templates.list({
        page: page.toString(),
        per_page: perPage.toString(),
        ...(status && { status })
      })

      if (response.data?.templates) {
        // Convertir les données API pour le frontend
        templates.value = (response.data.templates || []).map((template: any) => ({
          ...template,
          update_permissions: Boolean(parseInt(template.update_permissions)),
          is_featured: Boolean(parseInt(template.is_featured)),
          price: parseFloat(template.price),
          setup_fee: parseFloat(template.setup_fee || '0'),
          renewal_price: parseFloat(template.renewal_price || '0')
        }))
        pagination.value = response.data.pagination || pagination.value

        logger.info('[TemplatesStore] Templates récupérés avec succès', {
          count: templates.value.length,
          page,
          total: pagination.value.total
        })
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la récupération des templates')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la récupération des templates'
      logger.error('[TemplatesStore] Erreur fetchTemplates', { error: err.message })
    } finally {
      loading.value = false
    }
  }

  const fetchTemplateById = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.admin.templates.get(id)

      if (response.data?.template) {
        // Convertir les données API pour le frontend
        const template = {
          ...response.data.template,
          update_permissions: Boolean(parseInt(response.data.template.update_permissions)),
          is_featured: Boolean(parseInt(response.data.template.is_featured)),
          price: parseFloat(response.data.template.price),
          setup_fee: parseFloat(response.data.template.setup_fee || '0'),
          renewal_price: parseFloat(response.data.template.renewal_price || '0')
        }

        currentTemplate.value = template

        logger.info('[TemplatesStore] Template récupéré avec succès', { id })
        return template
      } else {
        throw new Error(response.data?.message || 'Template non trouvé')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la récupération du template'
      logger.error('[TemplatesStore] Erreur fetchTemplateById', { id, error: err.message })
      return null
    } finally {
      loading.value = false
    }
  }

  const createTemplate = async (templateData: LicenseTemplateForm) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.admin.templates.create(templateData)

      if (response.data?.template) {
        // Ajouter le nouveau template à la liste
        templates.value.unshift(response.data.template)

        // Mettre à jour les stats
        await fetchStats()

        logger.info('[TemplatesStore] Template créé avec succès', {
          id: response.data.template.id,
          name: response.data.template.name
        })

        return response.data.template
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la création du template')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la création du template'
      logger.error('[TemplatesStore] Erreur createTemplate', { error: err.message })
      return null
    } finally {
      loading.value = false
    }
  }

  const updateTemplate = async (id: number, templateData: Partial<LicenseTemplateForm>) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.admin.templates.update(id, templateData)

      if (response.data?.template) {
        // Mettre à jour le template dans la liste
        const index = templates.value.findIndex(t => t.id === id)
        if (index !== -1) {
          templates.value[index] = response.data.template
        }

        // Mettre à jour le template courant si c'est le même
        if (currentTemplate.value?.id === id) {
          currentTemplate.value = response.data.template
        }

        logger.info('[TemplatesStore] Template mis à jour avec succès', { id })
        return response.data.template
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la mise à jour du template')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la mise à jour du template'
      logger.error('[TemplatesStore] Erreur updateTemplate', { id, error: err.message })
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteTemplate = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.admin.templates.delete(id)

      if (response.data) {
        // Supprimer le template de la liste
        templates.value = templates.value.filter(t => t.id !== id)

        // Réinitialiser le template courant si c'est le même
        if (currentTemplate.value?.id === id) {
          currentTemplate.value = null
        }

        // Mettre à jour les stats
        await fetchStats()

        logger.info('[TemplatesStore] Template supprimé avec succès', { id })
        return true
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la suppression du template')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la suppression du template'
      logger.error('[TemplatesStore] Erreur deleteTemplate', { id, error: err.message })
      return false
    } finally {
      loading.value = false
    }
  }

  const fetchStats = async () => {
    try {
      const response = await ApiService.routes.admin.templates.stats()

      if (response.data?.stats) {
        stats.value = response.data.stats
        logger.info('[TemplatesStore] Statistiques récupérées', { stats: response.data.stats })
      }
    } catch (err: any) {
      logger.error('[TemplatesStore] Erreur fetchStats', { error: err.message })
    }
  }

  const updateSortOrder = async (templateIds: number[]) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.admin.templates.updateOrder(templateIds)

      if (response.data) {
        // Recharger les templates pour avoir le bon ordre
        await fetchTemplates(pagination.value.current_page, pagination.value.per_page, filters.value.status)

        logger.info('[TemplatesStore] Ordre mis à jour avec succès')
        return true
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la mise à jour de l\'ordre')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la mise à jour de l\'ordre'
      logger.error('[TemplatesStore] Erreur updateSortOrder', { error: err.message })
      return false
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentTemplate = () => {
    currentTemplate.value = null
  }

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  return {
    // État
    templates,
    currentTemplate,
    stats,
    loading,
    error,
    pagination,
    filters,
    
    // Getters
    getTemplateById,
    activeTemplates,
    featuredTemplates,
    hasTemplates,
    
    // Actions
    fetchTemplates,
    fetchTemplateById,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    fetchStats,
    updateSortOrder,
    clearError,
    clearCurrentTemplate,
    setFilters
  }
})
