<template>
  <div class="file-upload-content">
    <!-- Version Info -->
    <div class="version-info-header">
      <span class="version-badge">{{ version.version }}</span>
    </div>

    <!-- Upload Area -->
    <div class="upload-section">
      <div
        class="upload-area"
        :class="{
          'drag-over': isDragOver,
          'has-file': selectedFile,
          'uploading': uploading
        }"
        @drop="handleDrop"
        @dragover.prevent="handleDragOver"
        @dragleave="handleDragLeave"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept=".zip,.tar.gz"
          style="display: none"
          @change="handleFileSelect"
        >

        <div v-if="!selectedFile" class="upload-placeholder">
          <i class="fas fa-cloud-upload-alt upload-icon"></i>
          <h4>{{ t('updates.versions.uploadTitle') }}</h4>
          <p>{{ t('updates.versions.uploadDescription') }}</p>
          <button type="button" class="btn btn-primary">
            {{ t('updates.versions.selectFile') }}
          </button>
          <small class="upload-help">{{ t('updates.versions.uploadHelp') }}</small>
        </div>

        <div v-else-if="!uploading" class="file-preview">
          <div class="file-icon">
            <i class="fas fa-file-archive"></i>
          </div>
          <div class="file-details">
            <h4 class="file-name">{{ selectedFile.name }}</h4>
            <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
            <p class="file-type">{{ selectedFile.type || 'application/zip' }}</p>
          </div>
          <button
            type="button"
            class="file-remove"
            @click.stop="removeFile"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div v-else class="upload-progress">
          <div class="progress-circle">
            <svg class="progress-ring" width="100" height="100">
              <circle
                class="progress-ring-circle"
                stroke="var(--primary-color)"
                stroke-width="6"
                fill="transparent"
                r="42"
                cx="50"
                cy="50"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="progressOffset"
              />
            </svg>
            <span class="progress-text">{{ Math.round(uploadProgress) }}%</span>
          </div>

          <div class="upload-info">
            <h4>{{ t('updates.versions.uploading') }}</h4>
            <p class="file-name">{{ selectedFile.name }}</p>
            <p v-if="totalBytes > 0" class="file-size">
              {{ formatFileSize(bytesUploaded) }} / {{ formatFileSize(totalBytes) }}
            </p>
            <p v-else class="file-size">{{ formatFileSize(selectedFile.size) }}</p>

            <div class="upload-stats">
              <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span>{{ formatTime(uploadTimeElapsed) }}</span>
              </div>
              <div v-if="uploadSpeed > 0" class="stat-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>{{ formatSpeed(uploadSpeed) }}</span>
              </div>
              <div v-if="estimatedTimeRemaining > 0 && estimatedTimeRemaining < 3600" class="stat-item">
                <i class="fas fa-hourglass-half"></i>
                <span>{{ formatTime(estimatedTimeRemaining) }} restant</span>
              </div>
              <div v-if="uploadProgress > 0" class="stat-item">
                <i class="fas fa-chart-line"></i>
                <span>{{ Math.round(uploadProgress) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Display -->
      <div v-if="error" class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        {{ error }}
      </div>
    </div>

    <!-- File Requirements -->
      <div class="requirements-section">
        <h4 class="requirements-title">{{ t('updates.versions.fileRequirements') }}</h4>
        <ul class="requirements-list">
          <li>
            <i class="fas fa-check text-success"></i>
            {{ t('updates.versions.requirement1') }}
          </li>
          <li>
            <i class="fas fa-check text-success"></i>
            {{ t('updates.versions.requirement2') }}
          </li>
          <li>
            <i class="fas fa-check text-success"></i>
            {{ t('updates.versions.requirement3') }}
          </li>
          <li>
            <i class="fas fa-check text-success"></i>
            {{ t('updates.versions.requirement4') }}
          </li>
        </ul>
      </div>

      <!-- Current File Info (if exists) -->
      <div v-if="version.file_exists" class="current-file-section">
        <h4 class="current-file-title">{{ t('updates.versions.currentFile') }}</h4>
        <div class="current-file-info">
          <div class="current-file-details">
            <i class="fas fa-file-archive text-success"></i>
            <span class="current-file-name">{{ version.version }}.zip</span>
            <span v-if="version.file_size" class="current-file-size">
              ({{ formatFileSize(version.file_size) }})
            </span>
          </div>
          <div v-if="version.file_hash" class="current-file-hash">
            <small>{{ t('updates.versions.fileHash') }}: {{ version.file_hash }}</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="upload-actions">
      <button
        type="button"
        :disabled="uploading"
        class="btn btn-secondary"
        @click="$emit('cancel')"
      >
        {{ t('common.cancel') }}
      </button>
      <button
        type="button"
        :disabled="!selectedFile || uploading"
        class="btn btn-primary upload-btn"
        @click="handleUpload"
      >
        <i v-if="uploading" class="fas fa-spinner fa-spin"></i>
        <i v-else class="fas fa-upload"></i>
        {{ uploading ? t('updates.versions.uploading') : t('updates.versions.uploadFile') }}
      </button>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import logger from '@/services/logger'
import { uploadService, type UploadProgress } from '@/services/uploadService'
import { chunkedUploadService, type ChunkUploadProgress } from '@/services/chunkedUploadService'

const { t } = useI18n()

// Props
interface Props {
  version: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  upload: [file: File]
  uploaded: [response: any]
  cancel: []
  close: []
}>()

// État local
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const isDragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const error = ref('')
const uploadTimeElapsed = ref(0)
const uploadSpeed = ref(0)
const estimatedTimeRemaining = ref(0)
const bytesUploaded = ref(0)
const totalBytes = ref(0)

// Computed
const circumference = computed(() => 2 * Math.PI * 36)
const progressOffset = computed(() => {
  return circumference.value - (uploadProgress.value / 100) * circumference.value
})

// Méthodes
const triggerFileInput = () => {
  if (!uploading.value) {
    fileInput.value?.click()
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const file = event.dataTransfer?.files[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const validateAndSetFile = (file: File) => {
  error.value = ''
  
  // Validation du type de fichier
  const allowedTypes = ['application/zip', 'application/x-zip-compressed', 'application/gzip', 'application/x-tar']
  const allowedExtensions = ['.zip', '.tar.gz']
  
  const isValidType = allowedTypes.includes(file.type) || 
    allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
  
  if (!isValidType) {
    error.value = t('updates.versions.validation.invalidFileType')
    return
  }
  
  // Validation de la taille (max 1GB)
  const maxSize = 1024 * 1024 * 1024 // 1GB
  if (file.size > maxSize) {
    error.value = t('updates.versions.validation.fileTooLarge')
    return
  }
  
  selectedFile.value = file
  logger.info('[FILE UPLOAD MODAL] Fichier sélectionné', { 
    name: file.name, 
    size: file.size, 
    type: file.type 
  })
}

const removeFile = () => {
  selectedFile.value = null
  error.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const handleUpload = async () => {
  if (!selectedFile.value) return

  uploading.value = true
  uploadProgress.value = 0
  error.value = ''
  uploadTimeElapsed.value = 0
  uploadSpeed.value = 0
  estimatedTimeRemaining.value = 0

  try {
    logger.info('[FILE UPLOAD MODAL] Début de l\'upload réel', {
      file: selectedFile.value.name,
      version: props.version.version,
      size: selectedFile.value.size
    })

    // Construction de l'URL d'upload
    const uploadUrl = `/api/v1/admin/versions/${props.version.id}/upload`

    // Données additionnelles à envoyer
    const additionalData = {
      version_id: props.version.id,
      version: props.version.version
    }

    // Seuil pour upload chunked : 70MB (entre 60MB qui marche et 136MB qui plante)
    const chunkThreshold = 70 * 1024 * 1024 // 70MB
    const useChunkedUpload = selectedFile.value.size > chunkThreshold

    logger.info('[FILE UPLOAD MODAL] Méthode d\'upload sélectionnée', {
      fileSize: selectedFile.value.size,
      threshold: chunkThreshold,
      useChunked: useChunkedUpload
    })

    // Lancer l'upload selon la méthode choisie
    if (useChunkedUpload) {
      // Upload chunked pour gros fichiers
      await chunkedUploadService.uploadFileInChunks(uploadUrl, selectedFile.value, additionalData, {
        onProgress: (progress: ChunkUploadProgress) => {
          // Adapter les statistiques chunked au format normal
          uploadProgress.value = progress.overallProgress
          uploadTimeElapsed.value = progress.timeElapsed
          uploadSpeed.value = progress.speed
          estimatedTimeRemaining.value = progress.timeRemaining
          bytesUploaded.value = progress.uploadedBytes
          totalBytes.value = progress.totalSize

          logger.debug('[FILE UPLOAD MODAL] Progression chunked', {
            chunk: `${progress.chunkIndex}/${progress.totalChunks}`,
            chunkProgress: Math.round(progress.chunkProgress),
            overallProgress: Math.round(progress.overallProgress),
            speed: Math.round(progress.speed / 1024), // KB/s
            timeRemaining: Math.round(progress.timeRemaining)
          })
        },
        onChunkComplete: (chunkIndex: number, totalChunks: number) => {
          logger.info('[FILE UPLOAD MODAL] Chunk terminé', {
            chunk: `${chunkIndex}/${totalChunks}`,
            progress: `${Math.round((chunkIndex / totalChunks) * 100)}%`
          })
        },
        onSuccess: (response) => {
          logger.info('[FILE UPLOAD MODAL] Upload chunked terminé avec succès', { response })
          uploadProgress.value = 100

          // Notifier le parent du succès
          emit('uploaded', response)

          // Fermer immédiatement
          setTimeout(() => {
            uploading.value = false
            uploadProgress.value = 0
            selectedFile.value = null
            emit('close')
          }, 500)
        },
        onError: (uploadError) => {
          logger.error('[FILE UPLOAD MODAL] Erreur upload chunked', { error: uploadError })
          error.value = uploadError.message || t('updates.versions.uploadError')
          uploading.value = false
          uploadProgress.value = 0
        },
        chunkSize: 50 * 1024 * 1024, // 50MB par chunk
        timeout: 120000 // 2 minutes par chunk
      })
    } else {
      // Upload normal pour petits fichiers
      await uploadService.uploadFile(uploadUrl, selectedFile.value, additionalData, {
      onProgress: (progress: UploadProgress) => {
        // VRAIES statistiques temps réel !
        uploadProgress.value = progress.percentage
        uploadTimeElapsed.value = progress.timeElapsed
        uploadSpeed.value = progress.speed
        estimatedTimeRemaining.value = progress.timeRemaining
        bytesUploaded.value = progress.loaded
        totalBytes.value = progress.total

        logger.debug('[FILE UPLOAD MODAL] Progression réelle', {
          percentage: Math.round(progress.percentage),
          loaded: progress.loaded,
          total: progress.total,
          speed: Math.round(progress.speed / 1024), // KB/s
          timeRemaining: Math.round(progress.timeRemaining)
        })
      },
      onSuccess: (response) => {
        logger.info('[FILE UPLOAD MODAL] Upload terminé avec succès', { response })
        uploadProgress.value = 100

        // Notifier le parent du succès
        emit('uploaded', response)

        // Fermer immédiatement
        setTimeout(() => {
          uploading.value = false
          uploadProgress.value = 0
          selectedFile.value = null
          emit('close')
        }, 500)
      },
      onError: (uploadError) => {
        logger.error('[FILE UPLOAD MODAL] Erreur upload réel', { error: uploadError })
        error.value = uploadError.message || t('updates.versions.uploadError')
        uploading.value = false
        uploadProgress.value = 0
      },
      timeout: 600000 // 10 minutes pour diagnostic
    })
    }

  } catch (err: any) {
    logger.error('[FILE UPLOAD MODAL] Erreur lors de l\'upload', { error: err })
    error.value = err.message || t('updates.versions.uploadError')
    uploading.value = false
    uploadProgress.value = 0
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond: number) => {
  return formatFileSize(bytesPerSecond) + '/s'
}

const formatTime = (seconds: number) => {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.round(seconds % 60)
    return `${minutes}m ${remainingSeconds}s`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }
}

// Méthode pour terminer l'upload (appelée par le parent)
const finishUpload = (success: boolean = true) => {
  if (success) {
    uploadProgress.value = 100
    // Attendre un peu pour voir 100% puis fermer
    setTimeout(() => {
      uploading.value = false
      uploadProgress.value = 0
      selectedFile.value = null
    }, 1000)
  } else {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// Exposer la méthode pour le parent
defineExpose({
  finishUpload
})
</script>

<style scoped>
.file-upload-modal {
  max-width: 600px;
  width: 100%;
}

.modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-color);
}

.version-info {
  color: var(--primary-color);
  font-weight: 600;
  margin-left: 0.5rem;
}

.modal-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

.upload-section {
  margin-bottom: 2rem;
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--background-light);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: rgb(var(--primary-rgb), 0.05);
}

.upload-area.drag-over {
  border-color: var(--primary-color);
  background: rgb(var(--primary-rgb), 0.1);
  transform: scale(1.02);
}

.upload-area.has-file {
  border-color: var(--success-color);
  background: rgb(var(--success-rgb), 0.05);
}

.upload-area.uploading {
  border-color: var(--info-color);
  background: rgb(var(--info-rgb), 0.05);
  cursor: not-allowed;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.upload-placeholder h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.25rem;
}

.upload-placeholder p {
  margin: 0;
  color: var(--text-muted);
}

.upload-help {
  color: var(--text-muted);
  font-style: italic;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  background: var(--bg-secondary);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  border: 1px solid var(--border-color);
}

.file-icon {
  font-size: 2rem;
  color: var(--success-color);
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  margin: 0 0 0.25rem;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.file-size,
.file-type {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.file-remove {
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-remove:hover {
  background: var(--danger-dark);
  transform: scale(1.1);
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
  position: absolute;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.upload-info {
  text-align: center;
  width: 100%;
}

.upload-info h4 {
  margin: 0 0 0.5rem;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.upload-info .file-name {
  margin: 0 0 0.25rem;
  color: var(--text-color);
  font-weight: 600;
  font-size: 1rem;
}

.upload-info .file-size {
  margin: 0 0 1rem;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.upload-stats {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  background: var(--bg-secondary);
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  border: 1px solid var(--border-color);
}

.stat-item i {
  color: var(--primary-color);
  font-size: 0.8rem;
}

.requirements-section {
  background: var(--background-light);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.requirements-title {
  margin: 0 0 1rem;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-color);
  font-size: 0.9rem;
}

.current-file-section {
  background: rgb(var(--info-rgb), 0.1);
  border: 1px solid rgb(var(--info-rgb), 0.2);
  border-radius: 8px;
  padding: 1.5rem;
}

.current-file-title {
  margin: 0 0 1rem;
  color: var(--text-color);
  font-size: 1rem;
  font-weight: 600;
}

.current-file-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.current-file-details {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.current-file-name {
  font-weight: 600;
  color: var(--text-color);
}

.current-file-size {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.current-file-hash {
  font-family: monospace;
  color: var(--text-muted);
  font-size: 0.8rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
}

.alert {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-danger {
  background: rgb(var(--danger-rgb), 0.1);
  border: 1px solid rgb(var(--danger-rgb), 0.2);
  color: var(--danger-color);
}

/* Action Buttons */
.upload-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1.5rem 0 0;
  border-top: 1px solid var(--border-color);
  margin-top: 1.5rem;
}

.upload-btn {
  min-width: 140px;
  font-weight: 600;
}

.upload-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.version-info-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.version-badge {
  background: var(--primary-color);
  color: var(--primary-contrast, white);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  border: 1px solid var(--primary-color);
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .upload-area {
    background: var(--bg-secondary, #2a2a2a);
    border-color: var(--border-color, #404040);
  }

  .file-preview {
    background: var(--bg-secondary, #2a2a2a);
    box-shadow: 0 2px 8px rgb(0 0 0 / 30%);
  }

  .requirements-section {
    background: var(--bg-secondary, #2a2a2a);
  }

  .upload-area:hover {
    background: rgb(var(--primary-rgb), 0.1);
  }
}

/* Responsive */
@media (width <= 768px) {
  .file-preview {
    flex-direction: column;
    text-align: center;
  }

  .upload-area {
    padding: 1.5rem;
  }

  .upload-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .upload-actions .btn {
    width: 100%;
  }
}
</style>
