/* Client App Layout Styles */
.client-app-layout {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    color: #fff;
}

/* Header positioning */
.client-app-layout header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    height: 70px;
}

.app-container {
    display: flex;
    flex: 1;
    overflow: hidden;
    margin-top: 70px; /* Hauteur du header */
}

.app-content {
    flex: 1;
    overflow-y: auto;
    margin-left: 280px; /* Largeur de la sidebar */
    transition: margin-left 0.3s ease;
    background: transparent;
}

.app-content.sidebar-collapsed {
    margin-left: 80px; /* Largeur de la sidebar réduite */
}

/* Responsive */
@media (width <= 768px) {
    .app-content {
        margin-left: 0;
    }
    
    .app-content.sidebar-collapsed {
        margin-left: 0;
    }
}
