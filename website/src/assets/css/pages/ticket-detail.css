/* Ticket Detail Page Styles */
#ticket-detail {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.ticket-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
}

.header-content {
  flex: 1;
}

.back-btn {
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateX(-5px);
}

.ticket-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ticket-icon-large {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 16px rgb(139 92 246 / 30%);
}

.ticket-info h1 {
  margin: 0 0 0.5rem;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
}

.ticket-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.ticket-id {
  background: rgb(255 255 255 / 10%);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border: 1px solid var(--glass-border);
  font-family: 'Courier New', monospace;
}

.ticket-actions {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

/* Status Classes */
.status-open {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-closed {
  background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-pending {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Priority Classes */
.priority-low {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.priority-medium {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.priority-high {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.priority-urgent {
  background: linear-gradient(135deg, #DC2626 0%, #991B1B 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner,
.error-message {
  text-align: center;
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.error-message i {
  font-size: 2rem;
  color: #EF4444;
  margin-bottom: 1rem;
}

/* Ticket Content */
.ticket-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.info-card,
.conversation-card,
.reply-card,
.notice-card {
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
  transition: all 0.3s ease;
}

.info-card:hover,
.conversation-card:hover,
.reply-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgb(0 102 255 / 20%);
}

.info-card h2,
.conversation-card h2,
.reply-card h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.info-card h2 i,
.conversation-card h2 i,
.reply-card h2 i {
  color: var(--primary);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.info-item span {
  font-weight: 500;
  color: var(--text-primary);
}

.ticket-number {
  font-family: 'Courier New', monospace;
  background: rgb(0 102 255 / 10%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  color: var(--primary);
  border: 1px solid rgb(0 102 255 / 20%);
}

/* Conversation */
.conversation-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.client-message .message-avatar {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.staff-message .message-avatar {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.message-content {
  flex: 1;
  background: rgb(255 255 255 / 5%);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid var(--glass-border);
}

.staff-message .message-content {
  background: rgb(139 92 246 / 10%);
  border-color: rgb(139 92 246 / 20%);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-author {
  font-weight: 600;
  color: var(--text-primary);
}

.staff-message .message-author {
  color: #8B5CF6;
}

.message-date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.message-body {
  color: var(--text-primary);
  line-height: 1.6;
  white-space: pre-wrap;
}

.message-attachments {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--glass-border);
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.attachment-item i {
  color: var(--primary);
}

.attachment-item a {
  color: var(--primary);
  text-decoration: none;
  font-size: 0.875rem;
}

.attachment-item a:hover {
  text-decoration: underline;
}

/* Reply Form */
.reply-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
}

.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: rgb(255 255 255 / 5%);
  color: var(--text-primary);
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgb(0 102 255 / 20%);
}

.form-file {
  padding: 0.75rem;
  border: 2px dashed var(--glass-border);
  border-radius: 8px;
  background: rgb(255 255 255 / 5%);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-file:hover {
  border-color: var(--primary);
  background: rgb(0 102 255 / 5%);
}

.file-info {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.selected-files {
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--glass-border);
}

.selected-files h4 {
  margin: 0 0 0.75rem;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgb(255 255 255 / 5%);
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-item i {
  color: var(--primary);
}

.file-item span {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.remove-file {
  background: none;
  border: none;
  color: #EF4444;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.remove-file:hover {
  background: rgb(239 68 68 / 10%);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Closed Notice */
.closed-notice {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.notice-card {
  text-align: center;
  max-width: 400px;
}

.notice-card i {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.notice-card h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.notice-card p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
}

/* Responsive */
@media (width <= 768px) {
  #ticket-detail {
    padding: 1rem;
  }

  .ticket-detail-header {
    flex-direction: column;
    gap: 1rem;
  }

  .ticket-actions {
    width: 100%;
    justify-content: stretch;
  }

  .ticket-actions .btn {
    flex: 1;
  }

  .ticket-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .ticket-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .message-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .message-avatar {
    align-self: flex-start;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
  }
}
