/* Support View Variables */
:root {
    --primary-blue: #06F;
    --secondary-blue: #00C6FF;
    --accent-blue: #5F9FFF;
    --neon-blue: #00F0FF;
    
    /* Glass effects */
    --glass-bg: rgb(16 20 35 / 70%);
    --card-border: rgb(255 255 255 / 8%);
    --text-color: #FFF;
    --text-muted: #A0A0A0;
    --text-primary: #E4E6EB;
    --text-secondary: #B0B3B8;
    --bg-primary: rgb(16 20 35 / 95%);
    --bg-secondary: rgb(255 255 255 / 10%);
    --border-color: rgb(255 255 255 / 10%);
    --danger-color: #ff4b4b;
    --success-color: #00b87d;
    --warning-color: #f90;
    --info-color: #3b82f6;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --radius-lg: 12px;
    --shadow-lg: 0 8px 25px rgb(0 0 0 / 30%);
}

/* Support Page Layout */
#client-support {
    padding: 1.5rem;
    padding-top: 1.5rem;
    min-height: calc(100vh - 70px);
    background: transparent;
    color: #fff;
}

/* Header Section */
.support-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    background: rgb(16 20 35 / 20%);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgb(255 255 255 / 8%);
    box-shadow: 0 8px 20px 0 rgb(0 102 255 / 10%);
}

.support-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue));
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.support-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

.action-card {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.25rem;
    box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
    backdrop-filter: blur(20px);
    border: 1px solid var(--card-border);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-card:hover::before {
    opacity: 1;
}

.action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px 0 rgb(0 102 255 / 20%);
}

.action-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgb(0 102 255 / 25%);
}

.action-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.action-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Tickets Section */
.tickets-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

.tickets-container {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.25rem;
    box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
    backdrop-filter: blur(20px);
    border: 1px solid var(--card-border);
}

.tickets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--card-border);
}

.tickets-title {
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tickets-filters {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.filter-select {
    background: rgb(255 255 255 / 10%);
    border: 1px solid rgb(255 255 255 / 20%);
    border-radius: 8px;
    padding: 0.4rem 0.6rem;
    color: #fff;
    font-size: 0.8rem;
    min-width: 120px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: rgb(255 255 255 / 15%);
    box-shadow: 0 0 0 3px rgb(0 102 255 / 20%);
}

.filter-select option {
    background: #1a1f2e;
    color: #fff;
}

/* Tickets List */
.tickets-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ticket-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    background: rgb(16 20 35 / 15%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(255 255 255 / 8%);
    box-shadow: 0 8px 20px 0 rgb(0 102 255 / 10%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ticket-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px 0 rgb(0 102 255 / 15%);
    border-color: rgb(0 102 255 / 20%);
}

.ticket-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.ticket-priority {
    width: 0.25rem;
    height: 2rem;
    border-radius: 0.125rem;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.priority-low {
    background: var(--success-color);
}

.priority-medium {
    background: var(--warning-color);
}

.priority-high {
    background: var(--danger-color);
}

.priority-urgent {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
}

.ticket-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    font-size: 0.875rem;
    box-shadow: 0 2px 8px rgb(0 102 255 / 20%);
    border: 1px solid rgb(255 255 255 / 8%);
    flex-shrink: 0;
}

.ticket-details {
    flex: 1;
}

.ticket-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.125rem;
    line-height: 1.2;
}

.ticket-title a {
    color: inherit;
    text-decoration: none;
    transition: all 0.2s;
}

.ticket-title a:hover {
    color: var(--primary-blue);
}

.ticket-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.ticket-id {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.ticket-date {
    font-size: 0.8rem;
    color: rgb(255 255 255 / 70%);
    font-weight: 500;
}

.ticket-status {
    display: flex;
    justify-content: flex-end;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    backdrop-filter: blur(5px);
    border: 1px solid rgb(255 255 255 / 10%);
}

.status-open {
    background: linear-gradient(135deg, rgb(59 130 246 / 20%), rgb(59 130 246 / 10%));
    color: var(--info-color);
    border-color: rgb(59 130 246 / 30%);
}

.status-answered {
    background: linear-gradient(135deg, rgb(34 197 94 / 20%), rgb(34 197 94 / 10%));
    color: var(--success-color);
    border-color: rgb(34 197 94 / 30%);
}

.status-customer-reply {
    background: linear-gradient(135deg, rgb(255 153 0 / 20%), rgb(255 153 0 / 10%));
    color: var(--warning-color);
    border-color: rgb(255 153 0 / 30%);
}

.status-in-progress {
    background: linear-gradient(135deg, rgb(255 153 0 / 20%), rgb(255 153 0 / 10%));
    color: var(--warning-color);
    border-color: rgb(255 153 0 / 30%);
}

.status-resolved {
    background: linear-gradient(135deg, rgb(0 184 125 / 20%), rgb(0 184 125 / 10%));
    color: var(--success-color);
    border-color: rgb(0 184 125 / 30%);
}

.status-closed {
    background: linear-gradient(135deg, rgb(107 114 128 / 20%), rgb(107 114 128 / 10%));
    color: #6b7280;
    border-color: rgb(107 114 128 / 30%);
}

/* Action Buttons */
.ticket-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.btn {
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    border: 1px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgb(0 102 255 / 30%);
}

.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: #fff;
}

.btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
}

/* Loading and Empty States */
.loading-state {
    text-align: center;
    padding: 3rem 0;
    color: rgb(255 255 255 / 70%);
}

.loading-state i {
    font-size: 2rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 0;
    color: rgb(255 255 255 / 60%);
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (width <= 1200px) {
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (width <= 768px) {
    #client-support {
        padding: 1rem;
    }
    
    .support-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .support-actions {
        justify-content: center;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .tickets-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }
    
    .tickets-filters {
        justify-content: center;
    }
    
    .ticket-item {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }
    
    .ticket-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .ticket-status-actions {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .ticket-actions {
        justify-content: center;
        margin-top: 0;
    }
}

/* New Ticket Modal Styles */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgb(0 0 0 / 70%);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 20px 60px rgb(0 0 0 / 40%);
    backdrop-filter: blur(20px);
    border: 1px solid var(--card-border);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--card-border);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s;
}

.modal-close:hover {
    background: rgb(255 255 255 / 10%);
    color: var(--text-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    background: rgb(255 255 255 / 10%);
    border: 1px solid rgb(255 255 255 / 20%);
    border-radius: 8px;
    padding: 0.75rem;
    color: #fff;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: rgb(255 255 255 / 15%);
    box-shadow: 0 0 0 3px rgb(0 102 255 / 20%);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: rgb(255 255 255 / 50%);
}

.form-select option {
    background: #1a1f2e;
    color: #fff;
}

.modal-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--card-border);
}
