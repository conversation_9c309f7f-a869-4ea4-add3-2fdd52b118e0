/* Account View Variables */
:root {
    --primary-blue: #06F;
    --secondary-blue: #00C6FF;
    --accent-blue: #5F9FFF;
    --neon-blue: #00F0FF;
    
    /* Glass effects */
    --glass-bg: rgb(16 20 35 / 70%);
    --card-border: rgb(255 255 255 / 8%);
    --text-color: #FFF;
    --text-muted: #A0A0A0;
    --text-primary: #E4E6EB;
    --text-secondary: #B0B3B8;
    --bg-primary: rgb(16 20 35 / 95%);
    --bg-secondary: rgb(255 255 255 / 10%);
    --border-color: rgb(255 255 255 / 10%);
    --danger-color: #ff4b4b;
    --success-color: #00b87d;
    --warning-color: #f90;
    --info-color: #3b82f6;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --radius-lg: 12px;
    --shadow-lg: 0 8px 25px rgb(0 0 0 / 30%);
}

/* Account Page Layout */
#client-account {
    padding: 1.5rem;
    padding-top: 1.5rem;
    min-height: calc(100vh - 70px);
    background: transparent;
    color: #fff;
}

/* Header Section */
.account-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    background: rgb(16 20 35 / 20%);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgb(255 255 255 / 8%);
    box-shadow: 0 8px 20px 0 rgb(0 102 255 / 10%);
}

.account-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue));
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.account-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Account Grid */
.account-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Account Cards */
.account-card {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.25rem;
    box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
    backdrop-filter: blur(20px);
    border: 1px solid var(--card-border);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.account-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.account-card:hover::before {
    opacity: 1;
}

.account-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px 0 rgb(0 102 255 / 20%);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--card-border);
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-body {
    flex: 1;
}

/* Profile Section */
.profile-avatar {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.avatar-container {
    position: relative;
}

.avatar-image {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgb(0 102 255 / 25%);
    border: 3px solid rgb(255 255 255 / 10%);
}

.avatar-upload {
    position: absolute;
    bottom: -0.25rem;
    right: -0.25rem;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    background: var(--primary-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid rgb(16 20 35);
}

.avatar-upload:hover {
    background: var(--secondary-blue);
    transform: scale(1.1);
}

.profile-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem;
}

.profile-info p {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input,
.form-select {
    width: 100%;
    background: rgb(255 255 255 / 10%);
    border: 1px solid rgb(255 255 255 / 20%);
    border-radius: 8px;
    padding: 0.75rem;
    color: #fff;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: rgb(255 255 255 / 15%);
    box-shadow: 0 0 0 3px rgb(0 102 255 / 20%);
}

.form-input::placeholder {
    color: rgb(255 255 255 / 50%);
}

.form-select option {
    background: #1a1f2e;
    color: #fff;
}

.form-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Security Section */
.security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    background: rgb(16 20 35 / 15%);
    backdrop-filter: blur(10px);
    border: 1px solid rgb(255 255 255 / 8%);
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.security-item:hover {
    background: rgb(255 255 255 / 10%);
    border-color: rgb(0 102 255 / 20%);
}

.security-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.security-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    font-size: 1rem;
    box-shadow: 0 4px 12px rgb(0 102 255 / 25%);
}

.security-details h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.25rem;
}

.security-details p {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
}

.security-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.status-active {
    background: var(--success-color);
}

.status-inactive {
    background: var(--danger-color);
}

.status-warning {
    background: var(--warning-color);
}

/* Notifications Section */
.notification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgb(255 255 255 / 8%);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-info {
    flex: 1;
}

.notification-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.notification-description {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 3rem;
    height: 1.5rem;
    background: rgb(255 255 255 / 20%);
    border-radius: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-switch.active {
    background: var(--primary-blue);
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
}

.toggle-switch.active::before {
    transform: translateX(1.5rem);
}

/* Action Buttons */
.btn {
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    border: 1px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgb(0 102 255 / 30%);
}

.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: #fff;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #ff6b6b);
    color: #fff;
    border: 1px solid transparent;
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgb(255 75 75 / 30%);
}

.btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--card-border);
}

/* Loading and Success States */
.loading-state {
    text-align: center;
    padding: 2rem 0;
    color: rgb(255 255 255 / 70%);
}

.loading-state i {
    font-size: 1.5rem;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.success-message {
    background: linear-gradient(135deg, rgb(0 184 125 / 20%), rgb(0 184 125 / 10%));
    border: 1px solid rgb(0 184 125 / 30%);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: var(--success-color);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-message {
    background: linear-gradient(135deg, rgb(255 75 75 / 20%), rgb(255 75 75 / 10%));
    border: 1px solid rgb(255 75 75 / 30%);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive Design */
@media (width <= 1200px) {
    .account-grid {
        grid-template-columns: 1fr;
    }
}

@media (width <= 768px) {
    #client-account {
        padding: 1rem;
    }
    
    .account-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .account-actions {
        justify-content: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .profile-avatar {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .security-item {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }
    
    .security-status {
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
}


