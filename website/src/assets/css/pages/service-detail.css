/* Service Detail Page Styles */
#service-detail {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.service-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
}

.header-content {
  flex: 1;
}

.back-btn {
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateX(-5px);
}

.service-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.service-icon-large {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 16px rgb(0 102 255 / 30%);
}

.service-info h1 {
  margin: 0 0 0.5rem;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
}

.service-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.service-type {
  background: rgb(255 255 255 / 10%);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border: 1px solid var(--glass-border);
}

.service-actions {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

/* Status Classes */
.status-active {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-suspended {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-terminated {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-pending {
  background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner,
.error-message {
  text-align: center;
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.error-message i {
  font-size: 2rem;
  color: #EF4444;
  margin-bottom: 1rem;
}

.error-message h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.error-message p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
}

/* Service Content */
.service-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.service-overview {
  grid-column: 1 / -1;
}

.overview-card,
.config-card,
.billing-card,
.history-card {
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
  transition: all 0.3s ease;
}

.overview-card:hover,
.config-card:hover,
.billing-card:hover,
.history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgb(0 102 255 / 20%);
}

.overview-card h2,
.config-card h2,
.billing-card h2,
.history-card h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.overview-card h2 i,
.config-card h2 i,
.billing-card h2 i,
.history-card h2 i {
  color: var(--primary);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.info-item span {
  font-weight: 500;
  color: var(--text-primary);
}

/* Configuration Content */
.config-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  border: 1px solid var(--glass-border);
}

.config-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.config-value {
  font-family: 'Courier New', monospace;
  background: rgb(0 102 255 / 10%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  color: var(--primary);
  border: 1px solid rgb(0 102 255 / 20%);
}

/* Billing Content */
.billing-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.billing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  border: 1px solid var(--glass-border);
}

.billing-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.price {
  font-size: 1.125rem;
  font-weight: 600;
  color: #10B981;
}

/* Service History */
.service-history {
  grid-column: 1 / -1;
}

.no-history {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.no-history i {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  border: 1px solid var(--glass-border);
  transition: all 0.3s ease;
}

.history-item:hover {
  background: rgb(255 255 255 / 8%);
  transform: translateX(5px);
}

.history-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.history-details {
  flex: 1;
}

.history-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.history-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.history-date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Responsive */
@media (width <= 768px) {
  #service-detail {
    padding: 1rem;
  }

  .service-detail-header {
    flex-direction: column;
    gap: 1rem;
  }

  .service-actions {
    width: 100%;
    justify-content: stretch;
  }

  .service-actions .btn {
    flex: 1;
  }

  .service-content {
    grid-template-columns: 1fr;
  }

  .service-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .config-item,
  .billing-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
