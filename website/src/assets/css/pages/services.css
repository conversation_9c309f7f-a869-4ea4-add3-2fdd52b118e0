/* Services View Variables */
:root {
    --primary-blue: #06F;
    --secondary-blue: #00C6FF;
    --accent-blue: #5F9FFF;
    --neon-blue: #00F0FF;
    
    /* Glass effects */
    --glass-bg: rgb(16 20 35 / 70%);
    --card-border: rgb(255 255 255 / 8%);
    --text-color: #FFF;
    --text-muted: #A0A0A0;
    --text-primary: #E4E6EB;
    --text-secondary: #B0B3B8;
    --bg-primary: rgb(16 20 35 / 95%);
    --bg-secondary: rgb(255 255 255 / 10%);
    --border-color: rgb(255 255 255 / 10%);
    --danger-color: #ff4b4b;
    --success-color: #00b87d;
    --warning-color: #f90;
    --info-color: #3b82f6;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --radius-lg: 12px;
    --shadow-lg: 0 8px 25px rgb(0 0 0 / 30%);
}

/* Services Page Layout */
#client-services {
    padding: 1.5rem;
    padding-top: 1.5rem;
    min-height: calc(100vh - 70px);
    background: transparent;
    color: #fff;
}

/* Header Section */
.services-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    background: rgb(16 20 35 / 20%);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgb(255 255 255 / 8%);
    box-shadow: 0 8px 20px 0 rgb(0 102 255 / 10%);
}

.services-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue));
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.services-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Filter Section */
.services-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgb(16 20 35 / 15%);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgb(255 255 255 / 8%);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    background: rgb(255 255 255 / 10%);
    border: 1px solid rgb(255 255 255 / 20%);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    color: #fff;
    font-size: 0.875rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: rgb(255 255 255 / 15%);
    box-shadow: 0 0 0 3px rgb(0 102 255 / 20%);
}

.filter-select option {
    background: #1a1f2e;
    color: #fff;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

/* Service Card */
.service-card {
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 1.25rem;
    box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
    backdrop-filter: blur(20px);
    border: 1px solid var(--card-border);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px 0 rgb(0 102 255 / 20%);
    border-color: rgb(0 102 255 / 20%);
}

/* Service Header */
.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.service-info {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    flex: 1;
}

.service-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    font-size: 1.25rem;
    box-shadow: 0 4px 12px rgb(0 102 255 / 25%);
    border: 2px solid rgb(255 255 255 / 10%);
    flex-shrink: 0;
}

.service-details {
    flex: 1;
}

.service-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.service-type {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.service-description {
    font-size: 0.8rem;
    color: rgb(255 255 255 / 70%);
    line-height: 1.4;
}

/* Service Status */
.service-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    backdrop-filter: blur(5px);
    border: 1px solid rgb(255 255 255 / 10%);
}

.status-active {
    background: linear-gradient(135deg, rgb(0 184 125 / 20%), rgb(0 184 125 / 10%));
    color: var(--success-color);
    border-color: rgb(0 184 125 / 30%);
}

.status-suspended {
    background: linear-gradient(135deg, rgb(255 75 75 / 20%), rgb(255 75 75 / 10%));
    color: var(--danger-color);
    border-color: rgb(255 75 75 / 30%);
}

.status-pending {
    background: linear-gradient(135deg, rgb(255 153 0 / 20%), rgb(255 153 0 / 10%));
    color: var(--warning-color);
    border-color: rgb(255 153 0 / 30%);
}

.status-expired {
    background: linear-gradient(135deg, rgb(107 114 128 / 20%), rgb(107 114 128 / 10%));
    color: #6b7280;
    border-color: rgb(107 114 128 / 30%);
}

/* Service Meta */
.service-meta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid rgb(255 255 255 / 8%);
    text-align: center;
}

.service-pricing {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
}

.service-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1;
}

.service-period {
    font-size: 0.7rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-dates {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.service-date-label {
    font-size: 0.65rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-date-value {
    font-size: 0.75rem;
    color: rgb(255 255 255 / 80%);
    font-weight: 600;
}

/* Service Actions */
.service-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn {
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: #fff;
    border: 1px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgb(0 102 255 / 30%);
}

.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: #fff;
}

.btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
}

/* Loading and Empty States */
.loading-state {
    text-align: center;
    padding: 3rem 0;
    color: rgb(255 255 255 / 70%);
}

.loading-state i {
    font-size: 2rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 0;
    color: rgb(255 255 255 / 60%);
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (width <= 1200px) {
    .services-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (width <= 768px) {
    #client-services {
        padding: 1rem;
    }
    
    .services-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .services-actions {
        justify-content: center;
    }
    
    .services-filters {
        flex-direction: column;
        gap: 1rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .service-header {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .service-status {
        align-items: flex-start;
        flex-direction: row;
        justify-content: space-between;
    }
}
