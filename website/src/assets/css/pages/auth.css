/**
 * Styles pour les pages d'authentification client
 * Architecture CSS modulaire - TechCMS Client
 */

/* Container principal d'authentification */
.auth-container {
    width: 100vw;
    height: 100vh;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
    padding: 1rem;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

.auth-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 30% 20%, rgb(0 102 255 / 10%) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgb(0 153 255 / 8%) 0%, transparent 50%);
    pointer-events: none;
}

/* Carte d'authentification */
.auth-card {
    background: rgb(255 255 255 / 5%);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(255 255 255 / 10%);
    border-radius: 1.5rem;
    padding: 2rem;
    width: 100%;
    max-width: 650px;
    min-width: 450px;
    box-shadow: 0 20px 40px rgb(0 0 0 / 30%);
    position: relative;
    z-index: 1;
    box-sizing: border-box;
}

.auth-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgb(255 255 255 / 10%) 0%, rgb(255 255 255 / 5%) 100%);
    border-radius: 1.5rem;
    pointer-events: none;
}

/* En-tête d'authentification */
.auth-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.auth-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 10px 30px rgb(0 102 255 / 30%);
}

.auth-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.4rem;
    background: linear-gradient(135deg, #fff, #e0e7ff);
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-subtitle {
    color: rgb(255 255 255 / 70%);
    font-size: 0.9rem;
    font-weight: 400;
}

/* Formulaire d'authentification */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

@media (width <= 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.form-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: rgb(255 255 255 / 90%);
    margin-bottom: 0.2rem;
}

.form-input {
    background: rgb(255 255 255 / 8%);
    border: 1px solid rgb(255 255 255 / 15%);
    border-radius: 0.75rem;
    padding: 0.65rem 0.9rem;
    font-size: 0.9rem;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: rgb(255 255 255 / 50%);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: rgb(255 255 255 / 12%);
    box-shadow: 0 0 0 3px rgb(0 102 255 / 10%);
}

.form-input.error {
    border-color: #ff4757;
    background: rgb(255 71 87 / 10%);
}

/* Messages d'erreur */
.form-error {
    color: #ff4757;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-error i {
    font-size: 0.75rem;
}

/* Messages de succès */
.form-success {
    color: #2ed573;
    font-size: 0.8rem;
    font-weight: 500;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Boutons d'authentification */
.auth-button {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border: none;
    border-radius: 0.75rem;
    padding: 0.9rem 1.4rem;
    font-size: 0.95rem;
    font-weight: 600;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgb(0 102 255 / 30%);
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgb(0 102 255 / 40%);
}

.auth-button:active {
    transform: translateY(0);
}

.auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 15px rgb(0 102 255 / 20%);
}

.auth-button.loading {
    pointer-events: none;
}

.auth-button .loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgb(255 255 255 / 30%);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Liens d'authentification */
.auth-links {
    text-align: center;
    margin-top: 1.2rem;
    padding-top: 1rem;
    border-top: 1px solid rgb(255 255 255 / 10%);
}

.auth-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.auth-link:hover {
    color: var(--secondary-blue);
    text-decoration: underline;
}

/* Options supplémentaires */
.auth-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
    font-size: 0.85rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgb(255 255 255 / 80%);
}

.remember-me input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-blue);
}

.forgot-password {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--secondary-blue);
}

/* Responsive Design */
@media (width <= 1024px) {
    .auth-card {
        max-width: 550px;
        min-width: 400px;
    }
}

@media (width <= 768px) {
    .auth-container {
        padding: 0.8rem;
    }

    .auth-card {
        padding: 1.5rem 1.2rem;
        margin: 0;
        border-radius: 1rem;
        min-width: auto;
        max-width: 100%;
    }

    .auth-header {
        margin-bottom: 1.2rem;
    }

    .auth-title {
        font-size: 1.4rem;
    }

    .auth-logo {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
    }

    .auth-form {
        gap: 0.9rem;
    }

    .auth-options {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (width <= 480px) {
    .auth-container {
        padding: 0.5rem;
        width: 100vw;
        height: 100vh;
    }

    .auth-card {
        padding: 1.2rem 0.8rem;
        min-width: auto;
        width: 100%;
        max-width: calc(100vw - 1rem);
    }

    .auth-header {
        margin-bottom: 1rem;
    }

    .auth-logo {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        margin-bottom: 0.6rem;
    }

    .auth-title {
        font-size: 1.3rem;
    }

    .auth-form {
        gap: 0.8rem;
    }

    .form-input {
        padding: 0.6rem;
        font-size: 0.85rem;
    }

    .auth-button {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* États de validation */
.form-group.valid .form-input {
    border-color: #2ed573;
    background: rgb(46 213 115 / 10%);
}

.form-group.valid .form-input:focus {
    box-shadow: 0 0 0 3px rgb(46 213 115 / 10%);
}

/* Animation d'entrée */
.auth-card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
