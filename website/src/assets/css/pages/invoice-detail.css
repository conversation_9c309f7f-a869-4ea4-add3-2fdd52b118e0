/* Invoice Detail Page Styles */
#invoice-detail {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.invoice-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
}

.header-content {
  flex: 1;
}

.back-btn {
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateX(-5px);
}

.invoice-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.invoice-icon-large {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 16px rgb(16 185 129 / 30%);
}

.invoice-info h1 {
  margin: 0 0 0.5rem;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
}

.invoice-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.invoice-date {
  background: rgb(255 255 255 / 10%);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border: 1px solid var(--glass-border);
}

.invoice-actions {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

/* Status Classes */
.status-paid {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-unpaid {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-cancelled {
  background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-overdue {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner,
.error-message {
  text-align: center;
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
}

.loading-spinner i {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.error-message i {
  font-size: 2rem;
  color: #EF4444;
  margin-bottom: 1rem;
}

/* Invoice Content */
.invoice-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.summary-card,
.items-card,
.history-card {
  background: var(--glass-bg);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px 0 rgb(0 102 255 / 15%);
  transition: all 0.3s ease;
}

.summary-card:hover,
.items-card:hover,
.history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgb(0 102 255 / 20%);
}

.summary-card h2,
.items-card h2,
.history-card h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.summary-card h2 i,
.items-card h2 i,
.history-card h2 i {
  color: var(--primary);
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.summary-item span {
  font-weight: 500;
  color: var(--text-primary);
}

.invoice-number {
  font-family: 'Courier New', monospace;
  background: rgb(0 102 255 / 10%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  color: var(--primary);
  border: 1px solid rgb(0 102 255 / 20%);
}

.total-amount {
  font-size: 1.25rem;
  font-weight: 600;
  color: #10B981;
}

/* Items Table */
.items-table {
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--glass-border);
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 80px 120px 120px;
  gap: 1rem;
  padding: 1rem;
  background: rgb(255 255 255 / 10%);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--glass-border);
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 80px 120px 120px;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--glass-border);
  transition: all 0.3s ease;
}

.table-row:hover {
  background: rgb(255 255 255 / 5%);
}

.table-row:last-child {
  border-bottom: none;
}

.col-description {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-name {
  font-weight: 500;
  color: var(--text-primary);
}

.item-details {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.col-period,
.col-quantity,
.col-price,
.col-total {
  display: flex;
  align-items: center;
  color: var(--text-primary);
}

.col-total {
  font-weight: 600;
}

/* Table Footer */
.table-footer {
  background: rgb(255 255 255 / 10%);
  padding: 1rem;
  border-top: 1px solid var(--glass-border);
}

.footer-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.footer-row:not(:last-child) {
  border-bottom: 1px solid var(--glass-border);
}

.total-row {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.total-row .footer-value {
  color: #10B981;
}

/* Payment History */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  border: 1px solid var(--glass-border);
  transition: all 0.3s ease;
}

.history-item:hover {
  background: rgb(255 255 255 / 8%);
  transform: translateX(5px);
}

.payment-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.payment-details {
  flex: 1;
}

.payment-amount {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.payment-method {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.payment-date {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.payment-status {
  flex-shrink: 0;
}

.payment-completed {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.payment-pending {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.payment-failed {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive */
@media (width <= 768px) {
  #invoice-detail {
    padding: 1rem;
  }

  .invoice-detail-header {
    flex-direction: column;
    gap: 1rem;
  }

  .invoice-actions {
    width: 100%;
    justify-content: stretch;
  }

  .invoice-actions .btn {
    flex: 1;
  }

  .invoice-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-header {
    display: none;
  }

  .table-row {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .col-description,
  .col-period,
  .col-quantity,
  .col-price,
  .col-total {
    width: 100%;
  }

  .col-period::before {
    content: "Période: ";
    font-weight: 600;
  }

  .col-quantity::before {
    content: "Quantité: ";
    font-weight: 600;
  }

  .col-price::before {
    content: "Prix unitaire: ";
    font-weight: 600;
  }

  .col-total::before {
    content: "Total: ";
    font-weight: 600;
  }
}
