/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: none;
    z-index: 1050;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgb(0 0 0 / 80%);
    backdrop-filter: blur(10px);
    z-index: 1051;
}

/* Modal Content */
.modal-content {
    position: relative;
    background: var(--glass-bg);
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 40px rgb(0 0 0 / 30%);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    z-index: 1052;
}

/* Modal Sizes */
.modal-sm .modal-content {
    max-width: 300px;
}

.modal-lg .modal-content {
    max-width: 800px;
}

.modal-xl .modal-content {
    max-width: 1140px;
}

.modal-fullscreen .modal-content {
    width: 100%;
    max-width: none;
    height: 100%;
    margin: 0;
}

/* Modal Header */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--body-bg);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    padding: 0.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-primary);
}

/* Modal Body avec Scrollbar Stylisée */
.modal-content .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(90vh - 140px);
    background: var(--glass-bg);
    margin: 0;
    scrollbar-width: thin;
    scrollbar-color: var(--primary) rgb(255 255 255 / 10%);
}

/* Webkit Scrollbar */
.modal-content .modal-body::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
}

.modal-content .modal-body::-webkit-scrollbar-track {
    background: rgb(255 255 255 / 10%);
    border-radius: 10px;
    margin: 0.5rem;
}

.modal-content .modal-body::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 10px;
    border: 2px solid var(--glass-bg);
}

.modal-content .modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* Firefox Scrollbar */
.modal-content .modal-body {
    scrollbar-width: thin;
    scrollbar-color: var(--primary) rgb(255 255 255 / 10%);
}

/* Modal Footer */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.25rem;
    border-top: 1px solid var(--border-color);
    background: var(--body-bg);
}

/* Form Elements in Modal */
.modal .form-group {
    margin-bottom: 1.25rem;
}

.modal .form-input,
.modal .form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--input-bg);
    color: var(--text-primary);
}

.modal .form-input:focus,
.modal .form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgb(var(--primary-rgb), 0.25);
}

.modal .form-switch {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 1rem 0;
}

.modal .form-switch-input {
    appearance: none;
    width: 3rem;
    height: 1.5rem;
    background: var(--border-color);
    border-radius: 1rem;
    position: relative;
    cursor: pointer;
    transition: background var(--transition-fast);
}

.modal .form-switch-input:checked {
    background: var(--primary);
}

.modal .form-switch-input::before {
    content: '';
    position: absolute;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: white;
    top: 0.125rem;
    left: 0.125rem;
    transition: transform var(--transition-fast);
}

.modal .form-switch-input:checked::before {
    transform: translateX(1.5rem);
}

.modal .form-switch-label {
    color: var(--text-primary);
    user-select: none;
    cursor: pointer;
}

/* Form Row */
.modal .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.modal .col-8 {
    flex: 0 0 66.6667%;
}

.modal .col-4 {
    flex: 0 0 33.3333%;
}

/* Form Text */
.modal .form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}
