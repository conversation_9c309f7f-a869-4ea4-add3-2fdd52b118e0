/* Realtime Stats Card - Glassmorphism Design */

.realtime-stats-card {
  position: relative;
  background: rgb(255 255 255 / 10%);
  backdrop-filter: blur(20px);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

.realtime-stats-card:hover {
  background: rgb(255 255 255 / 15%);
  border-color: rgb(255 255 255 / 30%);
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgb(0 0 0 / 10%);
}

/* États de mise à jour */
.realtime-stats-card.updating {
  background: rgb(74 144 226 / 10%);
  border-color: rgb(74 144 226 / 30%);
  box-shadow: 0 0 20px rgb(74 144 226 / 20%);
}

.realtime-stats-card.has-change {
  animation: cardPulse 0.6s ease-out;
}

.realtime-stats-card.positive-change {
  border-color: rgb(34 197 94 / 40%);
  box-shadow: 0 0 15px rgb(34 197 94 / 20%);
}

.realtime-stats-card.negative-change {
  border-color: rgb(239 68 68 / 40%);
  box-shadow: 0 0 15px rgb(239 68 68 / 20%);
}

@keyframes cardPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Update Indicator */
.update-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--primary-color);
  font-size: 12px;
  z-index: 2;
}

.update-pulse {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.update-indicator i {
  font-size: 12px;
}

/* Content Layout */
.stat-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

/* Icon Section */
.stat-icon-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgb(255 255 255 / 10%);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.realtime-stats-card:hover .stat-icon {
  background: rgb(255 255 255 / 20%);
  border-color: rgb(255 255 255 / 30%);
  transform: scale(1.05);
}

.stat-icon i {
  font-size: 20px;
  color: var(--primary-color);
}

/* Change Indicator */
.change-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  animation: fadeInUp 0.5s ease;
}

.positive-change .change-indicator {
  background: rgb(34 197 94 / 20%);
  color: #22c55e;
  border: 1px solid rgb(34 197 94 / 30%);
}

.negative-change .change-indicator {
  background: rgb(239 68 68 / 20%);
  color: #ef4444;
  border: 1px solid rgb(239 68 68 / 30%);
}

.change-indicator i {
  font-size: 8px;
}

.change-value {
  font-size: 9px;
  font-weight: 500;
}

/* Stats Data */
.stat-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-number {
  margin-bottom: 8px;
  overflow: hidden;
}

.number-value {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-subtitle {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 8px;
  line-height: 1.3;
}

.last-update {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: var(--text-muted);
  margin-top: auto;
}

.last-update i {
  font-size: 9px;
}

/* Progress Section */
.progress-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgb(255 255 255 / 10%);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgb(255 255 255 / 10%);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 3px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 30%), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 11px;
  color: var(--text-muted);
  text-align: center;
  font-weight: 500;
}

/* Action Button */
.card-action {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgb(255 255 255 / 10%);
}

.action-btn {
  width: 100%;
  padding: 8px 16px;
  background: rgb(255 255 255 / 10%);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.action-btn:hover {
  background: rgb(255 255 255 / 20%);
  border-color: rgb(255 255 255 / 30%);
  transform: translateY(-1px);
}

.action-btn i {
  font-size: 11px;
}

/* Animations */
.number-change-enter-active,
.number-change-leave-active {
  transition: all 0.4s ease;
}

.number-change-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.8);
}

.number-change-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (width <= 768px) {
  .realtime-stats-card {
    padding: 20px;
    min-height: 120px;
  }
  
  .stat-content {
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .stat-icon i {
    font-size: 18px;
  }
  
  .number-value {
    font-size: 24px;
  }
  
  .stat-label {
    font-size: 13px;
  }
}

@media (width <= 480px) {
  .realtime-stats-card {
    padding: 16px;
  }
  
  .stat-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }
  
  .stat-icon-section {
    flex-direction: row;
    align-items: center;
  }
  
  .number-value {
    font-size: 22px;
  }
}
