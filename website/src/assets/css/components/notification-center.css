/* Notification Center Styles - Glassmorphism Design */

.notification-center {
  position: relative;
  display: inline-block;
}

/* <PERSON>gger <PERSON> */
.notification-trigger {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgb(255 255 255 / 10%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-primary);
}

.notification-trigger:hover {
  background: rgb(255 255 255 / 15%);
  border-color: rgb(255 255 255 / 30%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgb(0 0 0 / 15%);
}

.notification-trigger.active {
  background: rgb(74 144 226 / 20%);
  border-color: rgb(74 144 226 / 40%);
  color: var(--primary-color);
}

.notification-trigger i {
  font-size: 18px;
}

/* Badge */
.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  border: 2px solid rgb(255 255 255 / 90%);
  animation: fadeIn 0.3s ease;
}

.notification-badge.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Dropdown */
.notification-dropdown {
  position: absolute;
  top: 50px;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: rgb(255 255 255 / 95%);
  backdrop-filter: blur(20px);
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
  z-index: 1000;
  overflow: hidden;
}

.notification-dropdown-enter-active,
.notification-dropdown-leave-active {
  transition: all 0.3s ease;
}

.notification-dropdown-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.notification-dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* Header */
.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgb(255 255 255 / 20%);
  background: rgb(255 255 255 / 10%);
}

.notification-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.btn-mark-all-read,
.btn-clear-all {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgb(255 255 255 / 10%);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.btn-mark-all-read:hover {
  background: rgb(34 197 94 / 20%);
  border-color: rgb(34 197 94 / 40%);
  color: #22c55e;
}

.btn-clear-all:hover {
  background: rgb(239 68 68 / 20%);
  border-color: rgb(239 68 68 / 40%);
  color: #ef4444;
}

/* Notifications List */
.notification-list {
  max-height: 350px;
  overflow-y: auto;
  padding: 8px 0;
}

.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: rgb(255 255 255 / 10%);
}

.notification-list::-webkit-scrollbar-thumb {
  background: rgb(255 255 255 / 30%);
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: rgb(255 255 255 / 50%);
}

/* No Notifications */
.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.no-notifications i {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.no-notifications p {
  margin: 0;
  font-size: 14px;
}

/* Notification Item */
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background: rgb(255 255 255 / 10%);
}

.notification-item.clickable {
  cursor: pointer;
}

.notification-item.unread {
  background: rgb(74 144 226 / 5%);
  border-left: 3px solid var(--primary-color);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
}

/* Notification Icon */
.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgb(255 255 255 / 10%);
  border-radius: 10px;
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-icon i {
  font-size: 16px;
}

.text-blue { color: #3b82f6; }
.text-orange { color: #f59e0b; }
.text-green { color: #10b981; }
.text-gray { color: #6b7280; }

/* Notification Content */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 11px;
  color: var(--text-muted);
  font-weight: 500;
}

/* Notification Actions */
.notification-actions-item {
  display: flex;
  gap: 4px;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions-item {
  opacity: 1;
}

.btn-mark-read,
.btn-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgb(255 255 255 / 10%);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  font-size: 10px;
}

.btn-mark-read:hover {
  background: rgb(34 197 94 / 20%);
  border-color: rgb(34 197 94 / 40%);
  color: #22c55e;
}

.btn-remove:hover {
  background: rgb(239 68 68 / 20%);
  border-color: rgb(239 68 68 / 40%);
  color: #ef4444;
}

/* Footer */
.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid rgb(255 255 255 / 20%);
  background: rgb(255 255 255 / 5%);
}

.btn-view-all {
  width: 100%;
  padding: 8px 16px;
  background: rgb(74 144 226 / 10%);
  border: 1px solid rgb(74 144 226 / 30%);
  border-radius: 8px;
  color: var(--primary-color);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-view-all:hover {
  background: rgb(74 144 226 / 20%);
  border-color: rgb(74 144 226 / 40%);
}

/* Overlay */
.notification-overlay {
  position: fixed;
  inset: 0;
  z-index: 999;
  background: transparent;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive */
@media (width <= 768px) {
  .notification-dropdown {
    width: 320px;
    right: -20px;
  }
  
  .notification-item {
    padding: 10px 16px;
  }
  
  .notification-header {
    padding: 14px 16px;
  }
}
