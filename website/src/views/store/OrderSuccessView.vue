<template>
  <div class="order-success-view">
    <div class="container">
      <!-- État de chargement -->
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <h2>Chargement de votre commande...</h2>
      </div>

      <!-- Erreur -->
      <div v-else-if="error" class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <h2>Commande introuvable</h2>
        <p>{{ error }}</p>
        <router-link to="/pricing" class="btn btn-primary">
          Retour aux licences
        </router-link>
      </div>

      <!-- Succès -->
      <div v-else-if="order" class="success-content">
        <!-- En-tête de succès -->
        <div class="success-header">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h1 class="success-title">Commande confirmée !</h1>
          <p class="success-subtitle">
            Merci pour votre achat. Votre commande a été traitée avec succès.
          </p>
        </div>

        <!-- Informations de commande -->
        <div class="order-info">
          <div class="info-grid">
            <div class="info-item">
              <strong>Numéro de commande</strong>
              <span class="order-number">{{ order.order_number }}</span>
            </div>
            <div class="info-item">
              <strong>Date</strong>
              <span>{{ formatDate(order.created_at) }}</span>
            </div>
            <div class="info-item">
              <strong>Statut</strong>
              <span class="status" :class="order.status">{{ getStatusLabel(order.status) }}</span>
            </div>
            <div class="info-item">
              <strong>Total</strong>
              <span class="total">{{ formatPrice(order.total) }}</span>
            </div>
          </div>
        </div>

        <!-- Articles commandés -->
        <div class="order-items">
          <h2>Articles commandés</h2>
          <div class="items-list">
            <div 
              v-for="item in order.items" 
              :key="item.id"
              class="order-item"
            >
              <div class="item-info">
                <h3 class="item-name">{{ item.template?.name || 'Template' }}</h3>
                <p class="item-description">{{ item.template?.description || '' }}</p>

                <div class="item-specs">
                  <span class="spec">
                    <i class="fas fa-globe"></i>
                    {{ item.template?.domain_limit || 0 }} domaine(s)
                  </span>
                  <span class="spec">
                    <i class="fas fa-server"></i>
                    {{ item.template?.installation_limit || 0 }} installation(s)
                  </span>
                  <span v-if="item.template?.update_permissions" class="spec">
                    <i class="fas fa-sync"></i>
                    Mises à jour incluses
                  </span>
                </div>
              </div>
              
              <div class="item-pricing">
                <div class="quantity">Quantité: {{ item.quantity }}</div>
                <div class="unit-price">{{ formatPrice(item.unit_price) }} / unité</div>
                <div class="total-price">{{ formatPrice(item.total_price) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Prochaines étapes -->
        <div class="next-steps">
          <h2>Prochaines étapes</h2>
          <div class="steps-list">
            <div class="step-item">
              <div class="step-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <div class="step-content">
                <h3>Confirmation par email</h3>
                <p>Un email de confirmation a été envoyé à <strong>{{ order.billing_address.email }}</strong></p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-icon">
                <i class="fas fa-key"></i>
              </div>
              <div class="step-content">
                <h3>Activation de licence</h3>
                <p>Vos licences seront activées automatiquement après confirmation du paiement</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-icon">
                <i class="fas fa-download"></i>
              </div>
              <div class="step-content">
                <h3>Téléchargement</h3>
                <p>Vous pourrez télécharger TechCMS depuis votre espace client</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="success-actions">
          <router-link to="/client/login" class="btn btn-primary">
            <i class="fas fa-user"></i>
            Accéder à mon espace client
          </router-link>
          
          <router-link to="/pricing" class="btn btn-outline">
            <i class="fas fa-shopping-cart"></i>
            Continuer mes achats
          </router-link>
          
          <button class="btn btn-outline" @click="printOrder">
            <i class="fas fa-print"></i>
            Imprimer la commande
          </button>
        </div>

        <!-- Support -->
        <div class="support-info">
          <div class="support-card">
            <i class="fas fa-headset"></i>
            <div>
              <h3>Besoin d'aide ?</h3>
              <p>Notre équipe support est là pour vous accompagner</p>
              <router-link to="/contact" class="support-link">
                Contacter le support
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import orderService from '@/services/orderService'
import logger from '@/services/logger'
import type { Order } from '@/types/order'
import { ORDER_STATUSES } from '@/types/order'

// Composables
const route = useRoute()

// État
const loading = ref(true)
const error = ref<string | null>(null)
const order = ref<Order | null>(null)

// Méthodes
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const formatDate = (dateString: string): string => {
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString))
}

const getStatusLabel = (status: string): string => {
  const statusObj = ORDER_STATUSES.find(s => s.value === status)
  return statusObj?.label || status
}

const printOrder = () => {
  window.print()
}

const loadOrder = async () => {
  const orderNumber = route.params.orderNumber as string
  
  if (!orderNumber) {
    error.value = 'Numéro de commande manquant'
    loading.value = false
    return
  }
  
  try {
    order.value = await orderService.getOrderByNumber(orderNumber)

    logger.info('[OrderSuccessView] Commande chargée', {
      order_number: orderNumber,
      order_id: order.value.id,
      order_structure: order.value,
      items_structure: order.value.items?.map(item => ({
        id: item.id,
        template_name: item.template?.name,
        template_description: item.template?.description,
        domain_limit: item.template?.domain_limit,
        installation_limit: item.template?.installation_limit,
        update_permissions: item.template?.update_permissions,
        has_template_object: !!item.template,
        template_object: item.template
      }))
    })
  } catch (err: any) {
    error.value = err.message || 'Erreur lors du chargement de la commande'
    logger.error('[OrderSuccessView] Erreur chargement commande', {
      order_number: orderNumber,
      error: err.message
    })
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadOrder()
  logger.info('[OrderSuccessView] Page succès commande montée')
})
</script>

<style scoped>
.order-success-view {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2rem 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.loading-state i,
.error-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.loading-state i {
  color: var(--color-primary);
}

.error-state i {
  color: var(--color-danger);
}

.success-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.success-header {
  text-align: center;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 3rem 2rem;
}

.success-icon {
  font-size: 4rem;
  color: var(--color-success);
  margin-bottom: 1rem;
}

.success-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.success-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.order-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item strong {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.order-number {
  font-family: monospace;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-primary);
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status.completed {
  background: rgb(34 197 94 / 10%);
  color: var(--color-success);
}

.status.pending {
  background: rgb(251 191 36 / 10%);
  color: var(--color-warning);
}

.total {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.order-items {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.order-items h2 {
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  gap: 2rem;
}

.item-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.item-description {
  color: var(--text-secondary);
  margin: 0 0 1rem;
}

.item-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.spec {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.spec i {
  color: var(--color-primary);
}

.item-pricing {
  text-align: right;
  min-width: 150px;
}

.quantity {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.unit-price {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.total-price {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.next-steps {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.next-steps h2 {
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: rgb(59 130 246 / 10%);
  border-radius: 50%;
  color: var(--color-primary);
  font-size: 1.25rem;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: 1rem;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.step-content p {
  color: var(--text-secondary);
  margin: 0;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.support-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.support-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.support-card i {
  font-size: 2rem;
  color: var(--color-primary);
  margin-top: 0.25rem;
}

.support-card h3 {
  font-size: 1.125rem;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.support-card p {
  color: var(--text-secondary);
  margin: 0 0 1rem;
}

.support-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
}

.support-link:hover {
  text-decoration: underline;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
}

@media (width <= 768px) {
  .order-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .item-pricing {
    text-align: left;
  }
  
  .success-actions {
    flex-direction: column;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media print {
  .success-actions,
  .support-info {
    display: none;
  }
}
</style>
