<template>
  <div class="cart-view">
    <!-- En-tête -->
    <div class="cart-header">
      <div class="container">
        <h1 class="page-title">
          <i class="fas fa-shopping-cart"></i>
          Mon panier
        </h1>
        <p class="page-description">
          Vérifiez vos articles avant de procéder au paiement
        </p>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="cart-content">
      <div class="container">
        <!-- Panier vide -->
        <div v-if="cartStore.isEmpty" class="empty-cart">
          <i class="fas fa-shopping-cart"></i>
          <h3>Votre panier est vide</h3>
          <p>Découvrez nos licences TechCMS et ajoutez-les à votre panier</p>
          <router-link to="/pricing" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i>
            Voir nos licences
          </router-link>
        </div>

        <!-- Panier avec articles -->
        <div v-else class="cart-grid">
          <!-- Liste des articles -->
          <div class="cart-items">
            <div class="cart-section-title">
              <h2>Articles dans votre panier ({{ cartStore.itemCount }})</h2>
            </div>

            <div 
              v-for="item in cartStore.items" 
              :key="item.id"
              class="cart-item"
            >
              <div class="item-info">
                <div class="item-header">
                  <h3 class="item-name">{{ item.template.name }}</h3>
                  <button 
                    class="remove-btn"
                    :title="$t('common.remove')"
                    @click="removeItem(item.id)"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                
                <div class="item-details">
                  <p class="item-description">{{ item.template.description }}</p>
                  
                  <div class="item-specs">
                    <span class="spec">
                      <i class="fas fa-globe"></i>
                      {{ item.template.domain_limit }} domaine(s)
                    </span>
                    <span class="spec">
                      <i class="fas fa-server"></i>
                      {{ item.template.installation_limit }} installation(s)
                    </span>
                    <span v-if="item.template.update_permissions" class="spec">
                      <i class="fas fa-sync"></i>
                      Mises à jour incluses
                    </span>
                  </div>

                  <!-- Customizations -->
                  <div v-if="Object.keys(item.customizations).length > 0" class="item-customizations">
                    <h4>Options supplémentaires :</h4>
                    <div v-if="item.customizations.extra_domains" class="customization">
                      <i class="fas fa-plus"></i>
                      {{ item.customizations.extra_domains }} domaine(s) supplémentaire(s)
                    </div>
                    <div v-if="item.customizations.extra_installations" class="customization">
                      <i class="fas fa-plus"></i>
                      {{ item.customizations.extra_installations }} installation(s) supplémentaire(s)
                    </div>
                  </div>
                </div>
              </div>

              <div class="item-actions">
                <div class="quantity-controls">
                  <label>Quantité :</label>
                  <div class="quantity-input">
                    <button 
                      :disabled="item.quantity <= 1"
                      class="qty-btn"
                      @click="updateQuantity(item.id, item.quantity - 1)"
                    >
                      <i class="fas fa-minus"></i>
                    </button>
                    <input 
                      :value="item.quantity"
                      type="number"
                      min="1"
                      class="qty-input"
                      @input="updateQuantity(item.id, parseInt(($event.target as HTMLInputElement)?.value) || 1)"
                    />
                    <button 
                      class="qty-btn"
                      @click="updateQuantity(item.id, item.quantity + 1)"
                    >
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                </div>

                <div class="item-price">
                  <div class="unit-price">
                    {{ formatPrice(item.template.price) }} / unité
                  </div>
                  <div class="total-price">
                    {{ formatPrice(item.subtotal) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Résumé de commande -->
          <div class="cart-summary">
            <div class="summary-card">
              <h3 class="summary-title">Résumé de la commande</h3>
              
              <div class="summary-line">
                <span>Sous-total</span>
                <span>{{ formatPrice(cartStore.subtotal) }}</span>
              </div>
              
              <div class="summary-line">
                <span>TVA (20%)</span>
                <span>{{ formatPrice(cartStore.taxAmount) }}</span>
              </div>
              
              <div class="summary-line total-line">
                <span>Total</span>
                <span>{{ formatPrice(cartStore.total) }}</span>
              </div>

              <div class="summary-actions">
                <button 
                  class="btn btn-outline btn-sm"
                  @click="clearCart"
                >
                  <i class="fas fa-trash"></i>
                  Vider le panier
                </button>
                
                <router-link 
                  to="/store/checkout"
                  class="btn btn-primary btn-block"
                >
                  <i class="fas fa-credit-card"></i>
                  Procéder au paiement
                </router-link>
              </div>
            </div>

            <!-- Informations supplémentaires -->
            <div class="info-card">
              <h4><i class="fas fa-shield-alt"></i> Paiement sécurisé</h4>
              <p>Vos données de paiement sont protégées par un cryptage SSL.</p>
              
              <h4><i class="fas fa-sync"></i> Mises à jour</h4>
              <p>Accès aux mises à jour selon les conditions de votre licence.</p>
              
              <h4><i class="fas fa-headset"></i> Support</h4>
              <p>Support technique inclus avec toutes nos licences.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCartStore } from '@/stores/cart'
// import { useI18n } from 'vue-i18n'
import logger from '@/services/logger'

// Composables
// const { t } = useI18n()
const cartStore = useCartStore()

// Méthodes
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const updateQuantity = (itemId: string, quantity: number) => {
  if (quantity >= 1) {
    cartStore.updateQuantity(itemId, quantity)
  }
}

const removeItem = (itemId: string) => {
  if (confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
    cartStore.removeItem(itemId)
  }
}

const clearCart = () => {
  if (confirm('Êtes-vous sûr de vouloir vider votre panier ?')) {
    cartStore.clearCart()
  }
}

// Lifecycle
logger.info('[CartView] Page panier montée')
</script>

<style scoped>
.cart-view {
  min-height: 100vh;
  background: var(--bg-primary);
}

.cart-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem 0;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-description {
  color: var(--text-secondary);
  margin: 0;
}

.cart-content {
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Panier vide */
.empty-cart {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.empty-cart i {
  font-size: 4rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.empty-cart h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.empty-cart p {
  color: var(--text-secondary);
  margin: 0 0 2rem;
}

/* Grille panier */
.cart-grid {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
}

.cart-section-title h2 {
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
}

/* Articles */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cart-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.item-info {
  flex: 1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.item-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.remove-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;
}

.remove-btn:hover {
  background: var(--bg-tertiary);
  color: var(--color-danger);
}

.item-description {
  color: var(--text-secondary);
  margin: 0 0 1rem;
}

.item-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.spec {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.spec i {
  color: var(--color-primary);
}

.item-customizations {
  background: var(--bg-tertiary);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.item-customizations h4 {
  font-size: 0.875rem;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.customization {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.customization i {
  color: var(--color-success);
}

/* Actions article */
.item-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  min-width: 200px;
}

.quantity-controls label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.quantity-input {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.qty-btn {
  background: var(--bg-tertiary);
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-primary);
  transition: background 0.2s;
}

.qty-btn:hover:not(:disabled) {
  background: var(--bg-quaternary);
}

.qty-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.qty-input {
  border: none;
  background: var(--bg-primary);
  color: var(--text-primary);
  text-align: center;
  width: 60px;
  padding: 0.5rem;
}

.item-price {
  text-align: right;
}

.unit-price {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.total-price {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Résumé */
.cart-summary {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.summary-card,
.info-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
}

.summary-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-line:last-of-type {
  border-bottom: none;
}

.total-line {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-primary);
  border-top: 2px solid var(--border-color);
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.summary-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.info-card h4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.info-card h4:not(:first-child) {
  margin-top: 1rem;
}

.info-card p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.info-card i {
  color: var(--color-primary);
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
}

.btn-block {
  width: 100%;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

/* Responsive */
@media (width <= 768px) {
  .cart-grid {
    grid-template-columns: 1fr;
  }
  
  .cart-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .item-actions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-width: auto;
  }
  
  .quantity-controls {
    flex: 1;
  }
}
</style>
