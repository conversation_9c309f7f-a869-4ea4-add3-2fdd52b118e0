<template>
  <div class="order-failure-view">
    <div class="container">
      <div class="failure-content">
        <!-- En-tête d'échec -->
        <div class="failure-header">
          <div class="failure-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h1 class="failure-title">Échec du paiement</h1>
          <p class="failure-subtitle">
            Nous n'avons pas pu traiter votre paiement. Votre commande n'a pas été finalisée.
          </p>
        </div>

        <!-- Informations sur l'erreur -->
        <div v-if="errorMessage" class="error-info">
          <h2>Détails de l'erreur</h2>
          <div class="error-message">
            <i class="fas fa-info-circle"></i>
            <span>{{ errorMessage }}</span>
          </div>
        </div>

        <!-- Raisons possibles -->
        <div class="possible-reasons">
          <h2>Raisons possibles</h2>
          <div class="reasons-list">
            <div class="reason-item">
              <i class="fas fa-credit-card"></i>
              <div>
                <strong>Carte bancaire</strong>
                <p>Fonds insuffisants, carte expirée ou bloquée</p>
              </div>
            </div>
            
            <div class="reason-item">
              <i class="fas fa-shield-alt"></i>
              <div>
                <strong>Sécurité</strong>
                <p>Transaction bloquée par votre banque pour sécurité</p>
              </div>
            </div>
            
            <div class="reason-item">
              <i class="fas fa-wifi"></i>
              <div>
                <strong>Connexion</strong>
                <p>Problème de connexion internet pendant le paiement</p>
              </div>
            </div>
            
            <div class="reason-item">
              <i class="fas fa-clock"></i>
              <div>
                <strong>Délai dépassé</strong>
                <p>Session de paiement expirée</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Solutions -->
        <div class="solutions">
          <h2>Que faire maintenant ?</h2>
          <div class="solutions-list">
            <div class="solution-item">
              <div class="solution-number">1</div>
              <div class="solution-content">
                <h3>Vérifiez vos informations</h3>
                <p>Assurez-vous que les informations de votre carte bancaire sont correctes</p>
              </div>
            </div>
            
            <div class="solution-item">
              <div class="solution-number">2</div>
              <div class="solution-content">
                <h3>Contactez votre banque</h3>
                <p>Vérifiez que votre carte n'est pas bloquée pour les paiements en ligne</p>
              </div>
            </div>
            
            <div class="solution-item">
              <div class="solution-number">3</div>
              <div class="solution-content">
                <h3>Essayez une autre méthode</h3>
                <p>Utilisez une autre carte bancaire ou PayPal</p>
              </div>
            </div>
            
            <div class="solution-item">
              <div class="solution-number">4</div>
              <div class="solution-content">
                <h3>Réessayez plus tard</h3>
                <p>Le problème peut être temporaire</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="failure-actions">
          <router-link to="/store/checkout" class="btn btn-primary">
            <i class="fas fa-redo"></i>
            Réessayer le paiement
          </router-link>
          
          <router-link to="/store/cart" class="btn btn-outline">
            <i class="fas fa-shopping-cart"></i>
            Retour au panier
          </router-link>
          
          <router-link to="/contact" class="btn btn-outline">
            <i class="fas fa-headset"></i>
            Contacter le support
          </router-link>
        </div>

        <!-- Informations de contact -->
        <div class="contact-info">
          <div class="contact-card">
            <i class="fas fa-headset"></i>
            <div>
              <h3>Besoin d'aide ?</h3>
              <p>Notre équipe support est disponible pour vous aider à résoudre ce problème</p>
              <div class="contact-methods">
                <a href="mailto:<EMAIL>" class="contact-method">
                  <i class="fas fa-envelope"></i>
                  <EMAIL>
                </a>
                <span class="contact-method">
                  <i class="fas fa-clock"></i>
                  Lun-Ven 9h-18h
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Sécurité -->
        <div class="security-notice">
          <div class="security-item">
            <i class="fas fa-lock"></i>
            <div>
              <strong>Vos données sont sécurisées</strong>
              <p>Aucune information bancaire n'a été stockée suite à cet échec</p>
            </div>
          </div>
          
          <div class="security-item">
            <i class="fas fa-shield-alt"></i>
            <div>
              <strong>Aucun prélèvement</strong>
              <p>Votre compte n'a pas été débité</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import logger from '@/services/logger'

// Composables
const route = useRoute()

// État
const errorMessage = ref<string>('')

// Lifecycle
onMounted(() => {
  // Récupérer le message d'erreur depuis les query params
  errorMessage.value = (route.query.error as string) || ''
  
  logger.info('[OrderFailureView] Page échec commande montée', {
    error: errorMessage.value
  })
})
</script>

<style scoped>
.order-failure-view {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2rem 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.failure-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.failure-header {
  text-align: center;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 3rem 2rem;
}

.failure-icon {
  font-size: 4rem;
  color: var(--color-danger);
  margin-bottom: 1rem;
}

.failure-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.failure-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.error-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.error-info h2 {
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.error-message {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: rgb(239 68 68 / 10%);
  border: 1px solid rgb(239 68 68 / 20%);
  border-radius: 8px;
  color: var(--color-danger);
}

.error-message i {
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.possible-reasons,
.solutions {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.possible-reasons h2,
.solutions h2 {
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
}

.reasons-list,
.solutions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reason-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.reason-item i {
  color: var(--color-warning);
  font-size: 1.25rem;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.reason-item strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.reason-item p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.solution-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.solution-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.solution-content h3 {
  font-size: 1rem;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.solution-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.failure-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.contact-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.contact-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-card i {
  font-size: 2rem;
  color: var(--color-primary);
  margin-top: 0.25rem;
}

.contact-card h3 {
  font-size: 1.125rem;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.contact-card p {
  color: var(--text-secondary);
  margin: 0 0 1rem;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
}

.contact-method:hover {
  color: var(--color-primary);
}

.contact-method i {
  color: var(--color-primary);
  width: 1rem;
  text-align: center;
}

.security-notice {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.security-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.security-item i {
  color: var(--color-success);
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.security-item strong {
  display: block;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.security-item p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-dark);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
}

@media (width <= 768px) {
  .failure-actions {
    flex-direction: column;
  }
  
  .contact-methods {
    align-items: flex-start;
  }
  
  .security-notice {
    gap: 1.5rem;
  }
}
</style>
