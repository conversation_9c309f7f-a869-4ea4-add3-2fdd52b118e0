<template>
  <div class="pricing-view">
    <!-- Section Hero -->
    <section class="hero-section">
      <div class="hero-container">
        <h1 class="hero-title">{{ $t('pricing.hero.title') }}</h1>
        <p class="hero-description">
          {{ $t('pricing.hero.description') }}
        </p>
      </div>
    </section>

    <!-- Filtres de cycle de facturation (seulement si plusieurs cycles disponibles) -->
    <section v-if="availableBillingCycles.length > 1" class="billing-filter-section">
      <div class="container">
        <div class="billing-toggle">
          <button
            v-for="cycle in availableBillingCycles"
            :key="cycle.value"
            :class="['billing-btn', { 'active': selectedBillingCycle === cycle.value }]"
            @click="selectedBillingCycle = cycle.value"
          >
            {{ cycle.label }}
          </button>
        </div>
      </div>
    </section>

    <!-- Message d'erreur -->
    <div v-if="error" class="container">
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        {{ $t('pricing.error') }}
      </div>
    </div>

    <!-- Section Tarifs -->
    <section class="pricing-section">
      <div class="container">
        <div v-if="loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          <span>{{ $t('pricing.loading') }}</span>
        </div>

        <div v-else-if="!hasTemplates" class="empty-state">
          <i class="fas fa-box-open"></i>
          <h3>{{ $t('pricing.no_templates.title') }}</h3>
          <p>{{ $t('pricing.no_templates.description') }}</p>
        </div>

        <div v-else class="pricing-grid">
          <div 
            v-for="template in filteredTemplates" 
            :key="template.id"
            class="pricing-card"
            :class="{ 'featured': template.is_featured }"
          >
            <!-- Badge populaire -->
            <div v-if="template.is_featured" class="popular-badge">
              <i class="fas fa-star"></i>
              {{ $t('pricing.template_card.most_popular') }}
            </div>

            <!-- En-tête du tarif -->
            <div class="pricing-header">
              <h3 class="plan-name">{{ template.name }}</h3>
              <div class="plan-price">
                <span class="price">{{ formatPrice(template.price) }}</span>
                <span class="period">/ {{ getBillingCycleLabel(template.billing_cycle) }}</span>
              </div>
              <div v-if="template.setup_fee && template.setup_fee > 0" class="setup-fee">
                + {{ formatPrice(template.setup_fee) }} {{ $t('pricing.template_card.setup_fee') }}
              </div>
            </div>

            <!-- Description -->
            <div v-if="template.description" class="plan-description">
              {{ template.description }}
            </div>

            <!-- Caractéristiques principales -->
            <div class="plan-highlights">
              <div class="highlight-item">
                <i class="fas fa-globe"></i>
                <span>{{ template.domain_limit }} {{ template.domain_limit > 1 ? $t('pricing.template_card.domains_plural') : $t('pricing.template_card.domains') }}</span>
              </div>
              
              <div class="highlight-item">
                <i class="fas fa-download"></i>
                <span>{{ template.installation_limit }} {{ template.installation_limit > 1 ? $t('pricing.template_card.installations_plural') : $t('pricing.template_card.installations') }}</span>
              </div>
              
              <div v-if="template.update_permissions" class="highlight-item">
                <i class="fas fa-sync"></i>
                <span>{{ $t('pricing.template_card.updates') }}</span>
              </div>
            </div>

            <!-- Liste des fonctionnalités -->
            <div class="plan-features">
              <h4>{{ $t('pricing.template_card.features') }} :</h4>
              <ul>
                <li v-for="feature in template.features" :key="feature">
                  <i class="fas fa-check"></i>
                  {{ feature }}
                </li>
                <!-- Fonctionnalités communes à tous les plans -->
                <li>
                  <i class="fas fa-check"></i>
                  {{ $t('pricing.template_card.support') }}
                </li>
                <li>
                  <i class="fas fa-check"></i>
                  {{ $t('pricing.template_card.documentation') }}
                </li>
                <li>
                  <i class="fas fa-check"></i>
                  {{ $t('pricing.template_card.updates') }}
                </li>
              </ul>
            </div>

            <!-- Prix de renouvellement -->
            <div v-if="template.renewal_price && template.renewal_price !== template.price" class="renewal-info">
              <small>
                <i class="fas fa-info-circle"></i>
                {{ $t('pricing.template_card.renewal') }} : {{ formatPrice(template.renewal_price) }} / {{ getBillingCycleLabel(template.billing_cycle) }}
              </small>
            </div>

            <!-- Action -->
            <div class="plan-action">
              <button
                class="btn btn-primary btn-block"
                :disabled="addingToCart === template.id"
                @click="addToCart(template)"
              >
                <i v-if="addingToCart === template.id" class="fas fa-spinner fa-spin"></i>
                <i v-else class="fas fa-shopping-cart"></i>
                {{ addingToCart === template.id ? $t('pricing.template_card.adding') : $t('pricing.template_card.add_to_cart') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section FAQ Tarifs -->
    <section class="faq-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('pricing.faq.title') }}</h2>
          <p>{{ $t('pricing.faq.subtitle') }}</p>
        </div>
        
        <div class="faq-grid">
          <div class="faq-item">
            <h3>
              <i class="fas fa-question-circle"></i>
              {{ $t('pricing.faq.questions.change_plan.question') }}
            </h3>
            <p>
              {{ $t('pricing.faq.questions.change_plan.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>
              <i class="fas fa-question-circle"></i>
              {{ $t('pricing.faq.questions.limits.question') }}
            </h3>
            <p>
              {{ $t('pricing.faq.questions.limits.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>
              <i class="fas fa-question-circle"></i>
              {{ $t('pricing.faq.questions.refund.question') }}
            </h3>
            <p>
              {{ $t('pricing.faq.questions.refund.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>
              <i class="fas fa-question-circle"></i>
              {{ $t('pricing.faq.questions.support.question') }}
            </h3>
            <p>
              {{ $t('pricing.faq.questions.support.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>
              <i class="fas fa-question-circle"></i>
              {{ $t('pricing.faq.multi_domain.question') }}
            </h3>
            <p>
              {{ $t('pricing.faq.multi_domain.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>
              <i class="fas fa-question-circle"></i>
              {{ $t('pricing.faq.updates.question') }}
            </h3>
            <p>
              {{ $t('pricing.faq.updates.answer') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Section CTA -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>{{ $t('pricing.help.title') }}</h2>
          <p>{{ $t('pricing.help.subtitle') }}</p>
          <div class="cta-actions">
            <router-link to="/contact" class="btn btn-primary btn-lg">
              <i class="fas fa-envelope"></i>
              {{ $t('pricing.help.contact_us') }}
            </router-link>
            <a href="/store" class="btn btn-outline btn-lg">
              <i class="fas fa-shopping-cart"></i>
              {{ $t('pricing.help.view_store') }}
            </a>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import { ApiService } from '@/services/api'
import { BILLING_CYCLES } from '@/types/template'
import type { LicenseTemplate } from '@/types/template'
import logger from '@/services/logger'

// Composables
const router = useRouter()
const cartStore = useCartStore()

// État local
const templates = ref<LicenseTemplate[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const selectedBillingCycle = ref('annually')
const addingToCart = ref<number | null>(null)

// Computed
const hasTemplates = computed(() => templates.value.length > 0)

// Cycles de facturation disponibles (qui ont des templates)
const availableBillingCycles = computed(() => {
  const availableCycles = new Set(templates.value.map(t => t.billing_cycle))
  return BILLING_CYCLES.filter(cycle => availableCycles.has(cycle.value))
})

const filteredTemplates = computed(() => {
  return templates.value
    .filter(t => t.billing_cycle === selectedBillingCycle.value)
    .sort((a, b) => {
      // Mettre les templates mis en avant en premier
      if (a.is_featured && !b.is_featured) return -1
      if (!a.is_featured && b.is_featured) return 1
      // Puis trier par ordre d'affichage
      return a.sort_order - b.sort_order
    })
})

// Méthodes utilitaires
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const getBillingCycleLabel = (cycle: string) => {
  const cycleObj = BILLING_CYCLES.find(c => c.value === cycle)
  return cycleObj?.label.toLowerCase() || cycle
}

// Actions
const loadTemplates = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await ApiService.get('/api/v1/website/templates')

    if (response.templates) {
      // Convertir les données API pour le frontend
      templates.value = response.templates.map((template: any) => ({
        ...template,
        id: parseInt(template.id),
        price: parseFloat(template.price),
        setup_fee: parseFloat(template.setup_fee || '0'),
        renewal_price: parseFloat(template.renewal_price || '0'),
        domain_limit: parseInt(template.domain_limit),
        installation_limit: parseInt(template.installation_limit),
        sort_order: parseInt(template.sort_order),
        is_featured: Boolean(parseInt(template.is_featured)),
        update_permissions: Boolean(parseInt(template.update_permissions))
      }))

      logger.info('[PricingView] Templates chargés', {
        count: templates.value.length
      })
    } else {
      throw new Error('Aucun template disponible')
    }
  } catch (err: any) {
    error.value = err.message || 'Erreur lors du chargement des tarifs'
    logger.error('[PricingView] Erreur loadTemplates', { error: err.message })
  } finally {
    loading.value = false
  }
}

// Actions panier
const addToCart = async (template: LicenseTemplate) => {
  try {
    addingToCart.value = template.id

    const success = cartStore.addItem(template, 1)

    if (success) {
      logger.info('[PricingView] Template ajouté au panier', {
        template_id: template.id,
        template_name: template.name
      })

      // Rediriger vers le panier après ajout
      router.push('/store/cart')
    } else {
      throw new Error('Erreur lors de l\'ajout au panier')
    }
  } catch (error: any) {
    logger.error('[PricingView] Erreur ajout panier', {
      template_id: template.id,
      error: error.message
    })

    // Afficher une notification d'erreur (vous pouvez utiliser un toast)
    alert('Erreur lors de l\'ajout au panier: ' + error.message)
  } finally {
    addingToCart.value = null
  }
}

// Lifecycle
onMounted(async () => {
  logger.info('[PricingView] Page des tarifs montée')
  await loadTemplates()

  // Sélectionner automatiquement le premier cycle disponible si le cycle actuel n'a pas de templates
  nextTick(() => {
    if (availableBillingCycles.value.length > 0) {
      const currentCycleAvailable = availableBillingCycles.value.some(c => c.value === selectedBillingCycle.value)
      if (!currentCycleAvailable) {
        selectedBillingCycle.value = availableBillingCycles.value[0].value
      }
    }
  })
})
</script>

<style scoped>
.pricing-view {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Hero */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Filtres de cycle de facturation */
.billing-filter-section {
  background: var(--bg-secondary);
  padding: 2rem 0;
  border-bottom: 1px solid var(--border-color);
}

.billing-toggle {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.billing-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-secondary);
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.billing-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.billing-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Section Tarifs */
.pricing-section {
  padding: 4rem 0;
  background: var(--bg-primary);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
}

.pricing-card.featured {
  border-color: var(--primary-color);
  transform: scale(1.02);
  background: linear-gradient(135deg, var(--card-bg) 0%, rgb(59 130 246 / 2%) 100%);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pricing-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.plan-price {
  margin-bottom: 0.5rem;
}

.price {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
}

.period {
  color: var(--text-secondary);
  font-size: 1rem;
  margin-left: 0.5rem;
}

.setup-fee {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.plan-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
  text-align: center;
}

.plan-highlights {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.highlight-item i {
  color: var(--primary-color);
  width: 16px;
  text-align: center;
}

.plan-features {
  margin-bottom: 2rem;
  flex: 1;
}

.plan-features h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.plan-features li i {
  color: var(--success-color);
  font-size: 0.75rem;
  width: 16px;
}

.renewal-info {
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: rgb(245 158 11 / 10%);
  border-radius: 6px;
  color: var(--warning-color);
  text-align: center;
}

.renewal-info i {
  margin-right: 0.5rem;
}

.plan-action {
  margin-top: auto;
}

/* Section FAQ */
.faq-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.faq-item h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.faq-item h3 i {
  color: var(--primary-color);
}

.faq-item p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Section CTA */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  text-align: center;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.cta-content p {
  font-size: 1.25rem;
  margin: 0 0 2.5rem;
  color: rgb(255 255 255 / 90%);
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-block {
  width: 100%;
  justify-content: center;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: white;
  border-color: white;
}

.btn-outline:hover {
  background: white;
  color: var(--primary-color);
}

/* États */
.loading-state, .empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-secondary);
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-danger {
  background: rgb(239 68 68 / 10%);
  color: var(--danger-color);
  border: 1px solid rgb(239 68 68 / 20%);
}

/* Responsive */
@media (width <= 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }

  .faq-grid {
    grid-template-columns: 1fr;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-lg {
    width: 100%;
    max-width: 300px;
  }

  .billing-toggle {
    flex-direction: column;
    align-items: center;
  }

  .billing-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
