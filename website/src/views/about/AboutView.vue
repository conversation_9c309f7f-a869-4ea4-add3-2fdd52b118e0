<template>
  <div class="about-view">
    <!-- Section Hero -->
    <section class="hero-section">
      <div class="hero-container">
        <h1 class="hero-title">{{ $t('about.hero.title') }}</h1>
        <p class="hero-description">
          {{ $t('about.hero.description') }}
        </p>
      </div>
    </section>

    <!-- Section Mission -->
    <section class="mission-section">
      <div class="container">
        <div class="mission-grid">
          <div class="mission-content">
            <h2>{{ $t('about.mission.title') }}</h2>
            <p>
              {{ $t('about.mission.description') }}
            </p>
          </div>
          <div class="mission-image">
            <div class="image-placeholder">
              <i class="fas fa-rocket"></i>
              <span>{{ $t('about.mission.slogan') }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Valeurs -->
    <section class="values-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('about.values.title') }}</h2>
          <p>{{ $t('about.values.subtitle') }}</p>
        </div>

        <div class="values-grid">
          <div class="value-card">
            <div class="value-icon">
              <i class="fas fa-lightbulb"></i>
            </div>
            <h3>{{ $t('about.values.innovation.title') }}</h3>
            <p>
              {{ $t('about.values.innovation.description') }}
            </p>
          </div>

          <div class="value-card">
            <div class="value-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3>{{ $t('about.values.quality.title') }}</h3>
            <p>
              {{ $t('about.values.quality.description') }}
            </p>
          </div>

          <div class="value-card">
            <div class="value-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>{{ $t('about.values.support.title') }}</h3>
            <p>
              {{ $t('about.values.support.description') }}
            </p>
          </div>
          
          <div class="value-card">
            <div class="value-icon">
              <i class="fas fa-heart"></i>
            </div>
            <h3>{{ $t('about.values.passion.title') }}</h3>
            <p>
              {{ $t('about.values.passion.description') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Équipe -->
    <section class="team-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('about.team.title') }}</h2>
          <p>{{ $t('about.team.subtitle') }}</p>
        </div>

        <div class="team-grid">
          <div class="team-member">
            <div class="member-avatar">
              <i class="fas fa-user"></i>
            </div>
            <h3>{{ $t('about.team.founder.name') }}</h3>
            <p class="member-role">{{ $t('about.team.founder.role') }}</p>
            <p class="member-bio">
              {{ $t('about.team.founder.bio') }}
            </p>
            <div class="member-social">
              <a href="#" class="social-link">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
          
          <div class="team-member">
            <div class="member-avatar">
              <i class="fas fa-user"></i>
            </div>
            <h3>{{ $t('about.team.cto.name') }}</h3>
            <p class="member-role">{{ $t('about.team.cto.role') }}</p>
            <p class="member-bio">
              {{ $t('about.team.cto.bio') }}
            </p>
            <div class="member-social">
              <a href="#" class="social-link">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
          
          <div class="team-member">
            <div class="member-avatar">
              <i class="fas fa-user"></i>
            </div>
            <h3>{{ $t('about.team.lead_dev.name') }}</h3>
            <p class="member-role">{{ $t('about.team.lead_dev.role') }}</p>
            <p class="member-bio">
              {{ $t('about.team.lead_dev.bio') }}
            </p>
            <div class="member-social">
              <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
          
          <div class="team-member">
            <div class="member-avatar">
              <i class="fas fa-user"></i>
            </div>
            <h3>{{ $t('about.team.support_manager.name') }}</h3>
            <p class="member-role">{{ $t('about.team.support_manager.role') }}</p>
            <p class="member-bio">
              {{ $t('about.team.support_manager.bio') }}
            </p>
            <div class="member-social">
              <a href="#" class="social-link">
                <i class="fab fa-linkedin"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Histoire -->
    <section class="history-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('about.history.title') }}</h2>
          <p>{{ $t('about.history.subtitle') }}</p>
        </div>
        
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-date">2020</div>
            <div class="timeline-content">
              <h3>{{ $t('about.history.timeline.2020.title') }}</h3>
              <p>
                {{ $t('about.history.timeline.2020.description') }}
              </p>
            </div>
          </div>
          
          <div class="timeline-item">
            <div class="timeline-date">2021</div>
            <div class="timeline-content">
              <h3>{{ $t('about.history.timeline.2021.title') }}</h3>
              <p>
                {{ $t('about.history.timeline.2021.description') }}
              </p>
            </div>
          </div>
          
          <div class="timeline-item">
            <div class="timeline-date">2022</div>
            <div class="timeline-content">
              <h3>{{ $t('about.history.timeline.2022.title') }}</h3>
              <p>
                {{ $t('about.history.timeline.2022.description') }}
              </p>
            </div>
          </div>
          
          <div class="timeline-item">
            <div class="timeline-date">2023</div>
            <div class="timeline-content">
              <h3>{{ $t('about.history.timeline.2023.title') }}</h3>
              <p>
                {{ $t('about.history.timeline.2023.description') }}
              </p>
            </div>
          </div>
          
          <div class="timeline-item">
            <div class="timeline-date">2024</div>
            <div class="timeline-content">
              <h3>{{ $t('about.history.timeline.2024.title') }}</h3>
              <p>
                {{ $t('about.history.timeline.2024.description') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Statistiques -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">1000+</div>
            <div class="stat-label">{{ $t('about.stats.clients') }}</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">5000+</div>
            <div class="stat-label">{{ $t('about.stats.projects') }}</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">100+</div>
            <div class="stat-label">{{ $t('about.stats.updates') }}</div>
          </div>

          <div class="stat-item">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">{{ $t('about.stats.uptime') }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section CTA -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>{{ $t('about.cta.title') }}</h2>
          <p>{{ $t('about.cta.subtitle') }}</p>
          <div class="cta-actions">
            <router-link to="/pricing" class="btn btn-primary btn-lg">
              <i class="fas fa-rocket"></i>
              {{ $t('about.cta.start_now') }}
            </router-link>
            <router-link to="/contact" class="btn btn-outline btn-lg">
              <i class="fas fa-envelope"></i>
              {{ $t('about.cta.contact_us') }}
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import logger from '@/services/logger'

// Lifecycle
onMounted(() => {
  logger.info('[AboutView] Page à propos montée')
})
</script>

<style scoped>
.about-view {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Hero */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Sections générales */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Section Mission */
.mission-section {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.mission-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.mission-content h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 2rem;
}

.mission-content p {
  color: var(--text-secondary);
  line-height: 1.8;
  margin: 0 0 1.5rem;
  font-size: 1.125rem;
}

.mission-content p:last-child {
  margin-bottom: 0;
}

.mission-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.image-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.image-placeholder span {
  font-size: 1.25rem;
  font-weight: 600;
}

/* Section Valeurs */
.values-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
}

.value-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
}

.value-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.value-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Section Équipe */
.team-section {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.team-member {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
}

.member-avatar {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2.5rem;
  color: white;
}

.team-member h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.member-role {
  color: var(--primary-color);
  font-weight: 500;
  margin: 0 0 1rem;
}

.member-bio {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 1.5rem;
  font-size: 0.875rem;
}

.member-social {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Section Histoire */
.history-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--primary-color);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-date {
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.timeline-content {
  flex: 1;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  margin: 0 2rem;
}

.timeline-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.timeline-content p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Section Statistiques */
.stats-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.125rem;
  opacity: 0.9;
}

/* Section CTA */
.cta-section {
  padding: 6rem 0;
  background: var(--bg-primary);
  text-align: center;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.cta-content p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0 0 2.5rem;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

/* Responsive */
@media (width <= 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .mission-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .mission-content h2 {
    font-size: 1.75rem;
  }

  .image-placeholder {
    width: 250px;
    height: 250px;
  }

  .image-placeholder i {
    font-size: 3rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: row !important;
    padding-left: 80px;
  }

  .timeline-date {
    position: absolute;
    left: 0;
    width: 60px;
    height: 60px;
    font-size: 0.875rem;
  }

  .timeline-content {
    margin: 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-lg {
    width: 100%;
    max-width: 300px;
  }
}

@media (width <= 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    padding-left: 70px;
  }

  .timeline-date {
    width: 50px;
    height: 50px;
    font-size: 0.75rem;
  }
}
</style>
