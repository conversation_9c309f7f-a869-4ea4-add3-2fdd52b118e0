<template>
  <div class="contact-view">
    <!-- Section Hero -->
    <section class="hero-section">
      <div class="hero-container">
        <h1 class="hero-title">{{ t('contact.hero.title') }}</h1>
        <p class="hero-description">
          {{ t('contact.hero.description') }}
        </p>
      </div>
    </section>

    <!-- Section Contact -->
    <section class="contact-section">
      <div class="container">
        <div class="contact-grid">
          <!-- Informations de contact -->
          <div class="contact-info">
            <h2>{{ t('contact.info.title') }}</h2>
            <p>
              {{ t('contact.hero.description') }}
            </p>

            <div class="contact-methods">
              <div class="contact-method">  
                <div class="method-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="method-content">
                  <h3>{{ t('contact.info.email.title') }}</h3>
                  <p><EMAIL></p>
                  <small>Réponse sous 24h</small>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <i class="fas fa-phone"></i>
                </div>
                <div class="method-content">
                  <h3>{{ t('contact.info.phone.title') }}</h3>
                  <p>{{ t('contact.info.phone.number') }}</p>
                  <small>{{ t('contact.info.hours.weekdays') }}</small>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <i class="fas fa-comments"></i>
                </div>
                <div class="method-content">
                  <h3>Chat en direct</h3>
                  <p>Support instantané</p>
                  <small>{{ t('contact.info.hours.weekdays') }}</small>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="method-content">
                  <h3>{{ t('contact.info.address.title') }}</h3>
                  <p>{{ t('contact.info.address.line1') }}<br>{{ t('contact.info.address.line2') }}</p>
                  <small>Sur rendez-vous</small>
                </div>
              </div>
            </div>

            <div class="social-links">
              <h3>{{ $t('contact.social.title') }}</h3>
              <div class="social-buttons">
                <a href="#" class="social-btn">
                  <i class="fab fa-twitter"></i>
                  Twitter
                </a>
                <a href="#" class="social-btn">
                  <i class="fab fa-linkedin"></i>
                  LinkedIn
                </a>
                <a href="#" class="social-btn">
                  <i class="fab fa-github"></i>
                  GitHub
                </a>
              </div>
            </div>
          </div>

          <!-- Formulaire de contact -->
          <div class="contact-form-container">
            <div class="form-card">
              <h2>{{ t('contact.form.title') }}</h2>
              
              <form class="contact-form" @submit.prevent="submitForm">
                <div class="form-row">
                  <div class="form-group">
                    <label for="name" class="required">{{ t('contact.form.name') }}</label>
                    <input
                      id="name"
                      v-model="form.name"
                      type="text"
                      class="form-input"
                      :class="{ 'error': errors.name }"
                      :placeholder="t('contact.form.name_placeholder')"
                      required
                    >
                    <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="email" class="required">{{ t('contact.form.email') }}</label>
                    <input
                      id="email"
                      v-model="form.email"
                      type="email"
                      class="form-input"
                      :class="{ 'error': errors.email }"
                      placeholder="<EMAIL>"
                      required
                    >
                    <span v-if="errors.email" class="error-message">{{ errors.email }}</span>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="company">Entreprise</label>
                    <input
                      id="company"
                      v-model="form.company"
                      type="text"
                      class="form-input"
                      placeholder="Nom de votre entreprise (optionnel)"
                    >
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="subject" class="required">{{ t('contact.form.subject') }}</label>
                    <select
                      id="subject"
                      v-model="form.subject"
                      class="form-select"
                      :class="{ 'error': errors.subject }"
                      required
                    >
                      <option value="">{{ t('contact.form.subject_placeholder') }}</option>
                      <option value="information">{{ t('contact.form.subject_options.information') }}</option>
                      <option value="demo">{{ t('contact.form.subject_options.demo') }}</option>
                      <option value="support">{{ t('contact.form.subject_options.support') }}</option>
                      <option value="partnership">{{ t('contact.form.subject_options.partnership') }}</option>
                      <option value="other">{{ t('contact.form.subject_options.other') }}</option>
                    </select>
                    <span v-if="errors.subject" class="error-message">{{ errors.subject }}</span>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="message" class="required">{{ t('contact.form.message') }}</label>
                    <textarea
                      id="message"
                      v-model="form.message"
                      class="form-textarea"
                      :class="{ 'error': errors.message }"
                      rows="5"
                      :placeholder="t('contact.form.message_placeholder')"
                      required
                    ></textarea>
                    <span v-if="errors.message" class="error-message">{{ errors.message }}</span>
                  </div>
                </div>

                <div class="form-actions">
                  <button 
                    type="submit" 
                    :disabled="submitting"
                    class="btn btn-primary btn-block"
                  >
                    <i v-if="submitting" class="fas fa-spinner fa-spin"></i>
                    <i v-else class="fas fa-paper-plane"></i>
                    {{ submitting ? t('contact.form.sending') : t('contact.form.send') }}
                  </button>
                </div>
              </form>

              <!-- Message de succès -->
              <div v-if="successMessage" class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                {{ successMessage }}
              </div>

              <!-- Message d'erreur -->
              <div v-if="errorMessage" class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                {{ errorMessage }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section FAQ -->
    <section class="faq-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('contact.faq.title') }}</h2>
          <p>{{ $t('contact.faq.subtitle') }}</p>
        </div>
        
        <div class="faq-grid">
          <div class="faq-item">
            <h3>{{ $t('contact.faq.questions.response_time.question') }}</h3>
            <p>
              {{ $t('contact.faq.questions.response_time.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>{{ $t('contact.faq.questions.demo.question') }}</h3>
            <p>
              {{ $t('contact.faq.questions.demo.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>{{ $t('contact.faq.questions.support.question') }}</h3>
            <p>
              {{ $t('contact.faq.questions.support.answer') }}
            </p>
          </div>
          
          <div class="faq-item">
            <h3>{{ $t('contact.faq.questions.quote.question') }}</h3>
            <p>
              {{ $t('contact.faq.questions.quote.answer') }}
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { ApiService } from '@/services/api'
import type { ContactForm } from '@/types/template'
import logger from '@/services/logger'

// Composables
const { t } = useI18n()

// État local
const submitting = ref(false)
const successMessage = ref('')
const errorMessage = ref('')

// Formulaire
const form = reactive<ContactForm>({
  name: '',
  email: '',
  company: '',
  subject: '',
  message: ''
})

// Erreurs de validation
const errors = reactive<Record<string, string>>({})

// Méthodes de validation
const validateForm = (): boolean => {
  // Réinitialiser les erreurs
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Validation du nom
  if (!form.name.trim()) {
    errors.name = 'Le nom est requis'
    isValid = false
  }

  // Validation de l'email
  if (!form.email.trim()) {
    errors.email = 'L\'email est requis'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Format d\'email invalide'
    isValid = false
  }

  // Validation du sujet
  if (!form.subject.trim()) {
    errors.subject = 'Le sujet est requis'
    isValid = false
  }

  // Validation du message
  if (!form.message.trim()) {
    errors.message = 'Le message est requis'
    isValid = false
  } else if (form.message.trim().length < 10) {
    errors.message = 'Le message doit contenir au moins 10 caractères'
    isValid = false
  }

  return isValid
}

// Soumission du formulaire
const submitForm = async () => {
  if (!validateForm()) {
    return
  }

  submitting.value = true
  successMessage.value = ''
  errorMessage.value = ''

  try {
    const response = await ApiService.post('/website/contact', {
      name: form.name.trim(),
      email: form.email.trim(),
      company: form.company?.trim() || undefined,
      subject: form.subject.trim(),
      message: form.message.trim()
    })

    if (response.success) {
      successMessage.value = response.message || t('contact.form.success')
      
      // Réinitialiser le formulaire
      Object.assign(form, {
        name: '',
        email: '',
        company: '',
        subject: '',
        message: ''
      })
      
      logger.info('[ContactView] Message envoyé avec succès')
    } else {
      throw new Error(response.message || t('contact.form.error'))
    }
  } catch (err: any) {
    errorMessage.value = err.message || t('contact.form.error')
    logger.error('[ContactView] Erreur submitForm', { error: err.message })
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.contact-view {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Hero */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Section Contact */
.contact-section {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Informations de contact */
.contact-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.contact-info > p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 3rem;
  font-size: 1.125rem;
}

.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.contact-method:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.method-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.method-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.method-content p {
  color: var(--text-primary);
  font-weight: 500;
  margin: 0 0 0.25rem;
}

.method-content small {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Liens sociaux */
.social-links h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.social-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.social-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.social-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

/* Formulaire de contact */
.contact-form-container {
  position: sticky;
  top: 2rem;
}

.form-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
}

.form-card h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2rem;
  text-align: center;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group label.required::after {
  content: ' *';
  color: var(--danger-color);
}

.form-input,
.form-textarea,
.form-select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(var(--primary-rgb), 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--danger-color);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.error-message {
  margin-top: 0.5rem;
  color: var(--danger-color);
  font-size: 0.75rem;
}

.form-actions {
  margin-top: 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 0.875rem;
}

.btn-block {
  width: 100%;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1e40af;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-success {
  background: rgb(16 185 129 / 10%);
  color: var(--success-color);
  border: 1px solid rgb(16 185 129 / 20%);
}

.alert-danger {
  background: rgb(239 68 68 / 10%);
  color: var(--danger-color);
  border: 1px solid rgb(239 68 68 / 20%);
}

/* Section FAQ */
.faq-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.faq-item h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.faq-item p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Responsive */
@media (width <= 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-info h2 {
    font-size: 1.75rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
  }

  .method-icon {
    align-self: center;
  }

  .social-buttons {
    justify-content: center;
  }

  .contact-form-container {
    position: static;
  }

  .form-row {
    flex-direction: column;
  }

  .faq-grid {
    grid-template-columns: 1fr;
  }
}

@media (width <= 480px) {
  .form-card {
    padding: 1.5rem;
  }

  .contact-method {
    padding: 1rem;
  }

  .social-buttons {
    flex-direction: column;
  }

  .social-btn {
    justify-content: center;
  }
}
</style>
