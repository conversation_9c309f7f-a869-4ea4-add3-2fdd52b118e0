<template>
  <div class="home-view">
    <!-- Section Hero -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('home.hero.title') }}
            <span class="hero-subtitle">{{ $t('home.hero.subtitle') }}</span>
          </h1>
          <p class="hero-description">
            {{ $t('home.hero.description') }}
          </p>
          <div class="hero-actions">
            <router-link to="/pricing" class="btn btn-primary btn-lg">
              <i class="fas fa-rocket"></i>
              {{ $t('home.hero.cta_pricing') }}
            </router-link>
            <router-link to="/features" class="btn btn-outline btn-lg">
              <i class="fas fa-list"></i>
              {{ $t('home.hero.cta_features') }}
            </router-link>
          </div>
        </div>
        <div class="hero-image">
          <div class="hero-mockup">
            <i class="fas fa-desktop"></i>
            <div class="mockup-content">
              <div class="mockup-header"></div>
              <div class="mockup-body">
                <div class="mockup-line"></div>
                <div class="mockup-line short"></div>
                <div class="mockup-line"></div>
                <div class="mockup-line medium"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Fonctionnalités principales -->
    <section class="features-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('home.features.title') }}</h2>
          <p>{{ $t('home.features.subtitle') }}</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-key"></i>
            </div>
            <h3>{{ $t('home.features.license_management.title') }}</h3>
            <p>{{ $t('home.features.license_management.description') }}</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <h3>{{ $t('home.features.auto_updates.title') }}</h3>
            <p>{{ $t('home.features.auto_updates.description') }}</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-headset"></i>
            </div>
            <h3>{{ $t('home.features.support.title') }}</h3>
            <p>{{ $t('home.features.support.description') }}</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3>{{ $t('home.features.security.title') }}</h3>
            <p>{{ $t('home.features.security.description') }}</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <h3>{{ $t('home.features.license_management.title') }}</h3>
            <p>{{ $t('home.features.license_management.description') }}</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>{{ $t('home.features.auto_updates.title') }}</h3>
            <p>{{ $t('home.features.auto_updates.description') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Templates mis en avant -->
    <section v-if="featuredTemplates.length > 0" class="pricing-preview-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('home.pricing.title') }}</h2>
          <p>{{ $t('home.pricing.subtitle') }}</p>
        </div>
        
        <div class="pricing-grid">
          <div 
            v-for="template in featuredTemplates" 
            :key="template.id"
            class="pricing-card featured"
          >
            <div class="pricing-header">
              <h3>{{ template.name }}</h3>
              <div class="pricing-price">
                <span class="price">{{ formatPrice(template.price) }}</span>
                <span class="period">/ {{ getBillingCycleLabel(template.billing_cycle) }}</span>
              </div>
            </div>
            
            <div class="pricing-features">
              <ul>
                <li>
                  <i class="fas fa-check"></i>
                  {{ template.domain_limit }} domaine{{ template.domain_limit > 1 ? 's' : '' }}
                </li>
                <li>
                  <i class="fas fa-check"></i>
                  {{ template.installation_limit }} installation{{ template.installation_limit > 1 ? 's' : '' }}
                </li>
                <li v-if="template.update_permissions">
                  <i class="fas fa-check"></i>
                  Mises à jour incluses
                </li>
                <li v-for="feature in (template.features || []).slice(0, 3)" :key="feature">
                  <i class="fas fa-check"></i>
                  {{ feature }}
                </li>
              </ul>
            </div>
            
            <div class="pricing-action">
              <router-link to="/pricing" class="btn btn-primary btn-block">
                Choisir cette offre
              </router-link>
            </div>
          </div>
        </div>
        
        <div class="pricing-footer">
          <router-link to="/pricing" class="btn btn-outline">
            {{ $t('home.pricing.view_all') }}
            <i class="fas fa-arrow-right"></i>
          </router-link>
        </div>
      </div>
    </section>

    <!-- Section CTA -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>{{ $t('home.cta.title') }}</h2>
          <p>{{ $t('home.cta.subtitle') }}</p>
          <div class="cta-actions">
            <router-link to="/pricing" class="btn btn-primary btn-lg">
              <i class="fas fa-shopping-cart"></i>
              {{ $t('home.cta.buy_license') }}
            </router-link>
            <router-link to="/contact" class="btn btn-outline btn-lg">
              <i class="fas fa-envelope"></i>
              {{ $t('home.cta.contact_us') }}
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ApiService } from '@/services/api'
import { BILLING_CYCLES } from '@/types/template'
import type { LicenseTemplate } from '@/types/template'
import logger from '@/services/logger'

// État local
const featuredTemplates = ref<LicenseTemplate[]>([])
const loading = ref(false)

// Méthodes utilitaires
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}

const getBillingCycleLabel = (cycle: string) => {
  const cycleObj = BILLING_CYCLES.find(c => c.value === cycle)
  return cycleObj?.label.toLowerCase() || cycle
}

// Charger les templates mis en avant
const loadFeaturedTemplates = async () => {
  loading.value = true
  
  try {
    // API publique pour récupérer les templates actifs
    const response = await ApiService.get('/website/templates/featured')
    
    if (response.success && response.templates) {
      featuredTemplates.value = response.templates.slice(0, 3) // Limiter à 3 templates
      logger.info('[HomeView] Templates mis en avant chargés', {
        count: featuredTemplates.value.length
      })
    }
  } catch (error: any) {
    logger.error('[HomeView] Erreur lors du chargement des templates', {
      error: error.message
    })
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  logger.info('[HomeView] Page d\'accueil montée')
  loadFeaturedTemplates()
})
</script>

<style scoped>
.home-view {
  min-height: 100vh;
}

/* Section Hero */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0 0 1rem;
  line-height: 1.1;
}

.hero-subtitle {
  display: block;
  font-size: 1.5rem;
  font-weight: 400;
  color: rgb(255 255 255 / 90%);
  margin-top: 0.5rem;
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  margin: 0 0 2.5rem;
  color: rgb(255 255 255 / 90%);
}

.hero-actions {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-mockup {
  position: relative;
  width: 300px;
  height: 200px;
  background: rgb(255 255 255 / 10%);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 20%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: rgb(255 255 255 / 70%);
}

.mockup-content {
  position: absolute;
  inset: 20px;
}

.mockup-header {
  height: 30px;
  background: rgb(255 255 255 / 20%);
  border-radius: 6px;
  margin-bottom: 15px;
}

.mockup-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mockup-line {
  height: 12px;
  background: rgb(255 255 255 / 30%);
  border-radius: 6px;
}

.mockup-line.short {
  width: 60%;
}

.mockup-line.medium {
  width: 80%;
}

/* Sections générales */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Section Fonctionnalités */
.features-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Section Pricing Preview */
.pricing-preview-section {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.pricing-card {
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.pricing-card.featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.pricing-card.featured::before {
  content: 'Populaire';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.pricing-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.pricing-price {
  margin-bottom: 2rem;
}

.price {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
}

.period {
  color: var(--text-secondary);
  font-size: 1rem;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem;
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: var(--text-secondary);
}

.pricing-features i {
  color: var(--success-color);
  font-size: 0.875rem;
}

.pricing-footer {
  text-align: center;
}

/* Section CTA */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  text-align: center;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.cta-content p {
  font-size: 1.25rem;
  margin: 0 0 2.5rem;
  color: rgb(255 255 255 / 90%);
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-block {
  width: 100%;
  justify-content: center;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.cta-section .btn-outline {
  color: white;
  border-color: white;
}

.cta-section .btn-outline:hover {
  background: white;
  color: var(--primary-color);
}

/* Responsive */
@media (width <= 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-lg {
    width: 100%;
    max-width: 300px;
  }
}
</style>
