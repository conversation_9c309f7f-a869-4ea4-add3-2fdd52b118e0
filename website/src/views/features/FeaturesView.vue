<template>
  <div class="features-view">
    <!-- Section Hero -->
    <section class="hero-section">
      <div class="hero-container">
        <h1 class="hero-title">{{ $t('features.hero.title') }}</h1>
        <p class="hero-description">
          {{ $t('features.hero.description') }}
        </p>
      </div>
    </section>

    <!-- Section Fonctionnalités principales -->
    <section class="main-features-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('features.main_features.title') }}</h2>
          <p>Tout ce dont vous avez besoin pour créer et gérer vos sites web</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-key"></i>
            </div>
            <h3>{{ $t('features.main_features.license_system.title') }}</h3>
            <p>{{ $t('features.main_features.license_system.description') }}</p>
            <ul class="feature-list">
              <li>{{ $t('features.main_features.license_system.features.0') }}</li>
              <li>{{ $t('features.main_features.license_system.features.1') }}</li>
              <li>{{ $t('features.main_features.license_system.features.2') }}</li>
              <li>{{ $t('features.main_features.license_system.features.3') }}</li>
            </ul>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <h3>{{ $t('features.main_features.updates.title') }}</h3>
            <p>{{ $t('features.main_features.updates.description') }}</p>
            <ul class="feature-list">
              <li>{{ $t('features.main_features.updates.features.0') }}</li>
              <li>{{ $t('features.main_features.updates.features.1') }}</li>
              <li>{{ $t('features.main_features.updates.features.2') }}</li>
              <li>{{ $t('features.main_features.updates.features.3') }}</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3>{{ $t('features.main_features.security.title') }}</h3>
            <p>{{ $t('features.main_features.security.description') }}</p>
            <ul class="feature-list">
              <li>{{ $t('features.main_features.security.features.0') }}</li>
              <li>{{ $t('features.main_features.security.features.1') }}</li>
              <li>{{ $t('features.main_features.security.features.2') }}</li>
              <li>{{ $t('features.main_features.security.features.3') }}</li>
            </ul>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <h3>{{ $t('features.main_cards.modular.title') }}</h3>
            <p>{{ $t('features.main_cards.modular.description') }}</p>
            <ul class="feature-list">
              <li>{{ $t('features.main_cards.modular.features.0') }}</li>
              <li>{{ $t('features.main_cards.modular.features.1') }}</li>
              <li>{{ $t('features.main_cards.modular.features.2') }}</li>
              <li>{{ $t('features.main_cards.modular.features.3') }}</li>
            </ul>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>{{ $t('features.main_cards.dashboard.title') }}</h3>
            <p>{{ $t('features.main_cards.dashboard.description') }}</p>
            <ul class="feature-list">
              <li>{{ $t('features.main_cards.dashboard.features.0') }}</li>
              <li>{{ $t('features.main_cards.dashboard.features.1') }}</li>
              <li>{{ $t('features.main_cards.dashboard.features.2') }}</li>
              <li>{{ $t('features.main_cards.dashboard.features.3') }}</li>
            </ul>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-headset"></i>
            </div>
            <h3>{{ $t('features.main_cards.support.title') }}</h3>
            <p>{{ $t('features.main_cards.support.description') }}</p>
            <ul class="feature-list">
              <li>{{ $t('features.main_cards.support.features.0') }}</li>
              <li>{{ $t('features.main_cards.support.features.1') }}</li>
              <li>{{ $t('features.main_cards.support.features.2') }}</li>
              <li>{{ $t('features.main_cards.support.features.3') }}</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Fonctionnalités techniques -->
    <section class="technical-features-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('features.technical.title') }}</h2>
          <p>{{ $t('features.technical.subtitle') }}</p>
        </div>

        <div class="tech-grid">
          <div class="tech-category">
            <h3>
              <i class="fas fa-server"></i>
              {{ $t('features.technical.backend.title') }}
            </h3>
            <ul>
              <li>{{ $t('features.technical.backend.features.0') }}</li>
              <li>{{ $t('features.technical.backend.features.1') }}</li>
              <li>{{ $t('features.technical.backend.features.2') }}</li>
              <li>{{ $t('features.technical.backend.features.3') }}</li>
              <li>{{ $t('features.technical.backend.features.4') }}</li>
            </ul>
          </div>

          <div class="tech-category">
            <h3>
              <i class="fas fa-desktop"></i>
              {{ $t('features.technical.frontend.title') }}
            </h3>
            <ul>
              <li>{{ $t('features.technical.frontend.features.0') }}</li>
              <li>{{ $t('features.technical.frontend.features.1') }}</li>
              <li>{{ $t('features.technical.frontend.features.2') }}</li>
              <li>{{ $t('features.technical.frontend.features.3') }}</li>
              <li>{{ $t('features.technical.frontend.features.4') }}</li>
            </ul>
          </div>
          
          <div class="tech-category">
            <h3>
              <i class="fas fa-cloud"></i>
              {{ $t('features.technical.infrastructure.title') }}
            </h3>
            <ul>
              <li>{{ $t('features.technical.infrastructure.features.0') }}</li>
              <li>{{ $t('features.technical.infrastructure.features.1') }}</li>
              <li>{{ $t('features.technical.infrastructure.features.2') }}</li>
              <li>{{ $t('features.technical.infrastructure.features.3') }}</li>
              <li>{{ $t('features.technical.infrastructure.features.4') }}</li>
            </ul>
          </div>
          
          <div class="tech-category">
            <h3>
              <i class="fas fa-lock"></i>
              {{ $t('features.security.title') }}
            </h3>
            <ul>
              <li>{{ $t('features.security.features.0') }}</li>
              <li>{{ $t('features.security.features.1') }}</li>
              <li>{{ $t('features.security.features.2') }}</li>
              <li>{{ $t('features.security.features.3') }}</li>
              <li>{{ $t('features.security.features.4') }}</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Section Comparaison -->
    <section class="comparison-section">
      <div class="container">
        <div class="section-header">
          <h2>{{ $t('features.comparison.title') }}</h2>
          <p>{{ $t('features.comparison.subtitle') }}</p>
        </div>
        
        <div class="comparison-table">
          <div class="comparison-header">
            <div class="feature-name">Fonctionnalité</div>
            <div class="competitor">Concurrent A</div>
            <div class="competitor">Concurrent B</div>
            <div class="techcms">TechCMS</div>
          </div>
          
          <div class="comparison-row">
            <div class="feature-name">{{ $t('features.comparison.multi_domain') }}</div>
            <div class="competitor"><i class="fas fa-times text-danger"></i></div>
            <div class="competitor"><i class="fas fa-check text-warning"></i></div>
            <div class="techcms"><i class="fas fa-check text-success"></i></div>
          </div>
          
          <div class="comparison-row">
            <div class="feature-name">{{ $t('features.comparison.auto_updates') }}</div>
            <div class="competitor"><i class="fas fa-check text-warning"></i></div>
            <div class="competitor"><i class="fas fa-times text-danger"></i></div>
            <div class="techcms"><i class="fas fa-check text-success"></i></div>
          </div>
          
          <div class="comparison-row">
            <div class="feature-name">{{ $t('features.comparison.support_24_7') }}</div>
            <div class="competitor"><i class="fas fa-times text-danger"></i></div>
            <div class="competitor"><i class="fas fa-times text-danger"></i></div>
            <div class="techcms"><i class="fas fa-check text-success"></i></div>
          </div>
          
          <div class="comparison-row">
            <div class="feature-name">{{ $t('features.comparison.rest_api') }}</div>
            <div class="competitor"><i class="fas fa-check text-warning"></i></div>
            <div class="competitor"><i class="fas fa-check text-warning"></i></div>
            <div class="techcms"><i class="fas fa-check text-success"></i></div>
          </div>
          
          <div class="comparison-row">
            <div class="feature-name">{{ $t('features.comparison.modern_interface') }}</div>
            <div class="competitor"><i class="fas fa-times text-danger"></i></div>
            <div class="competitor"><i class="fas fa-check text-warning"></i></div>
            <div class="techcms"><i class="fas fa-check text-success"></i></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Section CTA -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>{{ $t('features.cta.title') }}</h2>
          <p>{{ $t('features.cta.subtitle') }}</p>
          <div class="cta-actions">
            <router-link to="/pricing" class="btn btn-primary btn-lg">
              <i class="fas fa-shopping-cart"></i>
              {{ $t('features.cta.view_pricing') }}
            </router-link>
            <router-link to="/contact" class="btn btn-outline btn-lg">
              <i class="fas fa-envelope"></i>
              {{ $t('features.cta.request_demo') }}
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import logger from '@/services/logger'

// Lifecycle
onMounted(() => {
  logger.info('[FeaturesView] Page des fonctionnalités montée')
})
</script>

<style scoped>
.features-view {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Hero */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

/* Sections générales */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Section Fonctionnalités principales */
.main-features-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
  text-align: center;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 1.5rem;
  text-align: center;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: 1.5rem;
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: bold;
}

/* Section Fonctionnalités techniques */
.technical-features-section {
  padding: 6rem 0;
  background: var(--bg-primary);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.tech-category {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.tech-category h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.tech-category h3 i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.tech-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tech-category li {
  padding: 0.75rem 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
  position: relative;
  padding-left: 1.5rem;
}

.tech-category li:last-child {
  border-bottom: none;
}

.tech-category li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* Section Comparaison */
.comparison-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.comparison-table {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  max-width: 800px;
  margin: 0 auto;
}

.comparison-header,
.comparison-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  align-items: center;
  min-height: 60px;
}

.comparison-header {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
}

.comparison-row {
  border-bottom: 1px solid var(--border-color);
}

.comparison-row:last-child {
  border-bottom: none;
}

.comparison-row:nth-child(even) {
  background: var(--bg-secondary);
}

.feature-name,
.competitor,
.techcms {
  padding: 1rem;
  text-align: center;
}

.feature-name {
  text-align: left;
  font-weight: 500;
  color: var(--text-primary);
}

.competitor,
.techcms {
  font-size: 1.25rem;
}

.techcms {
  background: rgb(16 185 129 / 10%);
  font-weight: 600;
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

/* Section CTA */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
  color: white;
  text-align: center;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
}

.cta-content p {
  font-size: 1.25rem;
  margin: 0 0 2.5rem;
  color: rgb(255 255 255 / 90%);
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: white;
  border-color: white;
}

.btn-outline:hover {
  background: white;
  color: var(--primary-color);
}

/* Responsive */
@media (width <= 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .comparison-header,
  .comparison-row {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .feature-name {
    text-align: center;
    font-weight: 600;
    background: var(--bg-secondary);
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-lg {
    width: 100%;
    max-width: 300px;
  }
}

@media (width <= 480px) {
  .comparison-table {
    font-size: 0.875rem;
  }

  .feature-name,
  .competitor,
  .techcms {
    padding: 0.75rem 0.5rem;
  }
}
</style>
