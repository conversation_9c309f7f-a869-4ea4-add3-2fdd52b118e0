<template>
  <div id="app" class="tech-cms-website-app">
    <!-- Layout vitrine avec header et footer -->
    <div class="website-layout">
      <WebsiteHeader />
      <main class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
      <WebsiteFooter />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import WebsiteHeader from '@/components/layout/WebsiteHeader.vue'
import WebsiteFooter from '@/components/layout/WebsiteFooter.vue'
import logger from '@/services/logger'

// Configuration initiale
onMounted(() => {
  logger.info('TechCMS Website - Initialisation...')

  // Pas d'authentification nécessaire pour la vitrine
  // Pas de temps réel nécessaire

  logger.info('TechCMS Website - Initialisé avec succès')
})
</script>

<style scoped>
.website-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<style>
/* Styles globaux pour l'application vitrine */
.tech-cms-website-app {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Variables CSS pour la vitrine */
:root {
  --primary-color: #3b82f6;
  --primary-rgb: 59, 130, 246;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  --bg-primary: #fff;
  --bg-secondary: #f8fafc;
  --card-bg: #fff;
  --border-color: #e2e8f0;
  --hover-bg: #f1f5f9;
  --input-bg: #fff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --card-bg: #1e293b;
    --border-color: #334155;
    --hover-bg: #334155;
    --input-bg: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
  }
}
</style>