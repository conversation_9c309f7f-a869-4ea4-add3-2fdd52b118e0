<template>
  <div ref="languageDropdown" class="language-selector">
    <button 
      class="language-toggle"
      :class="{ 'dropdown-open': showDropdown }"
      :aria-label="$t('language.selector')"
      @click="toggleDropdown"
    >
      <span class="current-language">
        <span class="flag">{{ currentLanguageInfo?.flag }}</span>
        <span class="code">{{ currentLanguage.toUpperCase() }}</span>
      </span>
      <i class="fas fa-chevron-down dropdown-icon"></i>
    </button>
    
    <div 
      class="language-dropdown"
      :class="{ 'show': showDropdown }"
    >
      <div class="dropdown-header">
        <h3>{{ $t('language.change_language') }}</h3>
      </div>
      <div class="language-options">
        <button
          v-for="lang in availableLanguages"
          :key="lang.code"
          class="language-option"
          :class="{ 'active': currentLanguage === lang.code }"
          @click="changeLanguage(lang.code)"
        >
          <span class="flag">{{ lang.flag }}</span>
          <span class="name">{{ lang.name }}</span>
          <i v-if="currentLanguage === lang.code" class="fas fa-check"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useLanguageStore } from '@/stores/language'
import { useI18n } from 'vue-i18n'
import logger from '@/services/logger'

// Stores et composables
const languageStore = useLanguageStore()
const { locale } = useI18n()

// État local
const languageDropdown = ref<HTMLElement>()
const showDropdown = ref(false)

// Computed
const currentLanguage = computed(() => languageStore.currentLanguage)
const availableLanguages = computed(() => languageStore.availableLanguages)
const currentLanguageInfo = computed(() => languageStore.getCurrentLanguageInfo)

// Méthodes
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const changeLanguage = (langCode: 'fr' | 'en') => {
  logger.info('[LanguageSelector] Changement de langue vers:', { language: langCode })
  
  // Mettre à jour le store
  languageStore.setLanguage(langCode)
  
  // Mettre à jour vue-i18n
  locale.value = langCode
  
  // Fermer le dropdown
  showDropdown.value = false
  
  logger.info('[LanguageSelector] Langue changée avec succès')
}

const handleClickOutside = (event: Event) => {
  if (languageDropdown.value && !languageDropdown.value.contains(event.target as Node)) {
    showDropdown.value = false
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.language-selector {
  position: relative;
  display: inline-block;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 0.5rem;
  color: var(--text-primary, #1e293b);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.language-toggle:hover {
  background: var(--hover-bg, #f1f5f9);
  border-color: var(--primary-color, #3b82f6);
}

.language-toggle.dropdown-open {
  background: var(--hover-bg, #f1f5f9);
  border-color: var(--primary-color, #3b82f6);
}

.current-language {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.flag {
  font-size: 1rem;
}

.code {
  font-weight: 500;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.dropdown-icon {
  font-size: 0.75rem;
  transition: transform 0.2s ease;
}

.language-toggle.dropdown-open .dropdown-icon {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  min-width: 200px;
  background: var(--card-bg, #fff);
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px -5px rgb(0 0 0 / 10%), 0 4px 6px -2px rgb(0 0 0 / 5%);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  margin-top: 0.25rem;
}

.language-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color, #e2e8f0);
}

.dropdown-header h3 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
}

.language-options {
  padding: 0.5rem 0;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  color: var(--text-primary, #1e293b);
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.language-option:hover {
  background: var(--hover-bg, #f1f5f9);
}

.language-option.active {
  background: rgb(59 130 246 / 10%);
  color: var(--primary-color, #3b82f6);
}

.language-option .flag {
  font-size: 1.125rem;
}

.language-option .name {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.language-option .fa-check {
  font-size: 0.875rem;
  color: var(--primary-color, #3b82f6);
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .language-toggle {
    border-color: #374151;
    color: #f9fafb;
  }
  
  .language-toggle:hover,
  .language-toggle.dropdown-open {
    background: #374151;
    border-color: #3b82f6;
  }
  
  .language-dropdown {
    background: #1f2937;
    border-color: #374151;
  }
  
  .dropdown-header h3 {
    color: #f9fafb;
  }
  
  .language-option {
    color: #f9fafb;
  }
  
  .language-option:hover {
    background: #374151;
  }
}
</style>
