<template>
  <header class="website-header">
    <div class="header-container">
      <!-- Logo et nom -->
      <div class="header-brand">
        <router-link to="/" class="brand-link">
          <div class="brand-logo">
            <i class="fas fa-cube"></i>
          </div>
          <span class="brand-name">TechCMS</span>
        </router-link>
      </div>

      <!-- Navigation principale -->
      <nav class="header-nav" :class="{ 'nav-open': mobileMenuOpen }">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link to="/" class="nav-link" @click="closeMobileMenu">
              {{ $t('navigation.home') }}
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/features" class="nav-link" @click="closeMobileMenu">
              {{ $t('navigation.features') }}
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/pricing" class="nav-link" @click="closeMobileMenu">
              {{ $t('navigation.pricing') }}
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/about" class="nav-link" @click="closeMobileMenu">
              {{ $t('navigation.about') }}
            </router-link>
          </li>
          <li class="nav-item">
            <router-link to="/contact" class="nav-link" @click="closeMobileMenu">
              {{ $t('navigation.contact') }}
            </router-link>
          </li>
        </ul>
      </nav>

      <!-- Actions -->
      <div class="header-actions">
        <!-- Sélecteur de langue -->
        <LanguageSelector />

        <a href="/client" class="btn btn-outline">
          <i class="fas fa-sign-in-alt"></i>
          {{ $t('navigation.client_area') }}
        </a>
        <router-link to="/pricing" class="btn btn-primary">
          <i class="fas fa-shopping-cart"></i>
          {{ $t('navigation.buy') }}
        </router-link>
      </div>

      <!-- Menu mobile toggle -->
      <button 
        class="mobile-menu-toggle"
        :class="{ 'menu-open': mobileMenuOpen }"
        @click="toggleMobileMenu"
      >
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LanguageSelector from '@/components/common/LanguageSelector.vue'

// État local
const mobileMenuOpen = ref(false)

// Méthodes
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}
</script>

<style scoped>
.website-header {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Brand */
.header-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.5rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.brand-name {
  color: var(--text-primary);
}

/* Navigation */
.header-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  display: block;
  padding: 0.5rem 0;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link.router-link-active {
  color: var(--primary-color);
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--border-color);
}

.btn-outline:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
}

/* Menu mobile */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  gap: 4px;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  transition: all 0.3s ease;
  border-radius: 1px;
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Responsive */
@media (width <= 768px) {
  .header-container {
    padding: 0 1rem;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .header-nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  .header-nav.nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 1rem;
  }
  
  .nav-link {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
  }
  
  .nav-item:last-child .nav-link {
    border-bottom: none;
  }
  
  .header-actions {
    gap: 0.5rem;
  }
  
  .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .btn span {
    display: none;
  }
}

@media (width <= 480px) {
  .brand-name {
    display: none;
  }
  
  .header-actions .btn span {
    display: none;
  }
}
</style>
