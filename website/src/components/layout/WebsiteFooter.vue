<template>
  <footer class="website-footer">
    <div class="footer-container">
      <!-- Section principale -->
      <div class="footer-main">
        <!-- Colonne 1 : À propos -->
        <div class="footer-column">
          <div class="footer-brand">
            <div class="brand-logo">
              <i class="fas fa-cube"></i>
            </div>
            <span class="brand-name">TechCMS</span>
          </div>
          <p class="footer-description">
            {{ $t('footer.company.description') }}
          </p>
          <div class="social-links">
            <a href="#" class="social-link" title="GitHub">
              <i class="fab fa-github"></i>
            </a>
            <a href="#" class="social-link" title="Twitter">
              <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="social-link" title="LinkedIn">
              <i class="fab fa-linkedin"></i>
            </a>
            <a href="#" class="social-link" title="Email">
              <i class="fas fa-envelope"></i>
            </a>
          </div>
        </div>

        <!-- Colonne 2 : Produit -->
        <div class="footer-column">
          <h3 class="footer-title">{{ $t('footer.links.product.title') }}</h3>
          <ul class="footer-links">
            <li>
              <router-link to="/features" class="footer-link">
                {{ $t('footer.links.product.features') }}
              </router-link>
            </li>
            <li>
              <router-link to="/pricing" class="footer-link">
                {{ $t('footer.links.product.pricing') }}
              </router-link>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.product.documentation') }}
              </a>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.product.updates') }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Colonne 3 : Support -->
        <div class="footer-column">
          <h3 class="footer-title">{{ $t('footer.links.support.title') }}</h3>
          <ul class="footer-links">
            <li>
              <router-link to="/contact" class="footer-link">
                {{ $t('footer.links.support.contact') }}
              </router-link>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.support.help_center') }}
              </a>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.support.status') }}
              </a>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.support.community') }}
              </a>
            </li>
            <li>
              <a href="/client" class="footer-link">
                {{ $t('navigation.client_area') }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Colonne 4 : Entreprise -->
        <div class="footer-column">
          <h3 class="footer-title">{{ $t('footer.links.company.title') }}</h3>
          <ul class="footer-links">
            <li>
              <router-link to="/about" class="footer-link">
                {{ $t('footer.links.company.about') }}
              </router-link>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.company.team') }}
              </a>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.company.careers') }}
              </a>
            </li>
            <li>
              <a href="#" class="footer-link">
                {{ $t('footer.links.company.press') }}
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Section newsletter -->
      <div class="footer-newsletter">
        <div class="newsletter-content">
          <h3>{{ $t('footer.newsletter.title') }}</h3>
          <p>{{ $t('footer.newsletter.description') }}</p>
        </div>
        <form class="newsletter-form" @submit.prevent="subscribeNewsletter">
          <div class="newsletter-input-group">
            <input
              v-model="newsletterEmail"
              type="email"
              :placeholder="$t('footer.newsletter.placeholder')"
              class="newsletter-input"
              required
            >
            <button type="submit" class="newsletter-button" :disabled="subscribing">
              <i v-if="subscribing" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-paper-plane"></i>
              {{ subscribing ? $t('footer.newsletter.subscribing') : $t('footer.newsletter.subscribe') }}
            </button>
          </div>
          <p v-if="newsletterMessage" class="newsletter-message" :class="newsletterMessageType">
            {{ newsletterMessage }}
          </p>
        </form>
      </div>

      <!-- Section copyright -->
      <div class="footer-bottom">
        <div class="footer-copyright">
          <p>&copy; {{ currentYear }} TechCMS. {{ $t('footer.legal.copyright') }}</p>
        </div>
        <div class="footer-legal">
          <a href="#" class="legal-link">{{ $t('footer.legal.terms') }}</a>
          <a href="#" class="legal-link">{{ $t('footer.legal.privacy') }}</a>
          <a href="#" class="legal-link">{{ $t('footer.legal.conditions') }}</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import logger from '@/services/logger'

// Composables
const { t } = useI18n()

// État local
const newsletterEmail = ref('')
const subscribing = ref(false)
const newsletterMessage = ref('')
const newsletterMessageType = ref<'success' | 'error'>('success')

// Computed
const currentYear = computed(() => new Date().getFullYear())

// Méthodes
const subscribeNewsletter = async () => {
  if (!newsletterEmail.value) return

  subscribing.value = true
  newsletterMessage.value = ''

  try {
    // TODO: Implémenter l'abonnement à la newsletter
    // Simuler un appel API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    newsletterMessage.value = t('footer.newsletter.success')
    newsletterMessageType.value = 'success'
    newsletterEmail.value = ''
    
    logger.info('[WebsiteFooter] Abonnement newsletter', {
      email: newsletterEmail.value
    })
  } catch (error: any) {
    newsletterMessage.value = t('footer.newsletter.error')
    newsletterMessageType.value = 'error'
    
    logger.error('[WebsiteFooter] Erreur abonnement newsletter', {
      error: error.message
    })
  } finally {
    subscribing.value = false
    
    // Effacer le message après 5 secondes
    setTimeout(() => {
      newsletterMessage.value = ''
    }, 5000)
  }
}
</script>

<style scoped>
.website-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem 1rem;
}

/* Section principale */
.footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

/* Brand */
.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.brand-logo {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Liens sociaux */
.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Colonnes de liens */
.footer-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--primary-color);
}

/* Newsletter */
.footer-newsletter {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 3rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
}

.newsletter-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.5rem;
}

.newsletter-content p {
  color: var(--text-secondary);
  margin: 0;
}

.newsletter-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.newsletter-input-group {
  display: flex;
  gap: 0.75rem;
}

.newsletter-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(var(--primary-rgb), 0.1);
}

.newsletter-button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.newsletter-button:hover:not(:disabled) {
  background: #1e40af;
  transform: translateY(-1px);
}

.newsletter-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.newsletter-message {
  font-size: 0.875rem;
  margin: 0;
}

.newsletter-message.success {
  color: var(--success-color);
}

.newsletter-message.error {
  color: var(--danger-color);
}

/* Footer bottom */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.footer-copyright p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.footer-legal {
  display: flex;
  gap: 2rem;
}

.legal-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.legal-link:hover {
  color: var(--primary-color);
}

/* Responsive */
@media (width <= 768px) {
  .footer-container {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .footer-newsletter {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }
  
  .newsletter-input-group {
    flex-direction: column;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-legal {
    gap: 1rem;
  }
}

@media (width <= 480px) {
  .social-links {
    justify-content: center;
  }
  
  .footer-legal {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
