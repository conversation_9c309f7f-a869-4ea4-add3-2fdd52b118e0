import { defineStore } from 'pinia'
import { useNavigatorLanguage } from '@vueuse/core'
import { i18n } from '@/i18n'
import logger from '@/services/logger'

type LanguageCode = 'fr' | 'en'

interface LanguageInfo {
  code: LanguageCode
  name: string
  flag: string
}

export const useLanguageStore = defineStore('language', {
  state: () => ({
    currentLanguage: 'fr' as LanguageCode,
    availableLanguages: [
      { code: 'fr', name: 'Français', flag: '🇫🇷' },
      { code: 'en', name: 'English', flag: '🇬🇧' }
    ] as LanguageInfo[],
    isInitialized: false
  }),

  getters: {
    getCurrentLanguage: (state) => state.currentLanguage,
    getAvailableLanguages: (state) => state.availableLanguages,
    getCurrentLanguageInfo: (state) => {
      return state.availableLanguages.find(lang => lang.code === state.currentLanguage)
    }
  },

  actions: {
    /**
     * Initialise le store de langue avec détection automatique
     */
    async initialize() {
      if (this.isInitialized) {
        return
      }

      logger.info('[LANGUAGE STORE] Initialisation du store de langue')
      
      // 1. Vérifier s'il y a une langue sauvegardée
      const savedLanguage = localStorage.getItem('website-language')
      
      if (savedLanguage && this.isValidLanguage(savedLanguage)) {
        logger.info('[LANGUAGE STORE] Langue sauvegardée trouvée:', { language: savedLanguage })
        this.setLanguage(savedLanguage as LanguageCode, false) // false = ne pas sauvegarder à nouveau
      } else {
        // 2. Détecter la langue du navigateur
        const { language } = useNavigatorLanguage()
        const browserLang = this.detectBrowserLanguage(language.value)
        
        logger.info('[LANGUAGE STORE] Langue du navigateur détectée:', { language: browserLang })
        this.setLanguage(browserLang, true) // true = sauvegarder la préférence
      }
      
      this.isInitialized = true
      logger.info('[LANGUAGE STORE] Store de langue initialisé avec succès')
    },

    /**
     * Change la langue active
     */
    setLanguage(langCode: LanguageCode, savePreference: boolean = true) {
      if (!this.isValidLanguage(langCode)) {
        logger.warn('[LANGUAGE STORE] Code de langue invalide:', { language: langCode })
        return false
      }

      logger.info('[LANGUAGE STORE] Changement de langue vers:', { language: langCode })
      
      // Mettre à jour l'état
      this.currentLanguage = langCode
      
      // Mettre à jour vue-i18n
      i18n.global.locale.value = langCode
      
      // Sauvegarder la préférence si demandé
      if (savePreference) {
        localStorage.setItem('website-language', langCode)
        logger.info('[LANGUAGE STORE] Préférence de langue sauvegardée')
      }
      
      // Mettre à jour l'attribut lang du document
      document.documentElement.lang = langCode
      
      return true
    },

    /**
     * Détecte la langue préférée du navigateur
     */
    detectBrowserLanguage(navigatorLanguage: string | undefined): LanguageCode {
      if (!navigatorLanguage) {
        return 'fr' // Langue par défaut
      }

      // Extraire le code de langue principal (ex: 'fr-FR' -> 'fr')
      const langCode = navigatorLanguage.split('-')[0].toLowerCase()
      
      // Vérifier si la langue est supportée
      if (this.isValidLanguage(langCode)) {
        return langCode as LanguageCode
      }
      
      // Fallback vers le français
      return 'fr'
    },

    /**
     * Vérifie si un code de langue est valide
     */
    isValidLanguage(langCode: string): langCode is LanguageCode {
      return this.availableLanguages.some(lang => lang.code === langCode)
    },

    /**
     * Bascule entre les langues disponibles
     */
    toggleLanguage() {
      const currentIndex = this.availableLanguages.findIndex(
        lang => lang.code === this.currentLanguage
      )
      const nextIndex = (currentIndex + 1) % this.availableLanguages.length
      const nextLanguage = this.availableLanguages[nextIndex]
      
      this.setLanguage(nextLanguage.code)
    }
  },
  
  persist: {
    key: 'website-language-state',
    storage: localStorage
  }
})
