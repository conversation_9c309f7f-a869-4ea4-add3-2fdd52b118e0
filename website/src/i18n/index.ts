import { createI18n } from 'vue-i18n'
import fr from './fr.json'
import en from './en.json'

/**
 * Détecte la langue préférée du navigateur
 */
function detectBrowserLanguage(): string {
  // 1. Vérifier s'il y a une langue sauvegardée
  const savedLanguage = localStorage.getItem('website-language')
  if (savedLanguage && (savedLanguage === 'fr' || savedLanguage === 'en')) {
    return savedLanguage
  }

  // 2. Détecter la langue du navigateur
  const browserLang = navigator.language.split('-')[0].toLowerCase()
  if (browserLang === 'en' || browserLang === 'fr') {
    return browserLang
  }

  // 3. Fallback vers le français
  return 'fr'
}

// Détecter la langue initiale
const initialLanguage = detectBrowserLanguage()

// Créer l'instance i18n
export const i18n = createI18n({
  legacy: false,
  locale: initialLanguage,
  fallbackLocale: 'en',
  messages: {
    fr,
    en
  }
})
