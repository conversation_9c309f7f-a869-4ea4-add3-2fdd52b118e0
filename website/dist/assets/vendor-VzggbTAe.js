/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function e(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}const t={},n=[],r=()=>{},o=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),a=Object.assign,i=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},c=Object.prototype.hasOwnProperty,u=(e,t)=>c.call(e,t),f=Array.isArray,p=e=>"[object Map]"===k(e),d=e=>"[object Set]"===k(e),h=e=>"[object Date]"===k(e),m=e=>"function"==typeof e,g=e=>"string"==typeof e,v=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,y=e=>(_(e)||m(e))&&m(e.then)&&m(e.catch),b=Object.prototype.toString,k=e=>b.call(e),x=e=>"[object Object]"===k(e),w=e=>g(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,S=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),C=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},L=/-(\w)/g,O=C(e=>e.replace(L,(e,t)=>t?t.toUpperCase():"")),E=/\B([A-Z])/g,F=C(e=>e.replace(E,"-$1").toLowerCase()),T=C(e=>e.charAt(0).toUpperCase()+e.slice(1)),P=C(e=>e?`on${T(e)}`:""),I=(e,t)=>!Object.is(e,t),$=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},R=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},M=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let A;const j=()=>A||(A="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function N(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=g(r)?U(r):N(r);if(o)for(const e in o)t[e]=o[e]}return t}if(g(e)||_(e))return e}const W=/;(?![^(]*\))/g,D=/:([^]+)/,V=/\/\*[^]*?\*\//g;function U(e){const t={};return e.replace(V,"").split(W).forEach(e=>{if(e){const n=e.split(D);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function H(e){let t="";if(g(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const r=H(e[n]);r&&(t+=r+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const B=e("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function G(e){return!!e||""===e}function z(e,t){if(e===t)return!0;let n=h(e),r=h(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=v(e),r=v(t),n||r)return e===t;if(n=f(e),r=f(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=z(e[r],t[r]);return n}(e,t);if(n=_(e),r=_(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!z(e[n],t[n]))return!1}}return String(e)===String(t)}function Y(e,t){return e.findIndex(e=>z(e,t))}const q=e=>!(!e||!0!==e.__v_isRef),K=e=>g(e)?e:null==e?"":f(e)||_(e)&&(e.toString===b||!m(e.toString))?q(e)?K(e.value):JSON.stringify(e,J,2):String(e),J=(e,t)=>q(t)?J(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],r)=>(e[Z(t,r)+" =>"]=n,e),{})}:d(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>Z(e))}:v(t)?Z(t):!_(t)||f(t)||x(t)?t:String(t),Z=(e,t="")=>{var n;return v(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};
/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let Q,X;class ee{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Q,!e&&Q&&(this.index=(Q.scopes||(Q.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=Q;try{return Q=this,e()}finally{Q=t}}}on(){1===++this._on&&(this.prevScope=Q,Q=this)}off(){this._on>0&&0===--this._on&&(Q=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function te(e){return new ee(e)}function ne(){return Q}function re(e,t=!1){Q&&Q.cleanups.push(e)}const oe=new WeakSet;class se{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Q&&Q.active&&Q.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,oe.has(this)&&(oe.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||ce(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,xe(this),pe(this);const e=X,t=_e;X=this,_e=!0;try{return this.fn()}finally{de(this),X=e,_e=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)ge(e);this.deps=this.depsTail=void 0,xe(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?oe.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){he(this)&&this.run()}get dirty(){return he(this)}}let le,ae,ie=0;function ce(e,t=!1){if(e.flags|=8,t)return e.next=ae,void(ae=e);e.next=le,le=e}function ue(){ie++}function fe(){if(--ie>0)return;if(ae){let e=ae;for(ae=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;le;){let n=le;for(le=void 0;n;){const r=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=r}}if(e)throw e}function pe(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function de(e){let t,n=e.depsTail,r=n;for(;r;){const e=r.prevDep;-1===r.version?(r===n&&(n=e),ge(r),ve(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=e}e.deps=t,e.depsTail=n}function he(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(me(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function me(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===we)return;if(e.globalVersion=we,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!he(e)))return;e.flags|=2;const t=e.dep,n=X,r=_e;X=e,_e=!0;try{pe(e);const n=e.fn(e._value);(0===t.version||I(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(o){throw t.version++,o}finally{X=n,_e=r,de(e),e.flags&=-3}}function ge(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)ge(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function ve(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let _e=!0;const ye=[];function be(){ye.push(_e),_e=!1}function ke(){const e=ye.pop();_e=void 0===e||e}function xe(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=X;X=void 0;try{t()}finally{X=e}}}let we=0;class Se{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ce{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!X||!_e||X===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==X)t=this.activeLink=new Se(X,this),X.deps?(t.prevDep=X.depsTail,X.depsTail.nextDep=t,X.depsTail=t):X.deps=X.depsTail=t,Le(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=X.depsTail,t.nextDep=void 0,X.depsTail.nextDep=t,X.depsTail=t,X.deps===t&&(X.deps=e)}return t}trigger(e){this.version++,we++,this.notify(e)}notify(e){ue();try{0;for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{fe()}}}function Le(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)Le(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Oe=new WeakMap,Ee=Symbol(""),Fe=Symbol(""),Te=Symbol("");function Pe(e,t,n){if(_e&&X){let t=Oe.get(e);t||Oe.set(e,t=new Map);let r=t.get(n);r||(t.set(n,r=new Ce),r.map=t,r.key=n),r.track()}}function Ie(e,t,n,r,o,s){const l=Oe.get(e);if(!l)return void we++;const a=e=>{e&&e.trigger()};if(ue(),"clear"===t)l.forEach(a);else{const o=f(e),s=o&&w(n);if(o&&"length"===n){const e=Number(r);l.forEach((t,n)=>{("length"===n||n===Te||!v(n)&&n>=e)&&a(t)})}else switch((void 0!==n||l.has(void 0))&&a(l.get(n)),s&&a(l.get(Te)),t){case"add":o?s&&a(l.get("length")):(a(l.get(Ee)),p(e)&&a(l.get(Fe)));break;case"delete":o||(a(l.get(Ee)),p(e)&&a(l.get(Fe)));break;case"set":p(e)&&a(l.get(Ee))}}fe()}function $e(e){const t=_t(e);return t===e?t:(Pe(t,0,Te),gt(e)?t:t.map(bt))}function Re(e){return Pe(e=_t(e),0,Te),e}const Me={__proto__:null,[Symbol.iterator](){return Ae(this,Symbol.iterator,bt)},concat(...e){return $e(this).concat(...e.map(e=>f(e)?$e(e):e))},entries(){return Ae(this,"entries",e=>(e[1]=bt(e[1]),e))},every(e,t){return Ne(this,"every",e,t,void 0,arguments)},filter(e,t){return Ne(this,"filter",e,t,e=>e.map(bt),arguments)},find(e,t){return Ne(this,"find",e,t,bt,arguments)},findIndex(e,t){return Ne(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ne(this,"findLast",e,t,bt,arguments)},findLastIndex(e,t){return Ne(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ne(this,"forEach",e,t,void 0,arguments)},includes(...e){return De(this,"includes",e)},indexOf(...e){return De(this,"indexOf",e)},join(e){return $e(this).join(e)},lastIndexOf(...e){return De(this,"lastIndexOf",e)},map(e,t){return Ne(this,"map",e,t,void 0,arguments)},pop(){return Ve(this,"pop")},push(...e){return Ve(this,"push",e)},reduce(e,...t){return We(this,"reduce",e,t)},reduceRight(e,...t){return We(this,"reduceRight",e,t)},shift(){return Ve(this,"shift")},some(e,t){return Ne(this,"some",e,t,void 0,arguments)},splice(...e){return Ve(this,"splice",e)},toReversed(){return $e(this).toReversed()},toSorted(e){return $e(this).toSorted(e)},toSpliced(...e){return $e(this).toSpliced(...e)},unshift(...e){return Ve(this,"unshift",e)},values(){return Ae(this,"values",bt)}};function Ae(e,t,n){const r=Re(e),o=r[t]();return r===e||gt(e)||(o._next=o.next,o.next=()=>{const e=o._next();return e.value&&(e.value=n(e.value)),e}),o}const je=Array.prototype;function Ne(e,t,n,r,o,s){const l=Re(e),a=l!==e&&!gt(e),i=l[t];if(i!==je[t]){const t=i.apply(e,s);return a?bt(t):t}let c=n;l!==e&&(a?c=function(t,r){return n.call(this,bt(t),r,e)}:n.length>2&&(c=function(t,r){return n.call(this,t,r,e)}));const u=i.call(l,c,r);return a&&o?o(u):u}function We(e,t,n,r){const o=Re(e);let s=n;return o!==e&&(gt(e)?n.length>3&&(s=function(t,r,o){return n.call(this,t,r,o,e)}):s=function(t,r,o){return n.call(this,t,bt(r),o,e)}),o[t](s,...r)}function De(e,t,n){const r=_t(e);Pe(r,0,Te);const o=r[t](...n);return-1!==o&&!1!==o||!vt(n[0])?o:(n[0]=_t(n[0]),r[t](...n))}function Ve(e,t,n=[]){be(),ue();const r=_t(e)[t].apply(e,n);return fe(),ke(),r}const Ue=e("__proto__,__v_isRef,__isVue"),He=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(v));function Be(e){v(e)||(e=String(e));const t=_t(this);return Pe(t,0,e),t.hasOwnProperty(e)}class Ge{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e.__v_skip;const r=this._isReadonly,o=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return o;if("__v_raw"===t)return n===(r?o?it:at:o?lt:st).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const s=f(e);if(!r){let e;if(s&&(e=Me[t]))return e;if("hasOwnProperty"===t)return Be}const l=Reflect.get(e,t,xt(e)?e:n);return(v(t)?He.has(t):Ue(t))?l:(r||Pe(e,0,t),o?l:xt(l)?s&&w(t)?l:l.value:_(l)?r?pt(l):ut(l):l)}}class ze extends Ge{constructor(e=!1){super(!1,e)}set(e,t,n,r){let o=e[t];if(!this._isShallow){const t=mt(o);if(gt(n)||mt(n)||(o=_t(o),n=_t(n)),!f(e)&&xt(o)&&!xt(n))return!t&&(o.value=n,!0)}const s=f(e)&&w(t)?Number(t)<e.length:u(e,t),l=Reflect.set(e,t,n,xt(e)?e:r);return e===_t(r)&&(s?I(n,o)&&Ie(e,"set",t,n):Ie(e,"add",t,n)),l}deleteProperty(e,t){const n=u(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&Ie(e,"delete",t,void 0),r}has(e,t){const n=Reflect.has(e,t);return v(t)&&He.has(t)||Pe(e,0,t),n}ownKeys(e){return Pe(e,0,f(e)?"length":Ee),Reflect.ownKeys(e)}}class Ye extends Ge{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const qe=new ze,Ke=new Ye,Je=new ze(!0),Ze=e=>e,Qe=e=>Reflect.getPrototypeOf(e);function Xe(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function et(e,t){const n={get(n){const r=this.__v_raw,o=_t(r),s=_t(n);e||(I(n,s)&&Pe(o,0,n),Pe(o,0,s));const{has:l}=Qe(o),a=t?Ze:e?kt:bt;return l.call(o,n)?a(r.get(n)):l.call(o,s)?a(r.get(s)):void(r!==o&&r.get(n))},get size(){const t=this.__v_raw;return!e&&Pe(_t(t),0,Ee),Reflect.get(t,"size",t)},has(t){const n=this.__v_raw,r=_t(n),o=_t(t);return e||(I(t,o)&&Pe(r,0,t),Pe(r,0,o)),t===o?n.has(t):n.has(t)||n.has(o)},forEach(n,r){const o=this,s=o.__v_raw,l=_t(s),a=t?Ze:e?kt:bt;return!e&&Pe(l,0,Ee),s.forEach((e,t)=>n.call(r,a(e),a(t),o))}};a(n,e?{add:Xe("add"),set:Xe("set"),delete:Xe("delete"),clear:Xe("clear")}:{add(e){t||gt(e)||mt(e)||(e=_t(e));const n=_t(this);return Qe(n).has.call(n,e)||(n.add(e),Ie(n,"add",e,e)),this},set(e,n){t||gt(n)||mt(n)||(n=_t(n));const r=_t(this),{has:o,get:s}=Qe(r);let l=o.call(r,e);l||(e=_t(e),l=o.call(r,e));const a=s.call(r,e);return r.set(e,n),l?I(n,a)&&Ie(r,"set",e,n):Ie(r,"add",e,n),this},delete(e){const t=_t(this),{has:n,get:r}=Qe(t);let o=n.call(t,e);o||(e=_t(e),o=n.call(t,e)),r&&r.call(t,e);const s=t.delete(e);return o&&Ie(t,"delete",e,void 0),s},clear(){const e=_t(this),t=0!==e.size,n=e.clear();return t&&Ie(e,"clear",void 0,void 0),n}});return["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=function(e,t,n){return function(...r){const o=this.__v_raw,s=_t(o),l=p(s),a="entries"===e||e===Symbol.iterator&&l,i="keys"===e&&l,c=o[e](...r),u=n?Ze:t?kt:bt;return!t&&Pe(s,0,i?Fe:Ee),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}(r,e,t)}),n}function tt(e,t){const n=et(e,t);return(t,r,o)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(u(n,r)&&r in t?n:t,r,o)}const nt={get:tt(!1,!1)},rt={get:tt(!1,!0)},ot={get:tt(!0,!1)},st=new WeakMap,lt=new WeakMap,at=new WeakMap,it=new WeakMap;function ct(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>k(e).slice(8,-1))(e))}function ut(e){return mt(e)?e:dt(e,!1,qe,nt,st)}function ft(e){return dt(e,!1,Je,rt,lt)}function pt(e){return dt(e,!0,Ke,ot,at)}function dt(e,t,n,r,o){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=ct(e);if(0===s)return e;const l=o.get(e);if(l)return l;const a=new Proxy(e,2===s?r:n);return o.set(e,a),a}function ht(e){return mt(e)?ht(e.__v_raw):!(!e||!e.__v_isReactive)}function mt(e){return!(!e||!e.__v_isReadonly)}function gt(e){return!(!e||!e.__v_isShallow)}function vt(e){return!!e&&!!e.__v_raw}function _t(e){const t=e&&e.__v_raw;return t?_t(t):e}function yt(e){return!u(e,"__v_skip")&&Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const bt=e=>_(e)?ut(e):e,kt=e=>_(e)?pt(e):e;function xt(e){return!!e&&!0===e.__v_isRef}function wt(e){return Ct(e,!1)}function St(e){return Ct(e,!0)}function Ct(e,t){return xt(e)?e:new Lt(e,t)}class Lt{constructor(e,t){this.dep=new Ce,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:_t(e),this._value=t?e:bt(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||gt(e)||mt(e);e=n?e:_t(e),I(e,t)&&(this._rawValue=e,this._value=n?e:bt(e),this.dep.trigger())}}function Ot(e){return xt(e)?e.value:e}function Et(e){return m(e)?e():Ot(e)}const Ft={get:(e,t,n)=>"__v_raw"===t?e:Ot(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return xt(o)&&!xt(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Tt(e){return ht(e)?e:new Proxy(e,Ft)}class Pt{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){const n=Oe.get(e);return n&&n.get(t)}(_t(this._object),this._key)}}function It(e,t,n){const r=e[t];return xt(r)?r:new Pt(e,t,n)}class $t{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Ce(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=we-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags)&&X!==this)return ce(this,!0),!0}get value(){const e=this.dep.track();return me(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}const Rt={},Mt=new WeakMap;let At;function jt(e,n,o=t){const{immediate:s,deep:l,once:a,scheduler:c,augmentJob:u,call:p}=o,d=e=>l?e:gt(e)||!1===l||0===l?Nt(e,1):Nt(e);let h,g,v,_,y=!1,b=!1;if(xt(e)?(g=()=>e.value,y=gt(e)):ht(e)?(g=()=>d(e),y=!0):f(e)?(b=!0,y=e.some(e=>ht(e)||gt(e)),g=()=>e.map(e=>xt(e)?e.value:ht(e)?d(e):m(e)?p?p(e,2):e():void 0)):g=m(e)?n?p?()=>p(e,2):e:()=>{if(v){be();try{v()}finally{ke()}}const t=At;At=h;try{return p?p(e,3,[_]):e(_)}finally{At=t}}:r,n&&l){const e=g,t=!0===l?1/0:l;g=()=>Nt(e(),t)}const k=ne(),x=()=>{h.stop(),k&&k.active&&i(k.effects,h)};if(a&&n){const e=n;n=(...t)=>{e(...t),x()}}let w=b?new Array(e.length).fill(Rt):Rt;const S=e=>{if(1&h.flags&&(h.dirty||e))if(n){const e=h.run();if(l||y||(b?e.some((e,t)=>I(e,w[t])):I(e,w))){v&&v();const t=At;At=h;try{const t=[e,w===Rt?void 0:b&&w[0]===Rt?[]:w,_];w=e,p?p(n,3,t):n(...t)}finally{At=t}}}else h.run()};return u&&u(S),h=new se(g),h.scheduler=c?()=>c(S,!1):S,_=e=>function(e,t=!1,n=At){if(n){let t=Mt.get(n);t||Mt.set(n,t=[]),t.push(e)}}(e,!1,h),v=h.onStop=()=>{const e=Mt.get(h);if(e){if(p)p(e,4);else for(const t of e)t();Mt.delete(h)}},n?s?S(!0):w=h.run():c?c(S.bind(null,!0),!0):h.run(),x.pause=h.pause.bind(h),x.resume=h.resume.bind(h),x.stop=x,x}function Nt(e,t=1/0,n){if(t<=0||!_(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,xt(e))Nt(e.value,t,n);else if(f(e))for(let r=0;r<e.length;r++)Nt(e[r],t,n);else if(d(e)||p(e))e.forEach(e=>{Nt(e,t,n)});else if(x(e)){for(const r in e)Nt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Nt(e[r],t,n)}return e}
/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Wt(e,t,n,r){try{return r?e(...r):e()}catch(o){Vt(o,t,n)}}function Dt(e,t,n,r){if(m(e)){const o=Wt(e,t,n,r);return o&&y(o)&&o.catch(e=>{Vt(e,t,n)}),o}if(f(e)){const o=[];for(let s=0;s<e.length;s++)o.push(Dt(e[s],t,n,r));return o}}function Vt(e,n,r,o=!0){n&&n.vnode;const{errorHandler:s,throwUnhandledErrorInProduction:l}=n&&n.appContext.config||t;if(n){let t=n.parent;const o=n.proxy,l=`https://vuejs.org/error-reference/#runtime-${r}`;for(;t;){const n=t.ec;if(n)for(let t=0;t<n.length;t++)if(!1===n[t](e,o,l))return;t=t.parent}if(s)return be(),Wt(s,null,10,[e,o,l]),void ke()}!function(e,t,n,r=!0,o=!1){if(o)throw e}(e,0,0,o,l)}const Ut=[];let Ht=-1;const Bt=[];let Gt=null,zt=0;const Yt=Promise.resolve();let qt=null;function Kt(e){const t=qt||Yt;return e?t.then(this?e.bind(this):e):t}function Jt(e){if(!(1&e.flags)){const t=en(e),n=Ut[Ut.length-1];!n||!(2&e.flags)&&t>=en(n)?Ut.push(e):Ut.splice(function(e){let t=Ht+1,n=Ut.length;for(;t<n;){const r=t+n>>>1,o=Ut[r],s=en(o);s<e||s===e&&2&o.flags?t=r+1:n=r}return t}(t),0,e),e.flags|=1,Zt()}}function Zt(){qt||(qt=Yt.then(tn))}function Qt(e,t,n=Ht+1){for(;n<Ut.length;n++){const t=Ut[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;Ut.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function Xt(e){if(Bt.length){const e=[...new Set(Bt)].sort((e,t)=>en(e)-en(t));if(Bt.length=0,Gt)return void Gt.push(...e);for(Gt=e,zt=0;zt<Gt.length;zt++){const e=Gt[zt];4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}Gt=null,zt=0}}const en=e=>null==e.id?2&e.flags?-1:1/0:e.id;function tn(e){try{for(Ht=0;Ht<Ut.length;Ht++){const e=Ut[Ht];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),Wt(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;Ht<Ut.length;Ht++){const e=Ut[Ht];e&&(e.flags&=-2)}Ht=-1,Ut.length=0,Xt(),qt=null,(Ut.length||Bt.length)&&tn()}}let nn=null,rn=null;function on(e){const t=nn;return nn=e,rn=e&&e.type.__scopeId||null,t}function sn(e,t=nn,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&fo(-1);const o=on(t);let s;try{s=e(...n)}finally{on(o),r._d&&fo(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}function ln(e,n){if(null===nn)return e;const r=Ho(nn),o=e.dirs||(e.dirs=[]);for(let s=0;s<n.length;s++){let[e,l,a,i=t]=n[s];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&Nt(l),o.push({dir:e,instance:r,value:l,oldValue:void 0,arg:a,modifiers:i}))}return e}function an(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let l=0;l<o.length;l++){const a=o[l];s&&(a.oldValue=s[l].value);let i=a.dir[r];i&&(be(),Dt(i,n,8,[e.el,a,e,t]),ke())}}const cn=Symbol("_vte"),un=e=>e.__isTeleport,fn=Symbol("_leaveCb"),pn=Symbol("_enterCb");const dn=[Function,Array],hn={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:dn,onEnter:dn,onAfterEnter:dn,onEnterCancelled:dn,onBeforeLeave:dn,onLeave:dn,onAfterLeave:dn,onLeaveCancelled:dn,onBeforeAppear:dn,onAppear:dn,onAfterAppear:dn,onAppearCancelled:dn},mn=e=>{const t=e.subTree;return t.component?mn(t.component):t};function gn(e){let t=e[0];if(e.length>1)for(const n of e)if(n.type!==so){t=n;break}return t}const vn={name:"BaseTransition",props:hn,setup(e,{slots:t}){const n=$o(),r=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return An(()=>{e.isMounted=!0}),Wn(()=>{e.isUnmounting=!0}),e}();return()=>{const o=t.default&&wn(t.default(),!0);if(!o||!o.length)return;const s=gn(o),l=_t(e),{mode:a}=l;if(r.isLeaving)return bn(s);const i=kn(s);if(!i)return bn(s);let c=yn(i,l,r,n,e=>c=e);i.type!==so&&xn(i,c);let u=n.subTree&&kn(n.subTree);if(u&&u.type!==so&&!vo(i,u)&&mn(n).type!==so){let e=yn(u,l,r,n);if(xn(u,e),"out-in"===a&&i.type!==so)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave,u=void 0},bn(s);"in-out"===a&&i.type!==so?e.delayLeave=(e,t,n)=>{_n(r,u)[String(u.key)]=u,e[fn]=()=>{t(),e[fn]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{n(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return s}}};function _n(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function yn(e,t,n,r,o){const{appear:s,mode:l,persisted:a=!1,onBeforeEnter:i,onEnter:c,onAfterEnter:u,onEnterCancelled:p,onBeforeLeave:d,onLeave:h,onAfterLeave:m,onLeaveCancelled:g,onBeforeAppear:v,onAppear:_,onAfterAppear:y,onAppearCancelled:b}=t,k=String(e.key),x=_n(n,e),w=(e,t)=>{e&&Dt(e,r,9,t)},S=(e,t)=>{const n=t[1];w(e,t),f(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},C={mode:l,persisted:a,beforeEnter(t){let r=i;if(!n.isMounted){if(!s)return;r=v||i}t[fn]&&t[fn](!0);const o=x[k];o&&vo(e,o)&&o.el[fn]&&o.el[fn](),w(r,[t])},enter(e){let t=c,r=u,o=p;if(!n.isMounted){if(!s)return;t=_||c,r=y||u,o=b||p}let l=!1;const a=e[pn]=t=>{l||(l=!0,w(t?o:r,[e]),C.delayedLeave&&C.delayedLeave(),e[pn]=void 0)};t?S(t,[e,a]):a()},leave(t,r){const o=String(e.key);if(t[pn]&&t[pn](!0),n.isUnmounting)return r();w(d,[t]);let s=!1;const l=t[fn]=n=>{s||(s=!0,r(),w(n?g:m,[t]),t[fn]=void 0,x[o]===e&&delete x[o])};x[o]=e,h?S(h,[t,l]):l()},clone(e){const s=yn(e,t,n,r,o);return o&&o(s),s}};return C}function bn(e){if(En(e))return(e=xo(e)).children=null,e}function kn(e){if(!En(e))return un(e.type)&&e.children?gn(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&m(n.default))return n.default()}}function xn(e,t){6&e.shapeFlag&&e.component?(e.transition=t,xn(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function wn(e,t=!1,n){let r=[],o=0;for(let s=0;s<e.length;s++){let l=e[s];const a=null==n?l.key:String(n)+String(null!=l.key?l.key:s);l.type===ro?(128&l.patchFlag&&o++,r=r.concat(wn(l.children,t,a))):(t||l.type!==so)&&r.push(null!=a?xo(l,{key:a}):l)}if(o>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}
/*! #__NO_SIDE_EFFECTS__ */function Sn(e,t){return m(e)?(()=>a({name:e.name},t,{setup:e}))():e}function Cn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Ln(e,n,r,o,s=!1){if(f(e))return void e.forEach((e,t)=>Ln(e,n&&(f(n)?n[t]:n),r,o,s));if(On(o)&&!s)return void(512&o.shapeFlag&&o.type.__asyncResolved&&o.component.subTree.component&&Ln(e,n,r,o.component.subTree));const l=4&o.shapeFlag?Ho(o.component):o.el,a=s?null:l,{i:c,r:p}=e,d=n&&n.r,h=c.refs===t?c.refs={}:c.refs,v=c.setupState,_=_t(v),y=v===t?()=>!1:e=>u(_,e);if(null!=d&&d!==p&&(g(d)?(h[d]=null,y(d)&&(v[d]=null)):xt(d)&&(d.value=null)),m(p))Wt(p,c,12,[a,h]);else{const t=g(p),n=xt(p);if(t||n){const o=()=>{if(e.f){const n=t?y(p)?v[p]:h[p]:p.value;s?f(n)&&i(n,l):f(n)?n.includes(l)||n.push(l):t?(h[p]=[l],y(p)&&(v[p]=h[p])):(p.value=[l],e.k&&(h[e.k]=p.value))}else t?(h[p]=a,y(p)&&(v[p]=a)):n&&(p.value=a,e.k&&(h[e.k]=a))};a?(o.id=-1,Mr(o,r)):o()}}}j().requestIdleCallback,j().cancelIdleCallback;const On=e=>!!e.type.__asyncLoader,En=e=>e.type.__isKeepAlive;function Fn(e,t){Pn(e,"a",t)}function Tn(e,t){Pn(e,"da",t)}function Pn(e,t,n=Io){const r=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if($n(t,r,n),n){let e=n.parent;for(;e&&e.parent;)En(e.parent.vnode)&&In(r,t,n,e),e=e.parent}}function In(e,t,n,r){const o=$n(t,e,r,!0);Dn(()=>{i(r[t],o)},n)}function $n(e,t,n=Io,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...r)=>{be();const o=Ao(n),s=Dt(t,n,e,r);return o(),ke(),s});return r?o.unshift(s):o.push(s),s}}const Rn=e=>(t,n=Io)=>{Wo&&"sp"!==e||$n(e,(...e)=>t(...e),n)},Mn=Rn("bm"),An=Rn("m"),jn=Rn("bu"),Nn=Rn("u"),Wn=Rn("bum"),Dn=Rn("um"),Vn=Rn("sp"),Un=Rn("rtg"),Hn=Rn("rtc");function Bn(e,t=Io){$n("ec",e,t)}const Gn="components";function zn(e,t){return Kn(Gn,e,!0,t)||e}const Yn=Symbol.for("v-ndc");function qn(e){return g(e)?Kn(Gn,e,!1)||e:e||Yn}function Kn(e,t,n=!0,r=!1){const o=nn||Io;if(o){const n=o.type;{const e=Bo(n,!1);if(e&&(e===t||e===O(t)||e===T(O(t))))return n}const s=Jn(o[e]||n[e],t)||Jn(o.appContext[e],t);return!s&&r?n:s}}function Jn(e,t){return e&&(e[t]||e[O(t)]||e[T(O(t))])}function Zn(e,t,n,r){let o;const s=n,l=f(e);if(l||g(e)){let n=!1,r=!1;l&&ht(e)&&(n=!gt(e),r=mt(e),e=Re(e)),o=new Array(e.length);for(let l=0,a=e.length;l<a;l++)o[l]=t(n?r?kt(bt(e[l])):bt(e[l]):e[l],l,void 0,s)}else if("number"==typeof e){o=new Array(e);for(let n=0;n<e;n++)o[n]=t(n+1,n,void 0,s)}else if(_(e))if(e[Symbol.iterator])o=Array.from(e,(e,n)=>t(e,n,void 0,s));else{const n=Object.keys(e);o=new Array(n.length);for(let r=0,l=n.length;r<l;r++){const l=n[r];o[r]=t(e[l],l,r,s)}}else o=[];return o}const Qn=e=>e?No(e)?Ho(e):Qn(e.parent):null,Xn=a(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qn(e.parent),$root:e=>Qn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ar(e),$forceUpdate:e=>e.f||(e.f=()=>{Jt(e.update)}),$nextTick:e=>e.n||(e.n=Kt.bind(e.proxy)),$watch:e=>zr.bind(e)}),er=(e,n)=>e!==t&&!e.__isScriptSetup&&u(e,n),tr={get({_:e},n){if("__v_skip"===n)return!0;const{ctx:r,setupState:o,data:s,props:l,accessCache:a,type:i,appContext:c}=e;let f;if("$"!==n[0]){const i=a[n];if(void 0!==i)switch(i){case 1:return o[n];case 2:return s[n];case 4:return r[n];case 3:return l[n]}else{if(er(o,n))return a[n]=1,o[n];if(s!==t&&u(s,n))return a[n]=2,s[n];if((f=e.propsOptions[0])&&u(f,n))return a[n]=3,l[n];if(r!==t&&u(r,n))return a[n]=4,r[n];rr&&(a[n]=0)}}const p=Xn[n];let d,h;return p?("$attrs"===n&&Pe(e.attrs,0,""),p(e)):(d=i.__cssModules)&&(d=d[n])?d:r!==t&&u(r,n)?(a[n]=4,r[n]):(h=c.config.globalProperties,u(h,n)?h[n]:void 0)},set({_:e},n,r){const{data:o,setupState:s,ctx:l}=e;return er(s,n)?(s[n]=r,!0):o!==t&&u(o,n)?(o[n]=r,!0):!u(e.props,n)&&(("$"!==n[0]||!(n.slice(1)in e))&&(l[n]=r,!0))},has({_:{data:e,setupState:n,accessCache:r,ctx:o,appContext:s,propsOptions:l}},a){let i;return!!r[a]||e!==t&&u(e,a)||er(n,a)||(i=l[0])&&u(i,a)||u(o,a)||u(Xn,a)||u(s.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:u(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function nr(e){return f(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}let rr=!0;function or(e){const t=ar(e),n=e.proxy,o=e.ctx;rr=!1,t.beforeCreate&&sr(t.beforeCreate,e,"bc");const{data:s,computed:l,methods:a,watch:i,provide:c,inject:u,created:p,beforeMount:d,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeDestroy:k,beforeUnmount:x,destroyed:w,unmounted:S,render:C,renderTracked:L,renderTriggered:O,errorCaptured:E,serverPrefetch:F,expose:T,inheritAttrs:P,components:I,directives:$,filters:R}=t;if(u&&function(e,t){f(e)&&(e=fr(e));for(const n in e){const r=e[n];let o;o=_(r)?"default"in r?br(r.from||n,r.default,!0):br(r.from||n):br(r),xt(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[n]=o}}(u,o,null),a)for(const r in a){const e=a[r];m(e)&&(o[r]=e.bind(n))}if(s){const t=s.call(n,n);_(t)&&(e.data=ut(t))}if(rr=!0,l)for(const f in l){const e=l[f],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):r,s=!m(e)&&m(e.set)?e.set.bind(n):r,a=Go({get:t,set:s});Object.defineProperty(o,f,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(i)for(const r in i)lr(i[r],o,n,r);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{yr(t,e[t])})}function M(e,t){f(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(p&&sr(p,e,"c"),M(Mn,d),M(An,h),M(jn,g),M(Nn,v),M(Fn,y),M(Tn,b),M(Bn,E),M(Hn,L),M(Un,O),M(Wn,x),M(Dn,S),M(Vn,F),f(T))if(T.length){const t=e.exposed||(e.exposed={});T.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});C&&e.render===r&&(e.render=C),null!=P&&(e.inheritAttrs=P),I&&(e.components=I),$&&(e.directives=$),F&&Cn(e)}function sr(e,t,n){Dt(f(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function lr(e,t,n,r){let o=r.includes(".")?Yr(n,r):()=>n[r];if(g(e)){const n=t[e];m(n)&&Br(o,n)}else if(m(e))Br(o,e.bind(n));else if(_(e))if(f(e))e.forEach(e=>lr(e,t,n,r));else{const r=m(e.handler)?e.handler.bind(n):t[e.handler];m(r)&&Br(o,r,e)}}function ar(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:l}}=e.appContext,a=s.get(t);let i;return a?i=a:o.length||n||r?(i={},o.length&&o.forEach(e=>ir(i,e,l,!0)),ir(i,t,l)):i=t,_(t)&&s.set(t,i),i}function ir(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&ir(e,s,n,!0),o&&o.forEach(t=>ir(e,t,n,!0));for(const l in t)if(r&&"expose"===l);else{const r=cr[l]||n&&n[l];e[l]=r?r(e[l],t[l]):t[l]}return e}const cr={data:ur,props:hr,emits:hr,methods:dr,computed:dr,beforeCreate:pr,created:pr,beforeMount:pr,mounted:pr,beforeUpdate:pr,updated:pr,beforeDestroy:pr,beforeUnmount:pr,destroyed:pr,unmounted:pr,activated:pr,deactivated:pr,errorCaptured:pr,serverPrefetch:pr,components:dr,directives:dr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=a(Object.create(null),e);for(const r in t)n[r]=pr(e[r],t[r]);return n},provide:ur,inject:function(e,t){return dr(fr(e),fr(t))}};function ur(e,t){return t?e?function(){return a(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function fr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function pr(e,t){return e?[...new Set([].concat(e,t))]:t}function dr(e,t){return e?a(Object.create(null),e,t):t}function hr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:a(Object.create(null),nr(e),nr(null!=t?t:{})):t}function mr(){return{app:null,config:{isNativeTag:o,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let gr=0;function vr(e,t){return function(t,n=null){m(t)||(t=a({},t)),null==n||_(n)||(n=null);const r=mr(),o=new WeakSet,s=[];let l=!1;const i=r.app={_uid:gr++,_component:t,_props:n,_container:null,_context:r,_instance:null,version:Yo,get config(){return r.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&m(e.install)?(o.add(e),e.install(i,...t)):m(e)&&(o.add(e),e(i,...t))),i),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),i),component:(e,t)=>t?(r.components[e]=t,i):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,i):r.directives[e],mount(o,s,a){if(!l){const s=i._ceVNode||ko(t,n);return s.appContext=r,!0===a?a="svg":!1===a&&(a=void 0),e(s,o,a),l=!0,i._container=o,o.__vue_app__=i,Ho(s.component)}},onUnmount(e){s.push(e)},unmount(){l&&(Dt(s,i._instance,16),e(null,i._container),delete i._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,i),runWithContext(e){const t=_r;_r=i;try{return e()}finally{_r=t}}};return i}}let _r=null;function yr(e,t){if(Io){let n=Io.provides;const r=Io.parent&&Io.parent.provides;r===n&&(n=Io.provides=Object.create(r)),n[e]=t}else;}function br(e,t,n=!1){const r=Io||nn;if(r||_r){let o=_r?_r._context.provides:r?null==r.parent||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&m(t)?t.call(r&&r.proxy):t}}const kr={},xr=()=>Object.create(kr),wr=e=>Object.getPrototypeOf(e)===kr;function Sr(e,n,r,o){const[s,l]=e.propsOptions;let a,i=!1;if(n)for(let t in n){if(S(t))continue;const c=n[t];let f;s&&u(s,f=O(t))?l&&l.includes(f)?(a||(a={}))[f]=c:r[f]=c:Zr(e.emitsOptions,t)||t in o&&c===o[t]||(o[t]=c,i=!0)}if(l){const n=_t(r),o=a||t;for(let t=0;t<l.length;t++){const a=l[t];r[a]=Cr(s,n,a,o[a],e,!u(o,a))}}return i}function Cr(e,t,n,r,o,s){const l=e[n];if(null!=l){const e=u(l,"default");if(e&&void 0===r){const e=l.default;if(l.type!==Function&&!l.skipFactory&&m(e)){const{propsDefaults:s}=o;if(n in s)r=s[n];else{const l=Ao(o);r=s[n]=e.call(null,t),l()}}else r=e;o.ce&&o.ce._setProp(n,r)}l[0]&&(s&&!e?r=!1:!l[1]||""!==r&&r!==F(n)||(r=!0))}return r}const Lr=new WeakMap;function Or(e,r,o=!1){const s=o?Lr:r.propsCache,l=s.get(e);if(l)return l;const i=e.props,c={},p=[];let d=!1;if(!m(e)){const t=e=>{d=!0;const[t,n]=Or(e,r,!0);a(c,t),n&&p.push(...n)};!o&&r.mixins.length&&r.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!i&&!d)return _(e)&&s.set(e,n),n;if(f(i))for(let n=0;n<i.length;n++){const e=O(i[n]);Er(e)&&(c[e]=t)}else if(i)for(const t in i){const e=O(t);if(Er(e)){const n=i[t],r=c[e]=f(n)||m(n)?{type:n}:a({},n),o=r.type;let s=!1,l=!0;if(f(o))for(let e=0;e<o.length;++e){const t=o[e],n=m(t)&&t.name;if("Boolean"===n){s=!0;break}"String"===n&&(l=!1)}else s=m(o)&&"Boolean"===o.name;r[0]=s,r[1]=l,(s||u(r,"default"))&&p.push(e)}}const h=[c,p];return _(e)&&s.set(e,h),h}function Er(e){return"$"!==e[0]&&!S(e)}const Fr=e=>"_"===e[0]||"$stable"===e,Tr=e=>f(e)?e.map(Lo):[Lo(e)],Pr=(e,t,n)=>{if(t._n)return t;const r=sn((...e)=>Tr(t(...e)),n);return r._c=!1,r},Ir=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Fr(o))continue;const n=e[o];if(m(n))t[o]=Pr(0,n,r);else if(null!=n){const e=Tr(n);t[o]=()=>e}}},$r=(e,t)=>{const n=Tr(t);e.slots.default=()=>n},Rr=(e,t,n)=>{for(const r in t)!n&&Fr(r)||(e[r]=t[r])},Mr=function(e,t){t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?Bt.push(...n):Gt&&-1===n.id?Gt.splice(zt+1,0,n):1&n.flags||(Bt.push(n),n.flags|=1),Zt());var n};function Ar(e){return function(e){j().__VUE__=!0;const{insert:o,remove:s,patchProp:l,createElement:a,createText:i,createComment:c,setText:p,setElementText:d,parentNode:h,nextSibling:m,setScopeId:g=r,insertStaticContent:v}=e,_=(e,t,n,r=null,o=null,s=null,l=void 0,a=null,i=!!t.dynamicChildren)=>{if(e===t)return;e&&!vo(e,t)&&(r=X(e),q(e,o,s,!0),e=null),-2===t.patchFlag&&(i=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case oo:b(e,t,n,r);break;case so:k(e,t,n,r);break;case lo:null==e&&x(t,n,r,l);break;case ro:N(e,t,n,r,o,s,l,a,i);break;default:1&f?L(e,t,n,r,o,s,l,a,i):6&f?W(e,t,n,r,o,s,l,a,i):(64&f||128&f)&&c.process(e,t,n,r,o,s,l,a,i,re)}null!=u&&o?Ln(u,e&&e.ref,s,t||e,!t):null==u&&e&&null!=e.ref&&Ln(e.ref,null,s,e,!0)},b=(e,t,n,r)=>{if(null==e)o(t.el=i(t.children),n,r);else{const n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},k=(e,t,n,r)=>{null==e?o(t.el=c(t.children||""),n,r):t.el=e.el},x=(e,t,n,r)=>{[e.el,e.anchor]=v(e.children,t,n,r,e.el,e.anchor)},w=({el:e,anchor:t},n,r)=>{let s;for(;e&&e!==t;)s=m(e),o(e,n,r),e=s;o(t,n,r)},C=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=m(e),s(e),e=n;s(t)},L=(e,t,n,r,o,s,l,a,i)=>{"svg"===t.type?l="svg":"math"===t.type&&(l="mathml"),null==e?E(t,n,r,o,s,l,a,i):I(e,t,o,s,l,a,i)},E=(e,t,n,r,s,i,c,u)=>{let f,p;const{props:h,shapeFlag:m,transition:g,dirs:v}=e;if(f=e.el=a(e.type,i,h&&h.is,h),8&m?d(f,e.children):16&m&&P(e.children,f,null,r,s,jr(e,i),c,u),v&&an(e,null,r,"created"),T(f,e,e.scopeId,c,r),h){for(const e in h)"value"===e||S(e)||l(f,e,null,h[e],i,r);"value"in h&&l(f,"value",null,h.value,i),(p=h.onVnodeBeforeMount)&&Fo(p,r,e)}v&&an(e,null,r,"beforeMount");const _=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,g);_&&g.beforeEnter(f),o(f,t,n),((p=h&&h.onVnodeMounted)||_||v)&&Mr(()=>{p&&Fo(p,r,e),_&&g.enter(f),v&&an(e,null,r,"mounted")},s)},T=(e,t,n,r,o)=>{if(n&&g(e,n),r)for(let s=0;s<r.length;s++)g(e,r[s]);if(o){let n=o.subTree;if(t===n||no(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=o.vnode;T(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},P=(e,t,n,r,o,s,l,a,i=0)=>{for(let c=i;c<e.length;c++){const i=e[c]=a?Oo(e[c]):Lo(e[c]);_(null,i,t,n,r,o,s,l,a)}},I=(e,n,r,o,s,a,i)=>{const c=n.el=e.el;let{patchFlag:u,dynamicChildren:f,dirs:p}=n;u|=16&e.patchFlag;const h=e.props||t,m=n.props||t;let g;if(r&&Nr(r,!1),(g=m.onVnodeBeforeUpdate)&&Fo(g,r,n,e),p&&an(n,e,r,"beforeUpdate"),r&&Nr(r,!0),(h.innerHTML&&null==m.innerHTML||h.textContent&&null==m.textContent)&&d(c,""),f?M(e.dynamicChildren,f,c,r,o,jr(n,s),a):i||B(e,n,c,null,r,o,jr(n,s),a,!1),u>0){if(16&u)A(c,h,m,r,s);else if(2&u&&h.class!==m.class&&l(c,"class",null,m.class,s),4&u&&l(c,"style",h.style,m.style,s),8&u){const e=n.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t],o=h[n],a=m[n];a===o&&"value"!==n||l(c,n,o,a,s,r)}}1&u&&e.children!==n.children&&d(c,n.children)}else i||null!=f||A(c,h,m,r,s);((g=m.onVnodeUpdated)||p)&&Mr(()=>{g&&Fo(g,r,n,e),p&&an(n,e,r,"updated")},o)},M=(e,t,n,r,o,s,l)=>{for(let a=0;a<t.length;a++){const i=e[a],c=t[a],u=i.el&&(i.type===ro||!vo(i,c)||198&i.shapeFlag)?h(i.el):n;_(i,c,u,null,r,o,s,l,!0)}},A=(e,n,r,o,s)=>{if(n!==r){if(n!==t)for(const t in n)S(t)||t in r||l(e,t,n[t],null,s,o);for(const t in r){if(S(t))continue;const a=r[t],i=n[t];a!==i&&"value"!==t&&l(e,t,i,a,s,o)}"value"in r&&l(e,"value",n.value,r.value,s)}},N=(e,t,n,r,s,l,a,c,u)=>{const f=t.el=e?e.el:i(""),p=t.anchor=e?e.anchor:i("");let{patchFlag:d,dynamicChildren:h,slotScopeIds:m}=t;m&&(c=c?c.concat(m):m),null==e?(o(f,n,r),o(p,n,r),P(t.children||[],n,p,s,l,a,c,u)):d>0&&64&d&&h&&e.dynamicChildren?(M(e.dynamicChildren,h,n,s,l,a,c),(null!=t.key||s&&t===s.subTree)&&Wr(e,t,!0)):B(e,t,n,p,s,l,a,c,u)},W=(e,t,n,r,o,s,l,a,i)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,l,i):D(t,n,r,o,s,l,i):V(e,t,i)},D=(e,n,r,o,s,l,a)=>{const i=e.component=function(e,n,r){const o=e.type,s=(n?n.appContext:e.appContext)||To,l={uid:Po++,vnode:e,type:o,parent:n,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ee(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(s.provides),ids:n?n.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Or(o,s),emitsOptions:Jr(o,s),emit:null,emitted:null,propsDefaults:t,inheritAttrs:o.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};l.ctx={_:l},l.root=n?n.root:l,l.emit=Kr.bind(null,l),e.ce&&e.ce(l);return l}(e,o,s);if(En(e)&&(i.ctx.renderer=re),function(e,t=!1,n=!1){t&&Mo(t);const{props:r,children:o}=e.vnode,s=No(e);(function(e,t,n,r=!1){const o={},s=xr();e.propsDefaults=Object.create(null),Sr(e,t,o,s);for(const l in e.propsOptions[0])l in o||(o[l]=void 0);n?e.props=r?o:ft(o):e.type.props?e.props=o:e.props=s,e.attrs=s})(e,r,s,t),((e,t,n)=>{const r=e.slots=xr();if(32&e.vnode.shapeFlag){const e=t.__;e&&R(r,"__",e,!0);const o=t._;o?(Rr(r,t,n),n&&R(r,"_",o,!0)):Ir(t,r)}else t&&$r(e,t)})(e,o,n||t);const l=s?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,tr);const{setup:r}=n;if(r){be();const n=e.setupContext=r.length>1?function(e){const t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,Uo),slots:e.slots,emit:e.emit,expose:t}}(e):null,o=Ao(e),s=Wt(r,e,0,[e.props,n]),l=y(s);if(ke(),o(),!l&&!e.sp||On(e)||Cn(e),l){if(s.then(jo,jo),t)return s.then(t=>{Do(e,t)}).catch(t=>{Vt(t,e,0)});e.asyncDep=s}else Do(e,s)}else Vo(e)}(e,t):void 0;t&&Mo(!1)}(i,!1,a),i.asyncDep){if(s&&s.registerDep(i,U,a),!e.el){const e=i.subTree=ko(so);k(null,e,n,r)}}else U(i,e,n,r,s,l,a)},V=(e,t,n)=>{const r=t.component=e.component;if(function(e,t,n){const{props:r,children:o,component:s}=e,{props:l,children:a,patchFlag:i}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&i>=0))return!(!o&&!a||a&&a.$stable)||r!==l&&(r?!l||to(r,l,c):!!l);if(1024&i)return!0;if(16&i)return r?to(r,l,c):!!l;if(8&i){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(l[n]!==r[n]&&!Zr(c,n))return!0}}return!1}(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void H(r,t,n);r.next=t,r.update()}else t.el=e.el,r.vnode=t},U=(e,t,n,r,o,s,l)=>{const a=()=>{if(e.isMounted){let{next:t,bu:n,u:r,parent:i,vnode:c}=e;{const n=Dr(e);if(n)return t&&(t.el=c.el,H(e,t,l)),void n.asyncDep.then(()=>{e.isUnmounted||a()})}let u,f=t;Nr(e,!1),t?(t.el=c.el,H(e,t,l)):t=c,n&&$(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Fo(u,i,t,c),Nr(e,!0);const p=Qr(e),d=e.subTree;e.subTree=p,_(d,p,h(d.el),X(d),e,o,s),t.el=p.el,null===f&&function({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,p.el),r&&Mr(r,o),(u=t.props&&t.props.onVnodeUpdated)&&Mr(()=>Fo(u,i,t,c),o)}else{let l;const{el:a,props:i}=t,{bm:c,m:u,parent:f,root:p,type:d}=e,h=On(t);Nr(e,!1),c&&$(c),!h&&(l=i&&i.onVnodeBeforeMount)&&Fo(l,f,t),Nr(e,!0);{p.ce&&!1!==p.ce._def.shadowRoot&&p.ce._injectChildStyle(d);const l=e.subTree=Qr(e);_(null,l,n,r,e,o,s),t.el=l.el}if(u&&Mr(u,o),!h&&(l=i&&i.onVnodeMounted)){const e=t;Mr(()=>Fo(l,f,e),o)}(256&t.shapeFlag||f&&On(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&Mr(e.a,o),e.isMounted=!0,t=n=r=null}};e.scope.on();const i=e.effect=new se(a);e.scope.off();const c=e.update=i.run.bind(i),u=e.job=i.runIfDirty.bind(i);u.i=e,u.id=e.uid,i.scheduler=()=>Jt(u),Nr(e,!0),c()},H=(e,n,r)=>{n.component=e;const o=e.vnode.props;e.vnode=n,e.next=null,function(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:l}}=e,a=_t(o),[i]=e.propsOptions;let c=!1;if(!(r||l>0)||16&l){let r;Sr(e,t,o,s)&&(c=!0);for(const s in a)t&&(u(t,s)||(r=F(s))!==s&&u(t,r))||(i?!n||void 0===n[s]&&void 0===n[r]||(o[s]=Cr(i,a,s,void 0,e,!0)):delete o[s]);if(s!==a)for(const e in s)t&&u(t,e)||(delete s[e],c=!0)}else if(8&l){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let l=n[r];if(Zr(e.emitsOptions,l))continue;const f=t[l];if(i)if(u(s,l))f!==s[l]&&(s[l]=f,c=!0);else{const t=O(l);o[t]=Cr(i,a,t,f,e,!1)}else f!==s[l]&&(s[l]=f,c=!0)}}c&&Ie(e.attrs,"set","")}(e,n.props,o,r),((e,n,r)=>{const{vnode:o,slots:s}=e;let l=!0,a=t;if(32&o.shapeFlag){const e=n._;e?r&&1===e?l=!1:Rr(s,n,r):(l=!n.$stable,Ir(n,s)),a=n}else n&&($r(e,n),a={default:1});if(l)for(const t in s)Fr(t)||null!=a[t]||delete s[t]})(e,n.children,r),be(),Qt(e),ke()},B=(e,t,n,r,o,s,l,a,i=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,f=t.children,{patchFlag:p,shapeFlag:h}=t;if(p>0){if(128&p)return void z(c,f,n,r,o,s,l,a,i);if(256&p)return void G(c,f,n,r,o,s,l,a,i)}8&h?(16&u&&Q(c,o,s),f!==c&&d(n,f)):16&u?16&h?z(c,f,n,r,o,s,l,a,i):Q(c,o,s,!0):(8&u&&d(n,""),16&h&&P(f,n,r,o,s,l,a,i))},G=(e,t,r,o,s,l,a,i,c)=>{t=t||n;const u=(e=e||n).length,f=t.length,p=Math.min(u,f);let d;for(d=0;d<p;d++){const n=t[d]=c?Oo(t[d]):Lo(t[d]);_(e[d],n,r,null,s,l,a,i,c)}u>f?Q(e,s,l,!0,!1,p):P(t,r,o,s,l,a,i,c,p)},z=(e,t,r,o,s,l,a,i,c)=>{let u=0;const f=t.length;let p=e.length-1,d=f-1;for(;u<=p&&u<=d;){const n=e[u],o=t[u]=c?Oo(t[u]):Lo(t[u]);if(!vo(n,o))break;_(n,o,r,null,s,l,a,i,c),u++}for(;u<=p&&u<=d;){const n=e[p],o=t[d]=c?Oo(t[d]):Lo(t[d]);if(!vo(n,o))break;_(n,o,r,null,s,l,a,i,c),p--,d--}if(u>p){if(u<=d){const e=d+1,n=e<f?t[e].el:o;for(;u<=d;)_(null,t[u]=c?Oo(t[u]):Lo(t[u]),r,n,s,l,a,i,c),u++}}else if(u>d)for(;u<=p;)q(e[u],s,l,!0),u++;else{const h=u,m=u,g=new Map;for(u=m;u<=d;u++){const e=t[u]=c?Oo(t[u]):Lo(t[u]);null!=e.key&&g.set(e.key,u)}let v,y=0;const b=d-m+1;let k=!1,x=0;const w=new Array(b);for(u=0;u<b;u++)w[u]=0;for(u=h;u<=p;u++){const n=e[u];if(y>=b){q(n,s,l,!0);continue}let o;if(null!=n.key)o=g.get(n.key);else for(v=m;v<=d;v++)if(0===w[v-m]&&vo(n,t[v])){o=v;break}void 0===o?q(n,s,l,!0):(w[o-m]=u+1,o>=x?x=o:k=!0,_(n,t[o],r,null,s,l,a,i,c),y++)}const S=k?function(e){const t=e.slice(),n=[0];let r,o,s,l,a;const i=e.length;for(r=0;r<i;r++){const i=e[r];if(0!==i){if(o=n[n.length-1],e[o]<i){t[r]=o,n.push(r);continue}for(s=0,l=n.length-1;s<l;)a=s+l>>1,e[n[a]]<i?s=a+1:l=a;i<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}s=n.length,l=n[s-1];for(;s-- >0;)n[s]=l,l=t[l];return n}(w):n;for(v=S.length-1,u=b-1;u>=0;u--){const e=m+u,n=t[e],p=e+1<f?t[e+1].el:o;0===w[u]?_(null,n,r,p,s,l,a,i,c):k&&(v<0||u!==S[v]?Y(n,r,p,2):v--)}}},Y=(e,t,n,r,l=null)=>{const{el:a,type:i,transition:c,children:u,shapeFlag:f}=e;if(6&f)return void Y(e.component.subTree,t,n,r);if(128&f)return void e.suspense.move(t,n,r);if(64&f)return void i.move(e,t,n,re);if(i===ro){o(a,t,n);for(let e=0;e<u.length;e++)Y(u[e],t,n,r);return void o(e.anchor,t,n)}if(i===lo)return void w(e,t,n);if(2!==r&&1&f&&c)if(0===r)c.beforeEnter(a),o(a,t,n),Mr(()=>c.enter(a),l);else{const{leave:r,delayLeave:l,afterLeave:i}=c,u=()=>{e.ctx.isUnmounted?s(a):o(a,t,n)},f=()=>{r(a,()=>{u(),i&&i()})};l?l(a,u,f):f()}else o(a,t,n)},q=(e,t,n,r=!1,o=!1)=>{const{type:s,props:l,ref:a,children:i,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:p,cacheIndex:d}=e;if(-2===f&&(o=!1),null!=a&&(be(),Ln(a,null,n,e,!0),ke()),null!=d&&(t.renderCache[d]=void 0),256&u)return void t.ctx.deactivate(e);const h=1&u&&p,m=!On(e);let g;if(m&&(g=l&&l.onVnodeBeforeUnmount)&&Fo(g,t,e),6&u)Z(e.component,n,r);else{if(128&u)return void e.suspense.unmount(n,r);h&&an(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,re,r):c&&!c.hasOnce&&(s!==ro||f>0&&64&f)?Q(c,t,n,!1,!0):(s===ro&&384&f||!o&&16&u)&&Q(i,t,n),r&&K(e)}(m&&(g=l&&l.onVnodeUnmounted)||h)&&Mr(()=>{g&&Fo(g,t,e),h&&an(e,null,t,"unmounted")},n)},K=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===ro)return void J(n,r);if(t===lo)return void C(e);const l=()=>{s(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:r}=o,s=()=>t(n,l);r?r(e.el,l,s):s()}else l()},J=(e,t)=>{let n;for(;e!==t;)n=m(e),s(e),e=n;s(t)},Z=(e,t,n)=>{const{bum:r,scope:o,job:s,subTree:l,um:a,m:i,a:c,parent:u,slots:{__:p}}=e;Vr(i),Vr(c),r&&$(r),u&&f(p)&&p.forEach(e=>{u.renderCache[e]=void 0}),o.stop(),s&&(s.flags|=8,q(l,e,t,n)),a&&Mr(a,t),Mr(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Q=(e,t,n,r=!1,o=!1,s=0)=>{for(let l=s;l<e.length;l++)q(e[l],t,n,r,o)},X=e=>{if(6&e.shapeFlag)return X(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=m(e.anchor||e.el),n=t&&t[cn];return n?m(n):t};let te=!1;const ne=(e,t,n)=>{null==e?t._vnode&&q(t._vnode,null,null,!0):_(t._vnode||null,e,t,null,null,null,n),t._vnode=e,te||(te=!0,Qt(),Xt(),te=!1)},re={p:_,um:q,m:Y,r:K,mt:D,mc:P,pc:B,pbc:M,n:X,o:e};let oe;return{render:ne,hydrate:oe,createApp:vr(ne)}}(e)}function jr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Nr({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Wr(e,t,n=!1){const r=e.children,o=t.children;if(f(r)&&f(o))for(let s=0;s<r.length;s++){const e=r[s];let t=o[s];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=o[s]=Oo(o[s]),t.el=e.el),n||-2===t.patchFlag||Wr(e,t)),t.type===oo&&(t.el=e.el),t.type!==so||t.el||(t.el=e.el)}}function Dr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Dr(t)}function Vr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ur=Symbol.for("v-scx"),Hr=()=>br(Ur);function Br(e,t,n){return Gr(e,t,n)}function Gr(e,n,o=t){const{immediate:s,deep:l,flush:i,once:c}=o,u=a({},o),f=n&&s||!n&&"post"!==i;let p;if(Wo)if("sync"===i){const e=Hr();p=e.__watcherHandles||(e.__watcherHandles=[])}else if(!f){const e=()=>{};return e.stop=r,e.resume=r,e.pause=r,e}const d=Io;u.call=(e,t,n)=>Dt(e,d,t,n);let h=!1;"post"===i?u.scheduler=e=>{Mr(e,d&&d.suspense)}:"sync"!==i&&(h=!0,u.scheduler=(e,t)=>{t?e():Jt(e)}),u.augmentJob=e=>{n&&(e.flags|=4),h&&(e.flags|=2,d&&(e.id=d.uid,e.i=d))};const m=jt(e,n,u);return Wo&&(p?p.push(m):f&&m()),m}function zr(e,t,n){const r=this.proxy,o=g(e)?e.includes(".")?Yr(r,e):()=>r[e]:e.bind(r,r);let s;m(t)?s=t:(s=t.handler,n=t);const l=Ao(this),a=Gr(o,s.bind(r),n);return l(),a}function Yr(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const qr=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${O(t)}Modifiers`]||e[`${F(t)}Modifiers`];function Kr(e,n,...r){if(e.isUnmounted)return;const o=e.vnode.props||t;let s=r;const l=n.startsWith("update:"),a=l&&qr(o,n.slice(7));let i;a&&(a.trim&&(s=r.map(e=>g(e)?e.trim():e)),a.number&&(s=r.map(M)));let c=o[i=P(n)]||o[i=P(O(n))];!c&&l&&(c=o[i=P(F(n))]),c&&Dt(c,e,6,s);const u=o[i+"Once"];if(u){if(e.emitted){if(e.emitted[i])return}else e.emitted={};e.emitted[i]=!0,Dt(u,e,6,s)}}function Jr(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;const s=e.emits;let l={},i=!1;if(!m(e)){const r=e=>{const n=Jr(e,t,!0);n&&(i=!0,a(l,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return s||i?(f(s)?s.forEach(e=>l[e]=null):a(l,s),_(e)&&r.set(e,l),l):(_(e)&&r.set(e,null),null)}function Zr(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),u(e,t[0].toLowerCase()+t.slice(1))||u(e,F(t))||u(e,t))}function Qr(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[s],slots:a,attrs:i,emit:c,render:u,renderCache:f,props:p,data:d,setupState:h,ctx:m,inheritAttrs:g}=e,v=on(e);let _,y;try{if(4&n.shapeFlag){const e=o||r,t=e;_=Lo(u.call(t,e,f,p,h,d,m)),y=i}else{const e=t;0,_=Lo(e.length>1?e(p,{attrs:i,slots:a,emit:c}):e(p,null)),y=t.props?i:Xr(i)}}catch(k){ao.length=0,Vt(k,e,1),_=ko(so)}let b=_;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(l)&&(y=eo(y,s)),b=xo(b,y,!1,!0))}return n.dirs&&(b=xo(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&xn(b,n.transition),_=b,on(v),_}const Xr=e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t},eo=(e,t)=>{const n={};for(const r in e)l(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function to(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Zr(n,s))return!0}return!1}const no=e=>e.__isSuspense;const ro=Symbol.for("v-fgt"),oo=Symbol.for("v-txt"),so=Symbol.for("v-cmt"),lo=Symbol.for("v-stc"),ao=[];let io=null;function co(e=!1){ao.push(io=e?null:[])}let uo=1;function fo(e,t=!1){uo+=e,e<0&&io&&t&&(io.hasOnce=!0)}function po(e){return e.dynamicChildren=uo>0?io||n:null,ao.pop(),io=ao[ao.length-1]||null,uo>0&&io&&io.push(e),e}function ho(e,t,n,r,o,s){return po(bo(e,t,n,r,o,s,!0))}function mo(e,t,n,r,o){return po(ko(e,t,n,r,o,!0))}function go(e){return!!e&&!0===e.__v_isVNode}function vo(e,t){return e.type===t.type&&e.key===t.key}const _o=({key:e})=>null!=e?e:null,yo=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?g(e)||xt(e)||m(e)?{i:nn,r:e,k:t,f:!!n}:e:null);function bo(e,t=null,n=null,r=0,o=null,s=(e===ro?0:1),l=!1,a=!1){const i={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&_o(t),ref:t&&yo(t),scopeId:rn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:nn};return a?(Eo(i,n),128&s&&e.normalize(i)):n&&(i.shapeFlag|=g(n)?8:16),uo>0&&!l&&io&&(i.patchFlag>0||6&s)&&32!==i.patchFlag&&io.push(i),i}const ko=function(e,t=null,n=null,r=0,o=null,s=!1){e&&e!==Yn||(e=so);if(go(e)){const r=xo(e,t,!0);return n&&Eo(r,n),uo>0&&!s&&io&&(6&r.shapeFlag?io[io.indexOf(e)]=r:io.push(r)),r.patchFlag=-2,r}l=e,m(l)&&"__vccOpts"in l&&(e=e.__vccOpts);var l;if(t){t=function(e){return e?vt(e)||wr(e)?a({},e):e:null}(t);let{class:e,style:n}=t;e&&!g(e)&&(t.class=H(e)),_(n)&&(vt(n)&&!f(n)&&(n=a({},n)),t.style=N(n))}const i=g(e)?1:no(e)?128:un(e)?64:_(e)?4:m(e)?2:0;return bo(e,t,n,r,o,i,s,!0)};function xo(e,t,n=!1,r=!1){const{props:o,ref:l,patchFlag:a,children:i,transition:c}=e,u=t?function(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=H([t.class,r.class]));else if("style"===e)t.style=N([t.style,r.style]);else if(s(e)){const n=t[e],o=r[e];!o||n===o||f(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}(o||{},t):o,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&_o(u),ref:t&&t.ref?n&&l?f(l)?l.concat(yo(t)):[l,yo(t)]:yo(t):l,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ro?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&xo(e.ssContent),ssFallback:e.ssFallback&&xo(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&xn(p,c.clone(p)),p}function wo(e=" ",t=0){return ko(oo,null,e,t)}function So(e,t){const n=ko(lo,null,e);return n.staticCount=t,n}function Co(e="",t=!1){return t?(co(),mo(so,null,e)):ko(so,null,e)}function Lo(e){return null==e||"boolean"==typeof e?ko(so):f(e)?ko(ro,null,e.slice()):go(e)?Oo(e):ko(oo,null,String(e))}function Oo(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:xo(e)}function Eo(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),Eo(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||wr(t)?3===r&&nn&&(1===nn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=nn}}else m(t)?(t={default:t,_ctx:nn},n=32):(t=String(t),64&r?(n=16,t=[wo(t)]):n=8);e.children=t,e.shapeFlag|=n}function Fo(e,t,n,r=null){Dt(e,t,7,[n,r])}const To=mr();let Po=0;let Io=null;const $o=()=>Io||nn;let Ro,Mo;{const e=j(),t=(t,n)=>{let r;return(r=e[t])||(r=e[t]=[]),r.push(n),e=>{r.length>1?r.forEach(t=>t(e)):r[0](e)}};Ro=t("__VUE_INSTANCE_SETTERS__",e=>Io=e),Mo=t("__VUE_SSR_SETTERS__",e=>Wo=e)}const Ao=e=>{const t=Io;return Ro(e),e.scope.on(),()=>{e.scope.off(),Ro(t)}},jo=()=>{Io&&Io.scope.off(),Ro(null)};function No(e){return 4&e.vnode.shapeFlag}let Wo=!1;function Do(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=Tt(t)),Vo(e)}function Vo(e,t,n){const o=e.type;e.render||(e.render=o.render||r);{const t=Ao(e);be();try{or(e)}finally{ke(),t()}}}const Uo={get:(e,t)=>(Pe(e,0,""),e[t])};function Ho(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Tt(yt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Xn?Xn[n](e):void 0,has:(e,t)=>t in e||t in Xn})):e.proxy}function Bo(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const Go=(e,t)=>{const n=function(e,t,n=!1){let r,o;return m(e)?r=e:(r=e.get,o=e.set),new $t(r,o,n)}(e,0,Wo);return n};function zo(e,t,n){const r=arguments.length;return 2===r?_(t)&&!f(t)?go(t)?ko(e,null,[t]):ko(e,t):ko(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&go(n)&&(n=[n]),ko(e,t,n))}const Yo="3.5.17";
/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let qo;const Ko="undefined"!=typeof window&&window.trustedTypes;if(Ko)try{qo=Ko.createPolicy("vue",{createHTML:e=>e})}catch(Cf){}const Jo=qo?e=>qo.createHTML(e):e=>e,Zo="undefined"!=typeof document?document:null,Qo=Zo&&Zo.createElement("template"),Xo={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o="svg"===t?Zo.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Zo.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Zo.createElement(e,{is:n}):Zo.createElement(e);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>Zo.createTextNode(e),createComment:e=>Zo.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Zo.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const l=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==s&&(o=o.nextSibling););else{Qo.innerHTML=Jo("svg"===r?`<svg>${e}</svg>`:"mathml"===r?`<math>${e}</math>`:e);const o=Qo.content;if("svg"===r||"mathml"===r){const e=o.firstChild;for(;e.firstChild;)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},es="transition",ts="animation",ns=Symbol("_vtc"),rs={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},os=a({},hn,rs),ss=(e=>(e.displayName="Transition",e.props=os,e))((e,{slots:t})=>zo(vn,function(e){const t={};for(const a in e)a in rs||(t[a]=e[a]);if(!1===e.css)return t;const{name:n="v",type:r,duration:o,enterFromClass:s=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:i=`${n}-enter-to`,appearFromClass:c=s,appearActiveClass:u=l,appearToClass:f=i,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(_(e))return[is(e.enter),is(e.leave)];{const t=is(e);return[t,t]}}(o),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:k,onLeave:x,onLeaveCancelled:w,onBeforeAppear:S=y,onAppear:C=b,onAppearCancelled:L=k}=t,O=(e,t,n,r)=>{e._enterCancelled=r,us(e,t?f:i),us(e,t?u:l),n&&n()},E=(e,t)=>{e._isLeaving=!1,us(e,p),us(e,h),us(e,d),t&&t()},F=e=>(t,n)=>{const o=e?C:b,l=()=>O(t,e,n);ls(o,[t,l]),fs(()=>{us(t,e?c:s),cs(t,e?f:i),as(o)||ds(t,r,g,l)})};return a(t,{onBeforeEnter(e){ls(y,[e]),cs(e,s),cs(e,l)},onBeforeAppear(e){ls(S,[e]),cs(e,c),cs(e,u)},onEnter:F(!1),onAppear:F(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>E(e,t);cs(e,p),e._enterCancelled?(cs(e,d),gs()):(gs(),cs(e,d)),fs(()=>{e._isLeaving&&(us(e,p),cs(e,h),as(x)||ds(e,r,v,n))}),ls(x,[e,n])},onEnterCancelled(e){O(e,!1,void 0,!0),ls(k,[e])},onAppearCancelled(e){O(e,!0,void 0,!0),ls(L,[e])},onLeaveCancelled(e){E(e),ls(w,[e])}})}(e),t)),ls=(e,t=[])=>{f(e)?e.forEach(e=>e(...t)):e&&e(...t)},as=e=>!!e&&(f(e)?e.some(e=>e.length>1):e.length>1);function is(e){const t=(e=>{const t=g(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function cs(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e[ns]||(e[ns]=new Set)).add(t)}function us(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const n=e[ns];n&&(n.delete(t),n.size||(e[ns]=void 0))}function fs(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let ps=0;function ds(e,t,n,r){const o=e._endId=++ps,s=()=>{o===e._endId&&r()};if(null!=n)return setTimeout(s,n);const{type:l,timeout:a,propCount:i}=function(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),o=r(`${es}Delay`),s=r(`${es}Duration`),l=hs(o,s),a=r(`${ts}Delay`),i=r(`${ts}Duration`),c=hs(a,i);let u=null,f=0,p=0;t===es?l>0&&(u=es,f=l,p=s.length):t===ts?c>0&&(u=ts,f=c,p=i.length):(f=Math.max(l,c),u=f>0?l>c?es:ts:null,p=u?u===es?s.length:i.length:0);const d=u===es&&/\b(transform|all)(,|$)/.test(r(`${es}Property`).toString());return{type:u,timeout:f,propCount:p,hasTransform:d}}(e,t);if(!l)return r();const c=l+"end";let u=0;const f=()=>{e.removeEventListener(c,p),s()},p=t=>{t.target===e&&++u>=i&&f()};setTimeout(()=>{u<i&&f()},a+1),e.addEventListener(c,p)}function hs(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>ms(t)+ms(e[n])))}function ms(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function gs(){return document.body.offsetHeight}const vs=Symbol("_vod"),_s=Symbol("_vsh"),ys=Symbol(""),bs=/(^|;)\s*display\s*:/;const ks=/\s*!important$/;function xs(e,t,n){if(f(n))n.forEach(n=>xs(e,t,n));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=function(e,t){const n=Ss[t];if(n)return n;let r=O(t);if("filter"!==r&&r in e)return Ss[t]=r;r=T(r);for(let o=0;o<ws.length;o++){const n=ws[o]+r;if(n in e)return Ss[t]=n}return t}(e,t);ks.test(n)?e.setProperty(F(r),n.replace(ks,""),"important"):e[r]=n}}const ws=["Webkit","Moz","ms"],Ss={};const Cs="http://www.w3.org/1999/xlink";function Ls(e,t,n,r,o,s=B(t)){r&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(Cs,t.slice(6,t.length)):e.setAttributeNS(Cs,t,n):null==n||s&&!G(n)?e.removeAttribute(t):e.setAttribute(t,s?"":v(n)?String(n):n)}function Os(e,t,n,r,o){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?Jo(n):n));const s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){const r="OPTION"===s?e.getAttribute("value")||"":e.value,o=null==n?"checkbox"===e.type?"on":"":String(n);return r===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const r=typeof e[t];"boolean"===r?n=G(n):null==n&&"string"===r?(n="",l=!0):"number"===r&&(n=0,l=!0)}try{e[t]=n}catch(Cf){}l&&e.removeAttribute(o||t)}function Es(e,t,n,r){e.addEventListener(t,n,r)}const Fs=Symbol("_vei");function Ts(e,t,n,r,o=null){const s=e[Fs]||(e[Fs]={}),l=s[t];if(r&&l)l.value=r;else{const[n,a]=function(e){let t;if(Ps.test(e)){let n;for(t={};n=e.match(Ps);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):F(e.slice(2));return[n,t]}(t);if(r){const l=s[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Dt(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=Rs(),n}(r,o);Es(e,n,l,a)}else l&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,n,l,a),s[t]=void 0)}}const Ps=/(?:Once|Passive|Capture)$/;let Is=0;const $s=Promise.resolve(),Rs=()=>Is||($s.then(()=>Is=0),Is=Date.now());const Ms=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const As=e=>{const t=e.props["onUpdate:modelValue"]||!1;return f(t)?e=>$(t,e):t};function js(e){e.target.composing=!0}function Ns(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ws=Symbol("_assign"),Ds={created(e,{modifiers:{lazy:t,trim:n,number:r}},o){e[Ws]=As(o);const s=r||o.props&&"number"===o.props.type;Es(e,t?"change":"input",t=>{if(t.target.composing)return;let r=e.value;n&&(r=r.trim()),s&&(r=M(r)),e[Ws](r)}),n&&Es(e,"change",()=>{e.value=e.value.trim()}),t||(Es(e,"compositionstart",js),Es(e,"compositionend",Ns),Es(e,"change",Ns))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:o,number:s}},l){if(e[Ws]=As(l),e.composing)return;const a=null==t?"":t;if((!s&&"number"!==e.type||/^0\d/.test(e.value)?e.value:M(e.value))!==a){if(document.activeElement===e&&"range"!==e.type){if(r&&t===n)return;if(o&&e.value.trim()===a)return}e.value=a}}},Vs={deep:!0,created(e,t,n){e[Ws]=As(n),Es(e,"change",()=>{const t=e._modelValue,n=zs(e),r=e.checked,o=e[Ws];if(f(t)){const e=Y(t,n),s=-1!==e;if(r&&!s)o(t.concat(n));else if(!r&&s){const n=[...t];n.splice(e,1),o(n)}}else if(d(t)){const e=new Set(t);r?e.add(n):e.delete(n),o(e)}else o(Ys(e,r))})},mounted:Us,beforeUpdate(e,t,n){e[Ws]=As(n),Us(e,t,n)}};function Us(e,{value:t,oldValue:n},r){let o;if(e._modelValue=t,f(t))o=Y(t,r.props.value)>-1;else if(d(t))o=t.has(r.props.value);else{if(t===n)return;o=z(t,Ys(e,!0))}e.checked!==o&&(e.checked=o)}const Hs={created(e,{value:t},n){e.checked=z(t,n.props.value),e[Ws]=As(n),Es(e,"change",()=>{e[Ws](zs(e))})},beforeUpdate(e,{value:t,oldValue:n},r){e[Ws]=As(r),t!==n&&(e.checked=z(t,r.props.value))}},Bs={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const o=d(t);Es(e,"change",()=>{const t=Array.prototype.filter.call(e.options,e=>e.selected).map(e=>n?M(zs(e)):zs(e));e[Ws](e.multiple?o?new Set(t):t:t[0]),e._assigning=!0,Kt(()=>{e._assigning=!1})}),e[Ws]=As(r)},mounted(e,{value:t}){Gs(e,t)},beforeUpdate(e,t,n){e[Ws]=As(n)},updated(e,{value:t}){e._assigning||Gs(e,t)}};function Gs(e,t){const n=e.multiple,r=f(t);if(!n||r||d(t)){for(let o=0,s=e.options.length;o<s;o++){const s=e.options[o],l=zs(s);if(n)if(r){const e=typeof l;s.selected="string"===e||"number"===e?t.some(e=>String(e)===String(l)):Y(t,l)>-1}else s.selected=t.has(l);else if(z(zs(s),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function zs(e){return"_value"in e?e._value:e.value}function Ys(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const qs=["ctrl","shift","alt","meta"],Ks={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>qs.some(n=>e[`${n}Key`]&&!t.includes(n))},Js=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(n,...r)=>{for(let e=0;e<t.length;e++){const r=Ks[t[e]];if(r&&r(n,t))return}return e(n,...r)})},Zs=a({patchProp:(e,t,n,r,o,a)=>{const i="svg"===o;"class"===t?function(e,t,n){const r=e[ns];r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,i):"style"===t?function(e,t,n){const r=e.style,o=g(n);let s=!1;if(n&&!o){if(t)if(g(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&xs(r,t,"")}else for(const e in t)null==n[e]&&xs(r,e,"");for(const e in n)"display"===e&&(s=!0),xs(r,e,n[e])}else if(o){if(t!==n){const e=r[ys];e&&(n+=";"+e),r.cssText=n,s=bs.test(n)}}else t&&e.removeAttribute("style");vs in e&&(e[vs]=s?r.display:"",e[_s]&&(r.display="none"))}(e,n,r):s(t)?l(t)||Ts(e,t,0,r,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&Ms(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(Ms(t)&&g(n))return!1;return t in e}(e,t,r,i))?(Os(e,t,r),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||Ls(e,t,r,i,0,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&g(r)?("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),Ls(e,t,r,i)):Os(e,O(t),r,0,t)}},Xo);let Qs;const Xs=(...e)=>{const t=(Qs||(Qs=Ar(Zs))).createApp(...e),{mount:n}=t;return t.mount=e=>{const r=function(e){if(g(e)){return document.querySelector(e)}return e}
/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */(e);if(!r)return;const o=t._component;m(o)||o.render||o.template||(o.template=r.innerHTML),1===r.nodeType&&(r.textContent="");const s=n(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),s},t};let el;const tl=e=>el=e,nl=Symbol();function rl(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var ol,sl;function ll(){const e=te(!0),t=e.run(()=>wt({}));let n=[],r=[];const o=yt({install(e){tl(o),o._a=e,e.provide(nl,o),e.config.globalProperties.$pinia=o,r.forEach(e=>n.push(e)),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}(sl=ol||(ol={})).direct="direct",sl.patchObject="patch object",sl.patchFunction="patch function";const al=()=>{};function il(e,t,n,r=al){e.push(t);const o=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};return!n&&ne()&&re(o),o}function cl(e,...t){e.slice().forEach(e=>{e(...t)})}const ul=e=>e(),fl=Symbol(),pl=Symbol();function dl(e,t){e instanceof Map&&t instanceof Map?t.forEach((t,n)=>e.set(n,t)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];rl(o)&&rl(r)&&e.hasOwnProperty(n)&&!xt(r)&&!ht(r)?e[n]=dl(o,r):e[n]=r}return e}const hl=Symbol();function ml(e){return!rl(e)||!Object.prototype.hasOwnProperty.call(e,hl)}const{assign:gl}=Object;function vl(e){return!(!xt(e)||!e.effect)}function _l(e,t,n,r){const{state:o,actions:s,getters:l}=t,a=n.state.value[e];let i;return i=yl(e,function(){a||(n.state.value[e]=o?o():{});const t=function(e){const t=f(e)?new Array(e.length):{};for(const n in e)t[n]=It(e,n);return t}(n.state.value[e]);return gl(t,s,Object.keys(l||{}).reduce((t,r)=>(t[r]=yt(Go(()=>{tl(n);const t=n._s.get(e);return l[r].call(t,t)})),t),{}))},t,n,r,!0),i}function yl(e,t,n={},r,o,s){let l;const a=gl({actions:{}},n),i={deep:!0};let c,u,f,p=[],d=[];const h=r.state.value[e];let m;function g(t){let n;c=u=!1,"function"==typeof t?(t(r.state.value[e]),n={type:ol.patchFunction,storeId:e,events:f}):(dl(r.state.value[e],t),n={type:ol.patchObject,payload:t,storeId:e,events:f});const o=m=Symbol();Kt().then(()=>{m===o&&(c=!0)}),u=!0,cl(p,n,r.state.value[e])}s||h||(r.state.value[e]={}),wt({});const v=s?function(){const{state:e}=n,t=e?e():{};this.$patch(e=>{gl(e,t)})}:al;const _=(t,n="")=>{if(fl in t)return t[pl]=n,t;const o=function(){tl(r);const n=Array.from(arguments),s=[],l=[];let a;cl(d,{args:n,name:o[pl],store:y,after:function(e){s.push(e)},onError:function(e){l.push(e)}});try{a=t.apply(this&&this.$id===e?this:y,n)}catch(i){throw cl(l,i),i}return a instanceof Promise?a.then(e=>(cl(s,e),e)).catch(e=>(cl(l,e),Promise.reject(e))):(cl(s,a),a)};return o[fl]=!0,o[pl]=n,o},y=ut({_p:r,$id:e,$onAction:il.bind(null,d),$patch:g,$reset:v,$subscribe(t,n={}){const o=il(p,t,n.detached,()=>s()),s=l.run(()=>Br(()=>r.state.value[e],r=>{("sync"===n.flush?u:c)&&t({storeId:e,type:ol.direct,events:f},r)},gl({},i,n)));return o},$dispose:function(){l.stop(),p=[],d=[],r._s.delete(e)}});r._s.set(e,y);const b=(r._a&&r._a.runWithContext||ul)(()=>r._e.run(()=>(l=te()).run(()=>t({action:_}))));for(const k in b){const t=b[k];if(xt(t)&&!vl(t)||ht(t))s||(h&&ml(t)&&(xt(t)?t.value=h[k]:dl(t,h[k])),r.state.value[e][k]=t);else if("function"==typeof t){const e=_(t,k);b[k]=e,a.actions[k]=t}}return gl(y,b),gl(_t(y),b),Object.defineProperty(y,"$state",{get:()=>r.state.value[e],set:e=>{g(t=>{gl(t,e)})}}),r._p.forEach(e=>{gl(y,l.run(()=>e({store:y,app:r._a,pinia:r,options:a})))}),h&&s&&n.hydrate&&n.hydrate(y.$state,h),c=!0,u=!0,y}
/*! #__NO_SIDE_EFFECTS__ */function bl(e,t,n){let r;const o="function"==typeof t;function s(n,s){(n=n||(!!(Io||nn||_r)?br(nl,null):null))&&tl(n),(n=el)._s.has(e)||(o?yl(e,t,r,n):_l(e,r,n));return n._s.get(e)}return r=o?n:t,s.$id=e,s}
/*!
  * shared v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const kl="undefined"!=typeof window,xl=(e,t=!1)=>t?Symbol.for(e):Symbol(e),wl=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Sl=e=>"number"==typeof e&&isFinite(e),Cl=e=>"[object RegExp]"===Vl(e),Ll=e=>Ul(e)&&0===Object.keys(e).length,Ol=Object.assign,El=Object.create,Fl=(e=null)=>El(e);let Tl;const Pl=()=>Tl||(Tl="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:Fl());function Il(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const $l=Object.prototype.hasOwnProperty;function Rl(e,t){return $l.call(e,t)}const Ml=Array.isArray,Al=e=>"function"==typeof e,jl=e=>"string"==typeof e,Nl=e=>"boolean"==typeof e,Wl=e=>null!==e&&"object"==typeof e,Dl=Object.prototype.toString,Vl=e=>Dl.call(e),Ul=e=>"[object Object]"===Vl(e);function Hl(e,t=""){return e.reduce((e,n,r)=>0===r?e+n:e+t+n,"")}function Bl(e,t){}const Gl=e=>!Wl(e)||Ml(e);function zl(e,t){if(Gl(e)||Gl(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach(r=>{"__proto__"!==r&&(Wl(e[r])&&!Wl(t[r])&&(t[r]=Array.isArray(e[r])?[]:Fl()),Gl(t[r])||Gl(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))})}}
/*!
  * message-compiler v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Yl(e,t,n){return{start:e,end:t}}const ql=1,Kl=2,Jl=3,Zl=4,Ql=5,Xl=6,ea=7,ta=8,na=9,ra=10,oa=11,sa=12,la=13,aa=14;function ia(e,t,n={}){const{domain:r,messages:o,args:s}=n,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=r,l}function ca(e){throw e}const ua=" ",fa="\n",pa=String.fromCharCode(8232),da=String.fromCharCode(8233);function ha(e){const t=e;let n=0,r=1,o=1,s=0;const l=e=>"\r"===t[e]&&t[e+1]===fa,a=e=>t[e]===da,i=e=>t[e]===pa,c=e=>l(e)||(e=>t[e]===fa)(e)||a(e)||i(e),u=e=>l(e)||a(e)||i(e)?fa:t[e];function f(){return s=0,c(n)&&(r++,o=0),l(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>s,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+s),next:f,peek:function(){return l(n+s)&&s++,s++,t[n+s]},reset:function(){n=0,r=1,o=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=n+s;for(;e!==n;)f();s=0}}}const ma=void 0;function ga(e,t={}){const n=!1!==t.location,r=ha(e),o=()=>r.index(),s=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},l=s(),a=o(),i={currentType:13,offset:a,startLoc:l,endLoc:l,lastType:13,lastOffset:a,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},c=()=>i,{onError:u}=t;function f(e,t,r,...o){const s=c();if(t.column+=r,t.offset+=r,u){const r=ia(e,n?Yl(s.startLoc,t):null,{domain:"tokenizer",args:o});u(r)}}function p(e,t,r){e.endLoc=s(),e.currentType=t;const o={type:t};return n&&(o.loc=Yl(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const d=e=>p(e,13);function h(e,t){return e.currentChar()===t?(e.next(),t):(f(ql,s(),0,t),"")}function m(e){let t="";for(;e.currentPeek()===ua||e.currentPeek()===fa;)t+=e.currentPeek(),e.peek();return t}function g(e){const t=m(e);return e.skipToPeek(),t}function v(e){if(e===ma)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function _(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=function(e){if(e===ma)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function y(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function b(e,t=!0){const n=(t=!1,r="")=>{const o=e.currentPeek();return"{"===o?t:"@"!==o&&o?"|"===o?!(r===ua||r===fa):o===ua?(e.peek(),n(!0,ua)):o!==fa||(e.peek(),n(!0,fa)):t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===ma?ma:t(n)?(e.next(),n):null}function x(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function w(e){return k(e,x)}function S(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function C(e){return k(e,S)}function L(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function O(e){return k(e,L)}function E(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function F(e){return k(e,E)}function T(e){let t="",n="";for(;t=O(e);)n+=t;return n}function P(e){return"'"!==e&&e!==fa}function I(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return $(e,t,4);case"U":return $(e,t,6);default:return f(Zl,s(),0,t),""}}function $(e,t,n){h(e,t);let r="";for(let o=0;o<n;o++){const n=F(e);if(!n){f(Ql,s(),0,`\\${t}${r}${e.currentChar()}`);break}r+=n}return`\\${t}${r}`}function R(e){return"{"!==e&&"}"!==e&&e!==ua&&e!==fa}function M(e){g(e);const t=h(e,"|");return g(e),t}function A(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&f(na,s(),0),e.next(),n=p(t,2,"{"),g(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&f(ta,s(),0),e.next(),n=p(t,3,"}"),t.braceNest--,t.braceNest>0&&g(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&f(ea,s(),0),n=j(e,t)||d(t),t.braceNest=0,n;default:{let r=!0,o=!0,l=!0;if(y(e))return t.braceNest>0&&f(ea,s(),0),n=p(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return f(ea,s(),0),t.braceNest=0,N(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=v(e.currentPeek());return e.resetPeek(),r}(e,t))return n=p(t,4,function(e){g(e);let t="",n="";for(;t=C(e);)n+=t;return e.currentChar()===ma&&f(ea,s(),0),n}(e)),g(e),n;if(o=_(e,t))return n=p(t,5,function(e){g(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${T(e)}`):t+=T(e),e.currentChar()===ma&&f(ea,s(),0),t}(e)),g(e),n;if(l=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=p(t,6,function(e){g(e),h(e,"'");let t="",n="";for(;t=k(e,P);)n+="\\"===t?I(e):t;const r=e.currentChar();return r===fa||r===ma?(f(Jl,s(),0),r===fa&&(e.next(),h(e,"'")),n):(h(e,"'"),n)}(e)),g(e),n;if(!r&&!o&&!l)return n=p(t,12,function(e){g(e);let t="",n="";for(;t=k(e,R);)n+=t;return n}(e)),f(Kl,s(),0,n.value),g(e),n;break}}return n}function j(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||o!==fa&&o!==ua||f(ra,s(),0),o){case"@":return e.next(),r=p(t,7,"@"),t.inLinked=!0,r;case".":return g(e),e.next(),p(t,8,".");case":":return g(e),e.next(),p(t,9,":");default:return y(e)?(r=p(t,1,M(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(g(e),j(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const r=v(e.currentPeek());return e.resetPeek(),r}(e,t)?(g(e),p(t,11,function(e){let t="",n="";for(;t=w(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?v(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===ua||!t)&&(t===fa?(e.peek(),r()):b(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(g(e),"{"===o?A(e,t)||r:p(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===ua?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&f(ra,s(),0),t.braceNest=0,t.inLinked=!1,N(e,t))}}function N(e,t){let n={type:13};if(t.braceNest>0)return A(e,t)||d(t);if(t.inLinked)return j(e,t)||d(t);switch(e.currentChar()){case"{":return A(e,t)||d(t);case"}":return f(Xl,s(),0),e.next(),p(t,3,"}");case"@":return j(e,t)||d(t);default:if(y(e))return n=p(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(b(e))return p(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===ua||n===fa)if(b(e))t+=n,e.next();else{if(y(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:l}=i;return i.lastType=e,i.lastOffset=t,i.lastStartLoc=n,i.lastEndLoc=l,i.offset=o(),i.startLoc=s(),r.currentChar()===ma?p(i,13):N(r,i)},currentOffset:o,currentPosition:s,context:c}}const va=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function _a(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function ya(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,r,o,s,...l){const a=e.currentPosition();if(a.offset+=s,a.column+=s,n){const e=ia(r,t?Yl(o,a):null,{domain:"parser",args:l});n(e)}}function o(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function s(e,n,r,o){t&&(e.end=n,e.loc&&(e.loc.end=r))}function l(e,t){const n=e.context(),r=o(3,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,a=o(5,r,l);return a.index=parseInt(t,10),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function i(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,a=o(4,r,l);return a.key=t,e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function c(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,a=o(9,r,l);return a.value=t.replace(va,_a),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function u(e){const t=e.context(),n=o(6,t.offset,t.startLoc);let l=e.nextToken();if(8===l.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:l,lastStartLoc:a}=n,i=o(8,l,a);return 11!==t.type?(r(e,sa,n.lastStartLoc,0),i.value="",s(i,l,a),{nextConsumeToken:t,node:i}):(null==t.value&&r(e,aa,n.lastStartLoc,0,ba(t)),i.value=t.value||"",s(i,e.currentOffset(),e.currentPosition()),{node:i})}(e);n.modifier=t.node,l=t.nextConsumeToken||e.nextToken()}switch(9!==l.type&&r(e,aa,t.lastStartLoc,0,ba(l)),l=e.nextToken(),2===l.type&&(l=e.nextToken()),l.type){case 10:null==l.value&&r(e,aa,t.lastStartLoc,0,ba(l)),n.key=function(e,t){const n=e.context(),r=o(7,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}(e,l.value||"");break;case 4:null==l.value&&r(e,aa,t.lastStartLoc,0,ba(l)),n.key=i(e,l.value||"");break;case 5:null==l.value&&r(e,aa,t.lastStartLoc,0,ba(l)),n.key=a(e,l.value||"");break;case 6:null==l.value&&r(e,aa,t.lastStartLoc,0,ba(l)),n.key=c(e,l.value||"");break;default:{r(e,la,t.lastStartLoc,0);const a=e.context(),i=o(7,a.offset,a.startLoc);return i.value="",s(i,a.offset,a.startLoc),n.key=i,s(n,a.offset,a.startLoc),{nextConsumeToken:l,node:n}}}return s(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let f=null;do{const o=f||e.nextToken();switch(f=null,o.type){case 0:null==o.value&&r(e,aa,t.lastStartLoc,0,ba(o)),n.items.push(l(e,o.value||""));break;case 5:null==o.value&&r(e,aa,t.lastStartLoc,0,ba(o)),n.items.push(a(e,o.value||""));break;case 4:null==o.value&&r(e,aa,t.lastStartLoc,0,ba(o)),n.items.push(i(e,o.value||""));break;case 6:null==o.value&&r(e,aa,t.lastStartLoc,0,ba(o)),n.items.push(c(e,o.value||""));break;case 7:{const t=u(e);n.items.push(t.node),f=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return s(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function p(e){const t=e.context(),{offset:n,startLoc:l}=t,a=f(e);return 13===t.currentType?a:function(e,t,n,l){const a=e.context();let i=0===l.items.length;const c=o(1,t,n);c.cases=[],c.cases.push(l);do{const t=f(e);i||(i=0===t.items.length),c.cases.push(t)}while(13!==a.currentType);return i&&r(e,oa,n,0),s(c,e.currentOffset(),e.currentPosition()),c}(e,n,l,a)}return{parse:function(n){const l=ga(n,Ol({},e)),a=l.context(),i=o(0,a.offset,a.startLoc);return t&&i.loc&&(i.loc.source=n),i.body=p(l),e.onCacheKey&&(i.cacheKey=e.onCacheKey(n)),13!==a.currentType&&r(l,aa,a.lastStartLoc,0,n[a.offset]||""),s(i,l.currentOffset(),l.currentPosition()),i}}}function ba(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function ka(e,t){for(let n=0;n<e.length;n++)xa(e[n],t)}function xa(e,t){switch(e.type){case 1:ka(e.cases,t),t.helper("plural");break;case 2:ka(e.items,t);break;case 6:xa(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function wa(e,t={}){const n=function(e){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&xa(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function Sa(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=Hl(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function Ca(e){switch(e.t=e.type,e.type){case 0:{const t=e;Ca(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)Ca(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)Ca(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Ca(t.key),t.k=t.key,delete t.key,t.modifier&&(Ca(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function La(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?La(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(La(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let s=0;s<o&&(La(e,t.items[s]),s!==o-1);s++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),La(e,t.key),t.modifier?(e.push(", "),La(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function Oa(e,t={}){const n=Ol({},t),r=!!n.jit,o=!!n.minify,s=null==n.optimize||n.optimize,l=ya(n).parse(e);return r?(s&&function(e){const t=e.body;2===t.type?Sa(t):t.cases.forEach(e=>Sa(e))}(l),o&&Ca(l),{ast:l,code:""}):(wa(l,n),((e,t={})=>{const n=jl(t.mode)?t.mode:"normal",r=jl(t.filename)?t.filename:"message.intl";t.sourceMap;const o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,l=e.helpers||[],a=function(e,t){const{filename:n,breakLineCode:r,needIndent:o}=t,s=!1!==t.location,l={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:o,indentLevel:0};function a(e,t){l.code+=e}function i(e,t=!0){const n=t?r:"";a(o?n+"  ".repeat(e):n)}return s&&e.loc&&(l.source=e.loc.source),{context:()=>l,push:a,indent:function(e=!0){const t=++l.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--l.indentLevel;e&&i(t)},newline:function(){i(l.indentLevel)},helper:e=>`_${e}`,needIndent:()=>l.needIndent}}(e,{filename:r,breakLineCode:o,needIndent:s});a.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(s),l.length>0&&(a.push(`const { ${Hl(l.map(e=>`${e}: _${e}`),", ")} } = ctx`),a.newline()),a.push("return "),La(a,e),a.deindent(s),a.push("}"),delete e.helpers;const{code:i,map:c}=a.context();return{ast:e,code:i,map:c?c.toJSON():void 0}})(l,n))}
/*!
  * core-base v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Ea(e){return Wl(e)&&0===Ra(e)&&(Rl(e,"b")||Rl(e,"body"))}const Fa=["b","body"];const Ta=["c","cases"];const Pa=["s","static"];const Ia=["i","items"];const $a=["t","type"];function Ra(e){return Wa(e,$a)}const Ma=["v","value"];function Aa(e,t){const n=Wa(e,Ma);if(null!=n)return n;throw Va(t)}const ja=["m","modifier"];const Na=["k","key"];function Wa(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(Rl(e,n)&&null!=e[n])return e[n]}return n}const Da=[...Fa,...Ta,...Pa,...Ia,...Na,...ja,...Ma,...$a];function Va(e){return new Error(`unhandled node type: ${e}`)}function Ua(e){return t=>function(e,t){const n=(r=t,Wa(r,Fa));var r;if(null==n)throw Va(0);if(1===Ra(n)){const t=function(e){return Wa(e,Ta,[])}(n);return e.plural(t.reduce((t,n)=>[...t,Ha(e,n)],[]))}return Ha(e,n)}(t,e)}function Ha(e,t){const n=function(e){return Wa(e,Pa)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return Wa(e,Ia,[])}(t).reduce((t,n)=>[...t,Ba(e,n)],[]);return e.normalize(n)}}function Ba(e,t){const n=Ra(t);switch(n){case 3:case 9:case 7:case 8:return Aa(t,n);case 4:{const r=t;if(Rl(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(Rl(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Va(n)}case 5:{const r=t;if(Rl(r,"i")&&Sl(r.i))return e.interpolate(e.list(r.i));if(Rl(r,"index")&&Sl(r.index))return e.interpolate(e.list(r.index));throw Va(n)}case 6:{const n=t,r=function(e){return Wa(e,ja)}(n),o=function(e){const t=Wa(e,Na);if(t)return t;throw Va(6)}(n);return e.linked(Ba(e,o),r?Ba(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const Ga=e=>e;let za=Fl();let Ya=null;const qa=Ka("function:translate");function Ka(e){return t=>Ya&&Ya.emit(e,t)}const Ja=17,Za=18,Qa=19,Xa=21,ei=22,ti=23;function ni(e){return ia(e,null,void 0)}function ri(e,t){return null!=t.locale?si(t.locale):si(e.locale)}let oi;function si(e){if(jl(e))return e;if(Al(e)){if(e.resolvedOnce&&null!=oi)return oi;if("Function"===e.constructor.name){const n=e();if(Wl(t=n)&&Al(t.then)&&Al(t.catch))throw ni(Xa);return oi=n}throw ni(ei)}throw ni(ti);var t}function li(e,t,n){return[...new Set([n,...Ml(t)?t:Wl(t)?Object.keys(t):jl(t)?[t]:[n]])]}function ai(e,t,n){const r=jl(n)?n:vi,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let s=o.__localeChainCache.get(r);if(!s){s=[];let e=[n];for(;Ml(e);)e=ii(s,e,t);const l=Ml(t)||!Ul(t)?t:t.default?t.default:null;e=jl(l)?[l]:l,Ml(e)&&ii(s,e,!1),o.__localeChainCache.set(r,s)}return s}function ii(e,t,n){let r=!0;for(let o=0;o<t.length&&Nl(r);o++){const s=t[o];jl(s)&&(r=ci(e,t[o],n))}return r}function ci(e,t,n){let r;const o=t.split("-");do{r=ui(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function ui(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(Ml(n)||Ul(n))&&n[o]&&(r=n[o])}return r}const fi=[];fi[0]={w:[0],i:[3,0],"[":[4],o:[7]},fi[1]={w:[1],".":[2],"[":[4],o:[7]},fi[2]={w:[2],i:[3,0],0:[3,0]},fi[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},fi[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},fi[5]={"'":[4,0],o:8,l:[5,0]},fi[6]={'"':[4,0],o:8,l:[6,0]};const pi=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function di(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function hi(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,pi.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const mi=new Map;function gi(e,t){return Wl(e)?e[t]:null}const vi="en-US",_i=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let yi,bi,ki;let xi=null;const wi=()=>xi;let Si=null;const Ci=e=>{Si=e};let Li=0;function Oi(e={}){const t=Al(e.onWarn)?e.onWarn:Bl,n=jl(e.version)?e.version:"11.1.7",r=jl(e.locale)||Al(e.locale)?e.locale:vi,o=Al(r)?vi:r,s=Ml(e.fallbackLocale)||Ul(e.fallbackLocale)||jl(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,l=Ul(e.messages)?e.messages:Ei(o),a=Ul(e.datetimeFormats)?e.datetimeFormats:Ei(o),i=Ul(e.numberFormats)?e.numberFormats:Ei(o),c=Ol(Fl(),e.modifiers,{upper:(e,t)=>"text"===t&&jl(e)?e.toUpperCase():"vnode"===t&&Wl(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&jl(e)?e.toLowerCase():"vnode"===t&&Wl(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&jl(e)?_i(e):"vnode"===t&&Wl(e)&&"__v_isVNode"in e?_i(e.children):e}),u=e.pluralRules||Fl(),f=Al(e.missing)?e.missing:null,p=!Nl(e.missingWarn)&&!Cl(e.missingWarn)||e.missingWarn,d=!Nl(e.fallbackWarn)&&!Cl(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,m=!!e.unresolving,g=Al(e.postTranslation)?e.postTranslation:null,v=Ul(e.processor)?e.processor:null,_=!Nl(e.warnHtmlMessage)||e.warnHtmlMessage,y=!!e.escapeParameter,b=Al(e.messageCompiler)?e.messageCompiler:yi,k=Al(e.messageResolver)?e.messageResolver:bi||gi,x=Al(e.localeFallbacker)?e.localeFallbacker:ki||li,w=Wl(e.fallbackContext)?e.fallbackContext:void 0,S=e,C=Wl(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,L=Wl(S.__numberFormatters)?S.__numberFormatters:new Map,O=Wl(S.__meta)?S.__meta:{};Li++;const E={version:n,cid:Li,locale:r,fallbackLocale:s,messages:l,modifiers:c,pluralRules:u,missing:f,missingWarn:p,fallbackWarn:d,fallbackFormat:h,unresolving:m,postTranslation:g,processor:v,warnHtmlMessage:_,escapeParameter:y,messageCompiler:b,messageResolver:k,localeFallbacker:x,fallbackContext:w,onWarn:t,__meta:O};return E.datetimeFormats=a,E.numberFormats=i,E.__datetimeFormatters=C,E.__numberFormatters=L,__INTLIFY_PROD_DEVTOOLS__&&function(e,t,n){Ya&&Ya.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}(E,n,O),E}const Ei=e=>({[e]:Fl()});function Fi(e,t,n,r,o){const{missing:s,onWarn:l}=e;if(null!==s){const r=s(e,n,t,o);return jl(r)?r:t}return t}function Ti(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Pi(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function Ii(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(Pi(e,t[r]))return!0;return!1}function $i(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:s,localeFallbacker:l}=e,{__datetimeFormatters:a}=e,[i,c,u,f]=Mi(...t);Nl(u.missingWarn)?u.missingWarn:e.missingWarn;Nl(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,d=ri(e,u),h=l(e,o,d);if(!jl(i)||""===i)return new Intl.DateTimeFormat(d,f).format(c);let m,g={},v=null;for(let b=0;b<h.length&&(m=h[b],g=n[m]||{},v=g[i],!Ul(v));b++)Fi(e,i,m,0,"datetime format");if(!Ul(v)||!jl(m))return r?-1:i;let _=`${m}__${i}`;Ll(f)||(_=`${_}__${JSON.stringify(f)}`);let y=a.get(_);return y||(y=new Intl.DateTimeFormat(m,Ol({},v,f)),a.set(_,y)),p?y.formatToParts(c):y.format(c)}const Ri=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Mi(...e){const[t,n,r,o]=e,s=Fl();let l,a=Fl();if(jl(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw ni(Qa);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(n);try{l.toISOString()}catch{throw ni(Qa)}}else if("[object Date]"===Vl(t)){if(isNaN(t.getTime()))throw ni(Za);l=t}else{if(!Sl(t))throw ni(Ja);l=t}return jl(n)?s.key=n:Ul(n)&&Object.keys(n).forEach(e=>{Ri.includes(e)?a[e]=n[e]:s[e]=n[e]}),jl(r)?s.locale=r:Ul(r)&&(a=r),Ul(o)&&(a=o),[s.key||"",l,s,a]}function Ai(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function ji(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:s,localeFallbacker:l}=e,{__numberFormatters:a}=e,[i,c,u,f]=Wi(...t);Nl(u.missingWarn)?u.missingWarn:e.missingWarn;Nl(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,d=ri(e,u),h=l(e,o,d);if(!jl(i)||""===i)return new Intl.NumberFormat(d,f).format(c);let m,g={},v=null;for(let b=0;b<h.length&&(m=h[b],g=n[m]||{},v=g[i],!Ul(v));b++)Fi(e,i,m,0,"number format");if(!Ul(v)||!jl(m))return r?-1:i;let _=`${m}__${i}`;Ll(f)||(_=`${_}__${JSON.stringify(f)}`);let y=a.get(_);return y||(y=new Intl.NumberFormat(m,Ol({},v,f)),a.set(_,y)),p?y.formatToParts(c):y.format(c)}const Ni=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Wi(...e){const[t,n,r,o]=e,s=Fl();let l=Fl();if(!Sl(t))throw ni(Ja);const a=t;return jl(n)?s.key=n:Ul(n)&&Object.keys(n).forEach(e=>{Ni.includes(e)?l[e]=n[e]:s[e]=n[e]}),jl(r)?s.locale=r:Ul(r)&&(l=r),Ul(o)&&(l=o),[s.key||"",a,s,l]}function Di(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const Vi=e=>e,Ui=e=>"",Hi=e=>0===e.length?"":Hl(e),Bi=e=>null==e?"":Ml(e)||Ul(e)&&e.toString===Dl?JSON.stringify(e,null,2):String(e);function Gi(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function zi(e={}){const t=e.locale,n=function(e){const t=Sl(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Sl(e.named.count)||Sl(e.named.n))?Sl(e.named.count)?e.named.count:Sl(e.named.n)?e.named.n:t:t}(e),r=Wl(e.pluralRules)&&jl(t)&&Al(e.pluralRules[t])?e.pluralRules[t]:Gi,o=Wl(e.pluralRules)&&jl(t)&&Al(e.pluralRules[t])?Gi:void 0,s=e.list||[],l=e.named||Fl();Sl(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,l);function a(t,n){const r=Al(e.messages)?e.messages(t,!!n):!!Wl(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):Ui)}const i=Ul(e.processor)&&Al(e.processor.normalize)?e.processor.normalize:Hi,c=Ul(e.processor)&&Al(e.processor.interpolate)?e.processor.interpolate:Bi,u={list:e=>s[e],named:e=>l[e],plural:e=>e[r(n,e.length,o)],linked:(t,...n)=>{const[r,o]=n;let s="text",l="";1===n.length?Wl(r)?(l=r.modifier||l,s=r.type||s):jl(r)&&(l=r||l):2===n.length&&(jl(r)&&(l=r||l),jl(o)&&(s=o||s));const i=a(t,!0)(u),c="vnode"===s&&Ml(i)&&l?i[0]:i;return l?(f=l,e.modifiers?e.modifiers[f]:Vi)(c,s):c;var f},message:a,type:Ul(e.processor)&&jl(e.processor.type)?e.processor.type:"text",interpolate:c,normalize:i,values:Ol(Fl(),s,l)};return u}const Yi=()=>"",qi=e=>Al(e);function Ki(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:o,messageCompiler:s,fallbackLocale:l,messages:a}=e,[i,c]=Qi(...t),u=Nl(c.missingWarn)?c.missingWarn:e.missingWarn,f=Nl(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,p=Nl(c.escapeParameter)?c.escapeParameter:e.escapeParameter,d=!!c.resolvedMessage,h=jl(c.default)||Nl(c.default)?Nl(c.default)?s?i:()=>i:c.default:n?s?i:()=>i:null,m=n||null!=h&&(jl(h)||Al(h)),g=ri(e,c);p&&function(e){Ml(e.list)?e.list=e.list.map(e=>jl(e)?Il(e):e):Wl(e.named)&&Object.keys(e.named).forEach(t=>{jl(e.named[t])&&(e.named[t]=Il(e.named[t]))})}(c);let[v,_,y]=d?[i,g,a[g]||Fl()]:Ji(e,i,g,l,f,u),b=v,k=i;if(d||jl(b)||Ea(b)||qi(b)||m&&(b=h,k=b),!(d||(jl(b)||Ea(b)||qi(b))&&jl(_)))return o?-1:i;let x=!1;const w=qi(b)?b:Zi(e,i,_,b,k,()=>{x=!0});if(x)return b;const S=function(e,t,n,r){const{modifiers:o,pluralRules:s,messageResolver:l,fallbackLocale:a,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,f=(r,o)=>{let s=l(n,r);if(null==s&&(u||o)){const[,,n]=Ji(u||e,r,t,a,i,c);s=l(n,r)}if(jl(s)||Ea(s)){let n=!1;const o=Zi(e,r,t,s,r,()=>{n=!0});return n?Yi:o}return qi(s)?s:Yi},p={locale:t,modifiers:o,pluralRules:s,messages:f};e.processor&&(p.processor=e.processor);r.list&&(p.list=r.list);r.named&&(p.named=r.named);Sl(r.plural)&&(p.pluralIndex=r.plural);return p}(e,_,y,c),C=function(e,t,n){const r=t(n);return r}(0,w,zi(S)),L=r?r(C,i):C;if(__INTLIFY_PROD_DEVTOOLS__){const t={timestamp:Date.now(),key:jl(i)?i:qi(b)?b.key:"",locale:_||(qi(b)?b.locale:""),format:jl(b)?b:qi(b)?b.source:"",message:L};t.meta=Ol({},e.__meta,wi()||{}),qa(t)}return L}function Ji(e,t,n,r,o,s){const{messages:l,onWarn:a,messageResolver:i,localeFallbacker:c}=e,u=c(e,r,n);let f,p=Fl(),d=null;for(let h=0;h<u.length&&(f=u[h],p=l[f]||Fl(),null===(d=i(p,t))&&(d=p[t]),!(jl(d)||Ea(d)||qi(d)));h++)if(!Ii(f,u)){const n=Fi(e,t,f,0,"translate");n!==t&&(d=n)}return[d,f,p]}function Zi(e,t,n,r,o,s){const{messageCompiler:l,warnHtmlMessage:a}=e;if(qi(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==l){const e=()=>r;return e.locale=n,e.key=t,e}const i=l(r,function(e,t,n,r,o,s){return{locale:t,key:n,warnHtmlMessage:o,onError:e=>{throw s&&s(e),e},onCacheKey:e=>((e,t,n)=>wl({l:e,k:t,s:n}))(t,n,e)}}(0,n,o,0,a,s));return i.locale=n,i.key=t,i.source=r,i}function Qi(...e){const[t,n,r]=e,o=Fl();if(!(jl(t)||Sl(t)||qi(t)||Ea(t)))throw ni(Ja);const s=Sl(t)?String(t):(qi(t),t);return Sl(n)?o.plural=n:jl(n)?o.default=n:Ul(n)&&!Ll(n)?o.named=n:Ml(n)&&(o.list=n),Sl(r)?o.plural=r:jl(r)?o.default=r:Ul(r)&&Ol(o,r),[s,o]}"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(Pl().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(Pl().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);const Xi=24,ec=25,tc=26,nc=27,rc=28,oc=29,sc=31,lc=32;function ac(e,...t){return ia(e,null,void 0)}const ic=xl("__translateVNode"),cc=xl("__datetimeParts"),uc=xl("__numberParts"),fc=xl("__setPluralRules"),pc=xl("__injectWithOption"),dc=xl("__dispose");function hc(e){if(!Wl(e))return e;if(Ea(e))return e;for(const t in e)if(Rl(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let o=e,s=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in o||(o[n[e]]=Fl()),!Wl(o[n[e]])){s=!0;break}o=o[n[e]]}if(s||(Ea(o)?Da.includes(n[r])||delete e[t]:(o[n[r]]=e[t],delete e[t])),!Ea(o)){const e=o[n[r]];Wl(e)&&hc(e)}}else Wl(e[t])&&hc(e[t]);return e}function mc(e,t){const{messages:n,__i18n:r,messageResolver:o,flatJson:s}=t,l=Ul(n)?n:Ml(r)?Fl():{[e]:Fl()};if(Ml(r)&&r.forEach(e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(l[t]=l[t]||Fl(),zl(n,l[t])):zl(n,l)}else jl(e)&&zl(JSON.parse(e),l)}),null==o&&s)for(const a in l)Rl(l,a)&&hc(l[a]);return l}function gc(e){return e.type}function vc(e,t,n){let r=Wl(t.messages)?t.messages:Fl();"__i18nGlobal"in n&&(r=mc(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const o=Object.keys(r);if(o.length&&o.forEach(t=>{e.mergeLocaleMessage(t,r[t])}),Wl(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach(n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])})}if(Wl(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach(n=>{e.mergeNumberFormat(n,t.numberFormats[n])})}}function _c(e){return ko(oo,null,e,0)}const yc=()=>[],bc=()=>!1;let kc=0;function xc(e){return(t,n,r,o)=>e(n,r,$o()||void 0,o)}function wc(e={}){const{__root:t,__injectWithOption:n}=e,r=void 0===t,o=e.flatJson,s=kl?wt:St;let l=!Nl(e.inheritLocale)||e.inheritLocale;const a=s(t&&l?t.locale.value:jl(e.locale)?e.locale:vi),i=s(t&&l?t.fallbackLocale.value:jl(e.fallbackLocale)||Ml(e.fallbackLocale)||Ul(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a.value),c=s(mc(a.value,e)),u=s(Ul(e.datetimeFormats)?e.datetimeFormats:{[a.value]:{}}),f=s(Ul(e.numberFormats)?e.numberFormats:{[a.value]:{}});let p=t?t.missingWarn:!Nl(e.missingWarn)&&!Cl(e.missingWarn)||e.missingWarn,d=t?t.fallbackWarn:!Nl(e.fallbackWarn)&&!Cl(e.fallbackWarn)||e.fallbackWarn,h=t?t.fallbackRoot:!Nl(e.fallbackRoot)||e.fallbackRoot,m=!!e.fallbackFormat,g=Al(e.missing)?e.missing:null,v=Al(e.missing)?xc(e.missing):null,_=Al(e.postTranslation)?e.postTranslation:null,y=t?t.warnHtmlMessage:!Nl(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter;const k=t?t.modifiers:Ul(e.modifiers)?e.modifiers:{};let x,w=e.pluralRules||t&&t.pluralRules;x=(()=>{r&&Ci(null);const t={version:"11.1.7",locale:a.value,fallbackLocale:i.value,messages:c.value,modifiers:k,pluralRules:w,missing:null===v?void 0:v,missingWarn:p,fallbackWarn:d,fallbackFormat:m,unresolving:!0,postTranslation:null===_?void 0:_,warnHtmlMessage:y,escapeParameter:b,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=u.value,t.numberFormats=f.value,t.__datetimeFormatters=Ul(x)?x.__datetimeFormatters:void 0,t.__numberFormatters=Ul(x)?x.__numberFormatters:void 0;const n=Oi(t);return r&&Ci(n),n})(),Ti(x,a.value,i.value);const S=Go({get:()=>a.value,set:e=>{x.locale=e,a.value=e}}),C=Go({get:()=>i.value,set:e=>{x.fallbackLocale=e,i.value=e,Ti(x,a.value,e)}}),L=Go(()=>c.value),O=Go(()=>u.value),E=Go(()=>f.value);const F=(e,n,o,s,l,p)=>{let d;a.value,i.value,c.value,u.value,f.value;try{__INTLIFY_PROD_DEVTOOLS__,r||(x.fallbackContext=t?Si:void 0),d=e(x)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(x.fallbackContext=void 0)}if("translate exists"!==o&&Sl(d)&&-1===d||"translate exists"===o&&!d){const[e,r]=n();return t&&h?s(t):l(e)}if(p(d))return d;throw ac(Xi)};function T(...e){return F(t=>Reflect.apply(Ki,null,[t,...e]),()=>Qi(...e),"translate",t=>Reflect.apply(t.t,t,[...e]),e=>e,e=>jl(e))}const P={normalize:function(e){return e.map(e=>jl(e)||Sl(e)||Nl(e)?_c(String(e)):e)},interpolate:e=>e,type:"vnode"};function I(e){return c.value[e]||{}}kc++,t&&kl&&(Br(t.locale,e=>{l&&(a.value=e,x.locale=e,Ti(x,a.value,i.value))}),Br(t.fallbackLocale,e=>{l&&(i.value=e,x.fallbackLocale=e,Ti(x,a.value,i.value))}));const $={id:kc,locale:S,fallbackLocale:C,get inheritLocale(){return l},set inheritLocale(e){l=e,e&&t&&(a.value=t.locale.value,i.value=t.fallbackLocale.value,Ti(x,a.value,i.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:L,get modifiers(){return k},get pluralRules(){return w||{}},get isGlobal(){return r},get missingWarn(){return p},set missingWarn(e){p=e,x.missingWarn=p},get fallbackWarn(){return d},set fallbackWarn(e){d=e,x.fallbackWarn=d},get fallbackRoot(){return h},set fallbackRoot(e){h=e},get fallbackFormat(){return m},set fallbackFormat(e){m=e,x.fallbackFormat=m},get warnHtmlMessage(){return y},set warnHtmlMessage(e){y=e,x.warnHtmlMessage=e},get escapeParameter(){return b},set escapeParameter(e){b=e,x.escapeParameter=e},t:T,getLocaleMessage:I,setLocaleMessage:function(e,t){if(o){const n={[e]:t};for(const e in n)Rl(n,e)&&hc(n[e]);t=n[e]}c.value[e]=t,x.messages=c.value},mergeLocaleMessage:function(e,t){c.value[e]=c.value[e]||{};const n={[e]:t};if(o)for(const r in n)Rl(n,r)&&hc(n[r]);zl(t=n[e],c.value[e]),x.messages=c.value},getPostTranslationHandler:function(){return Al(_)?_:null},setPostTranslationHandler:function(e){_=e,x.postTranslation=e},getMissingHandler:function(){return g},setMissingHandler:function(e){null!==e&&(v=xc(e)),g=e,x.missing=v},[fc]:function(e){w=e,x.pluralRules=w}};return $.datetimeFormats=O,$.numberFormats=E,$.rt=function(...e){const[t,n,r]=e;if(r&&!Wl(r))throw ac(ec);return T(t,n,Ol({resolvedMessage:!0},r||{}))},$.te=function(e,t){return F(()=>{if(!e)return!1;const n=I(jl(t)?t:a.value),r=x.messageResolver(n,e);return Ea(r)||qi(r)||jl(r)},()=>[e],"translate exists",n=>Reflect.apply(n.te,n,[e,t]),bc,e=>Nl(e))},$.tm=function(e){const n=function(e){let t=null;const n=ai(x,i.value,a.value);for(let r=0;r<n.length;r++){const o=c.value[n[r]]||{},s=x.messageResolver(o,e);if(null!=s){t=s;break}}return t}(e);return null!=n?n:t&&t.tm(e)||{}},$.d=function(...e){return F(t=>Reflect.apply($i,null,[t,...e]),()=>Mi(...e),"datetime format",t=>Reflect.apply(t.d,t,[...e]),()=>"",e=>jl(e)||Ml(e))},$.n=function(...e){return F(t=>Reflect.apply(ji,null,[t,...e]),()=>Wi(...e),"number format",t=>Reflect.apply(t.n,t,[...e]),()=>"",e=>jl(e)||Ml(e))},$.getDateTimeFormat=function(e){return u.value[e]||{}},$.setDateTimeFormat=function(e,t){u.value[e]=t,x.datetimeFormats=u.value,Ai(x,e,t)},$.mergeDateTimeFormat=function(e,t){u.value[e]=Ol(u.value[e]||{},t),x.datetimeFormats=u.value,Ai(x,e,t)},$.getNumberFormat=function(e){return f.value[e]||{}},$.setNumberFormat=function(e,t){f.value[e]=t,x.numberFormats=f.value,Di(x,e,t)},$.mergeNumberFormat=function(e,t){f.value[e]=Ol(f.value[e]||{},t),x.numberFormats=f.value,Di(x,e,t)},$[pc]=n,$[ic]=function(...e){return F(t=>{let n;const r=t;try{r.processor=P,n=Reflect.apply(Ki,null,[r,...e])}finally{r.processor=null}return n},()=>Qi(...e),"translate",t=>t[ic](...e),e=>[_c(e)],e=>Ml(e))},$[cc]=function(...e){return F(t=>Reflect.apply($i,null,[t,...e]),()=>Mi(...e),"datetime format",t=>t[cc](...e),yc,e=>jl(e)||Ml(e))},$[uc]=function(...e){return F(t=>Reflect.apply(ji,null,[t,...e]),()=>Wi(...e),"number format",t=>t[uc](...e),yc,e=>jl(e)||Ml(e))},$}function Sc(e={}){const t=wc(function(e){const t=jl(e.locale)?e.locale:vi,n=jl(e.fallbackLocale)||Ml(e.fallbackLocale)||Ul(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=Al(e.missing)?e.missing:void 0,o=!Nl(e.silentTranslationWarn)&&!Cl(e.silentTranslationWarn)||!e.silentTranslationWarn,s=!Nl(e.silentFallbackWarn)&&!Cl(e.silentFallbackWarn)||!e.silentFallbackWarn,l=!Nl(e.fallbackRoot)||e.fallbackRoot,a=!!e.formatFallbackMessages,i=Ul(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=Al(e.postTranslation)?e.postTranslation:void 0,f=!jl(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,d=!Nl(e.sync)||e.sync;let h=e.messages;if(Ul(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce((e,n)=>{const r=e[n]||(e[n]={});return Ol(r,t[n]),e},h||{})}const{__i18n:m,__root:g,__injectWithOption:v}=e,_=e.datetimeFormats,y=e.numberFormats;return{locale:t,fallbackLocale:n,messages:h,flatJson:e.flatJson,datetimeFormats:_,numberFormats:y,missing:r,missingWarn:o,fallbackWarn:s,fallbackRoot:l,fallbackFormat:a,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:f,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:d,__i18n:m,__root:g,__injectWithOption:v}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return Nl(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=Nl(e)?!e:e},get silentFallbackWarn(){return Nl(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=Nl(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return r.__extender=n,r}function Cc(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[fc](t.pluralizationRules||e.pluralizationRules);const n=mc(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(t=>e.mergeLocaleMessage(t,n[t])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n])),t.numberFormats&&Object.keys(t.numberFormats).forEach(n=>e.mergeNumberFormat(n,t.numberFormats[n])),e}const Lc={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Oc(){return ro}const Ec=Sn({name:"i18n-t",props:Ol({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Sl(e)||!isNaN(e)}},Lc),setup(e,t){const{slots:n,attrs:r}=t,o=e.i18n||Ac({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(n).filter(e=>"_"!==e[0]),l=Fl();e.locale&&(l.locale=e.locale),void 0!==e.plural&&(l.plural=jl(e.plural)?+e.plural:e.plural);const a=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce((e,t)=>[...e,...t.type===ro?t.children:[t]],[]);return t.reduce((t,n)=>{const r=e[n];return r&&(t[n]=r()),t},Fl())}(t,s),i=o[ic](e.keypath,a,l),c=Ol(Fl(),r);return zo(jl(e.tag)||Wl(e.tag)?e.tag:Oc(),c,i)}}});function Fc(e,t,n,r){const{slots:o,attrs:s}=t;return()=>{const t={part:!0};let l=Fl();e.locale&&(t.locale=e.locale),jl(e.format)?t.key=e.format:Wl(e.format)&&(jl(e.format.key)&&(t.key=e.format.key),l=Object.keys(e.format).reduce((t,r)=>n.includes(r)?Ol(Fl(),t,{[r]:e.format[r]}):t,Fl()));const a=r(e.value,t,l);let i=[t.key];Ml(a)?i=a.map((e,t)=>{const n=o[e.type],r=n?n({[e.type]:e.value,index:t,parts:a}):[e.value];var s;return Ml(s=r)&&!jl(s[0])&&(r[0].key=`${e.type}-${t}`),r}):jl(a)&&(i=[a]);const c=Ol(Fl(),s);return zo(jl(e.tag)||Wl(e.tag)?e.tag:Oc(),c,i)}}const Tc=Sn({name:"i18n-n",props:Ol({value:{type:Number,required:!0},format:{type:[String,Object]}},Lc),setup(e,t){const n=e.i18n||Ac({useScope:e.scope,__useComponent:!0});return Fc(e,t,Ni,(...e)=>n[uc](...e))}});function Pc(e){if(jl(e))return{path:e};if(Ul(e)){if(!("path"in e))throw ac(rc);return e}throw ac(oc)}function Ic(e){const{path:t,locale:n,args:r,choice:o,plural:s}=e,l={},a=r||{};return jl(n)&&(l.locale=n),Sl(o)&&(l.plural=o),Sl(s)&&(l.plural=s),[t,a,l]}function $c(e,t,...n){const r=Ul(n[0])?n[0]:{};(!Nl(r.globalInstall)||r.globalInstall)&&([Ec.name,"I18nT"].forEach(t=>e.component(t,Ec)),[Tc.name,"I18nN"].forEach(t=>e.component(t,Tc)),[Wc.name,"I18nD"].forEach(t=>e.component(t,Wc))),e.directive("t",function(e){const t=t=>{const{instance:n,value:r}=t;if(!n||!n.$)throw ac(lc);const o=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),s=Pc(r);return[Reflect.apply(o.t,o,[...Ic(s)]),o]};return{created:(n,r)=>{const[o,s]=t(r);kl&&e.global===s&&(n.__i18nWatcher=Br(s.locale,()=>{r.instance&&r.instance.$forceUpdate()})),n.__composer=s,n.textContent=o},unmounted:e=>{kl&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=Pc(t);e.textContent=Reflect.apply(n.t,n,[...Ic(r)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}(t))}const Rc=xl("global-vue-i18n");function Mc(e={}){const t=__VUE_I18N_LEGACY_API__&&Nl(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=!Nl(e.globalInjection)||e.globalInjection,r=new Map,[o,s]=function(e,t){const n=te(),r=__VUE_I18N_LEGACY_API__&&t?n.run(()=>Sc(e)):n.run(()=>wc(e));if(null==r)throw ac(lc);return[n,r]}(e,t),l=xl("");const a={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(e,...r){if(e.__VUE_I18N_SYMBOL__=l,e.provide(e.__VUE_I18N_SYMBOL__,a),Ul(r[0])){const e=r[0];a.__composerExtend=e.__composerExtend,a.__vueI18nExtend=e.__vueI18nExtend}let o=null;!t&&n&&(o=function(e,t){const n=Object.create(null);jc.forEach(e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw ac(lc);const o=xt(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,o)}),e.config.globalProperties.$i18n=n,Nc.forEach(n=>{const r=Object.getOwnPropertyDescriptor(t,n);if(!r||!r.value)throw ac(lc);Object.defineProperty(e.config.globalProperties,`$${n}`,r)});const r=()=>{delete e.config.globalProperties.$i18n,Nc.forEach(t=>{delete e.config.globalProperties[`$${t}`]})};return r}(e,a.global)),__VUE_I18N_FULL_INSTALL__&&$c(e,a,...r),__VUE_I18N_LEGACY_API__&&t&&e.mixin(function(e,t,n){return{beforeCreate(){const r=$o();if(!r)throw ac(lc);const o=this.$options;if(o.i18n){const r=o.i18n;if(o.__i18n&&(r.__i18n=o.__i18n),r.__root=t,this===this.$root)this.$i18n=Cc(e,r);else{r.__injectWithOption=!0,r.__extender=n.__vueI18nExtend,this.$i18n=Sc(r);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=Cc(e,o);else{this.$i18n=Sc({__i18n:o.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&vc(t,o,o),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=$o();if(!e)throw ac(lc);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),n.__deleteInstance(e),delete this.$i18n}}}(s,s.__composer,a));const i=e.unmount;e.unmount=()=>{o&&o(),a.dispose(),i()}},get global(){return s},dispose(){o.stop()},__instances:r,__getInstance:function(e){return r.get(e)||null},__setInstance:function(e,t){r.set(e,t)},__deleteInstance:function(e){r.delete(e)}};return a}function Ac(e={}){const t=$o();if(null==t)throw ac(tc);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw ac(nc);const n=function(e){const t=br(e.isCE?Rc:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw ac(e.isCE?sc:lc);return t}(t),r=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),o=gc(t),s=function(e,t){return Ll(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("global"===s)return vc(r,e,o),r;if("parent"===s){let o=function(e,t,n=!1){let r=null;const o=t.root;let s=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=s;){const t=e;if("composition"===e.mode)r=t.__getInstance(s);else if(__VUE_I18N_LEGACY_API__){const e=t.__getInstance(s);null!=e&&(r=e.__composer,n&&r&&!r[pc]&&(r=null))}if(null!=r)break;if(o===s)break;s=s.parent}return r}(n,t,e.__useComponent);return null==o&&(o=r),o}const l=n;let a=l.__getInstance(t);if(null==a){const n=Ol({},e);"__i18n"in o&&(n.__i18n=o.__i18n),r&&(n.__root=r),a=wc(n),l.__composerExtend&&(a[dc]=l.__composerExtend(a)),function(e,t,n){An(()=>{},t),Dn(()=>{const r=n;e.__deleteInstance(t);const o=r[dc];o&&(o(),delete r[dc])},t)}(l,t,a),l.__setInstance(t,a)}return a}const jc=["locale","fallbackLocale","availableLocales"],Nc=["t","rt","d","n","tm","te"];const Wc=Sn({name:"i18n-d",props:Ol({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Lc),setup(e,t){const n=e.i18n||Ac({useScope:e.scope,__useComponent:!0});return Fc(e,t,Ri,(...e)=>n[cc](...e))}});var Dc;if("boolean"!=typeof __VUE_I18N_FULL_INSTALL__&&(Pl().__VUE_I18N_FULL_INSTALL__=!0),"boolean"!=typeof __VUE_I18N_LEGACY_API__&&(Pl().__VUE_I18N_LEGACY_API__=!0),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(Pl().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(Pl().__INTLIFY_PROD_DEVTOOLS__=!1),yi=function(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&jl(e)){!Nl(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||Ga)(e),r=za[n];if(r)return r;const{ast:o,detectError:s}=function(e,t={}){let n=!1;const r=t.onError||ca;return t.onError=e=>{n=!0,r(e)},{...Oa(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),l=Ua(o);return s?l:za[n]=l}{const t=e.cacheKey;if(t){const n=za[t];return n||(za[t]=Ua(e))}return Ua(e)}},bi=function(e,t){if(!Wl(e))return null;let n=mi.get(t);if(n||(n=function(e){const t=[];let n,r,o,s,l,a,i,c=-1,u=0,f=0;const p=[];function d(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,o="\\"+t,p[0](),!0}for(p[0]=()=>{void 0===r?r=o:r+=o},p[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},p[2]=()=>{p[0](),f++},p[3]=()=>{if(f>0)f--,u=4,p[0]();else{if(f=0,void 0===r)return!1;if(r=hi(r),!1===r)return!1;p[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!d()){if(s=di(n),i=fi[u],l=i[s]||i.l||8,8===l)return;if(u=l[0],void 0!==l[1]&&(a=p[l[1]],a&&(o=n,!1===a())))return;if(7===u)return t}}(t),n&&mi.set(t,n)),!n)return null;const r=n.length;let o=e,s=0;for(;s<r;){const e=n[s];if(Da.includes(e)&&Ea(o))return null;const t=o[e];if(void 0===t)return null;if(Al(o))return null;o=t,s++}return o},ki=ai,__INTLIFY_PROD_DEVTOOLS__){const e=Pl();e.__INTLIFY__=!0,Dc=e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__,Ya=Dc}
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Vc="undefined"!=typeof document;function Uc(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const Hc=Object.assign;function Bc(e,t){const n={};for(const r in t){const o=t[r];n[r]=zc(o)?o.map(e):e(o)}return n}const Gc=()=>{},zc=Array.isArray,Yc=/#/g,qc=/&/g,Kc=/\//g,Jc=/=/g,Zc=/\?/g,Qc=/\+/g,Xc=/%5B/g,eu=/%5D/g,tu=/%5E/g,nu=/%60/g,ru=/%7B/g,ou=/%7C/g,su=/%7D/g,lu=/%20/g;function au(e){return encodeURI(""+e).replace(ou,"|").replace(Xc,"[").replace(eu,"]")}function iu(e){return au(e).replace(Qc,"%2B").replace(lu,"+").replace(Yc,"%23").replace(qc,"%26").replace(nu,"`").replace(ru,"{").replace(su,"}").replace(tu,"^")}function cu(e){return iu(e).replace(Jc,"%3D")}function uu(e){return null==e?"":function(e){return au(e).replace(Yc,"%23").replace(Zc,"%3F")}(e).replace(Kc,"%2F")}function fu(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const pu=/\/$/;function du(e,t,n="/"){let r,o={},s="",l="";const a=t.indexOf("#");let i=t.indexOf("?");return a<i&&a>=0&&(i=-1),i>-1&&(r=t.slice(0,i),s=t.slice(i+1,a>-1?a:t.length),o=e(s)),a>-1&&(r=r||t.slice(0,a),l=t.slice(a,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];".."!==o&&"."!==o||r.push("");let s,l,a=n.length-1;for(s=0;s<r.length;s++)if(l=r[s],"."!==l){if(".."!==l)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+r.slice(s).join("/")}(null!=r?r:t,n),{fullPath:r+(s&&"?")+s+l,path:r,query:o,hash:fu(l)}}function hu(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function mu(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function gu(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!vu(e[n],t[n]))return!1;return!0}function vu(e,t){return zc(e)?_u(e,t):zc(t)?_u(t,e):e===t}function _u(e,t){return zc(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):1===e.length&&e[0]===t}const yu={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var bu,ku,xu,wu;function Su(e){if(!e)if(Vc){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(pu,"")}(ku=bu||(bu={})).pop="pop",ku.push="push",(wu=xu||(xu={})).back="back",wu.forward="forward",wu.unknown="";const Cu=/^[^#]+#/;function Lu(e,t){return e.replace(Cu,"#")+t}const Ou=()=>({left:window.scrollX,top:window.scrollY});function Eu(e){let t;if("el"in e){const n=e.el,r="string"==typeof n&&n.startsWith("#"),o="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Fu(e,t){return(history.state?history.state.position-t:-1)+e}const Tu=new Map;function Pu(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let t=o.includes(e.slice(s))?e.slice(s).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),hu(n,"")}return hu(n,e)+r+o}function Iu(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?Ou():null}}function $u(e){const{history:t,location:n}=window,r={value:Pu(e,n)},o={value:t.state};function s(r,s,l){const a=e.indexOf("#"),i=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+r:location.protocol+"//"+location.host+e+r;try{t[l?"replaceState":"pushState"](s,"",i),o.value=s}catch(c){n[l?"replace":"assign"](i)}}return o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:function(e,n){const l=Hc({},o.value,t.state,{forward:e,scroll:Ou()});s(l.current,l,!0),s(e,Hc({},Iu(r.value,e,null),{position:l.position+1},n),!1),r.value=e},replace:function(e,n){s(e,Hc({},t.state,Iu(o.value.back,e,o.value.forward,!0),n,{position:o.value.position}),!0),r.value=e}}}function Ru(e){const t=$u(e=Su(e)),n=function(e,t,n,r){let o=[],s=[],l=null;const a=({state:s})=>{const a=Pu(e,location),i=n.value,c=t.value;let u=0;if(s){if(n.value=a,t.value=s,l&&l===i)return void(l=null);u=c?s.position-c.position:0}else r(a);o.forEach(e=>{e(n.value,i,{delta:u,type:bu.pop,direction:u?u>0?xu.forward:xu.back:xu.unknown})})};function i(){const{history:e}=window;e.state&&e.replaceState(Hc({},e.state,{scroll:Ou()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",i,{passive:!0}),{pauseListeners:function(){l=n.value},listen:function(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return s.push(t),t},destroy:function(){for(const e of s)e();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",i)}}}(e,t.state,t.location,t.replace);const r=Hc({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Lu.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Mu(e){return"string"==typeof e||"symbol"==typeof e}const Au=Symbol("");var ju,Nu;function Wu(e,t){return Hc(new Error,{type:e,[Au]:!0},t)}function Du(e,t){return e instanceof Error&&Au in e&&(null==t||!!(e.type&t))}(Nu=ju||(ju={}))[Nu.aborted=4]="aborted",Nu[Nu.cancelled=8]="cancelled",Nu[Nu.duplicated=16]="duplicated";const Vu="[^/]+?",Uu={sensitive:!1,strict:!1,start:!0,end:!0},Hu=/[.+*?^${}()[\]/\\]/g;function Bu(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Gu(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const e=Bu(r[n],o[n]);if(e)return e;n++}if(1===Math.abs(o.length-r.length)){if(zu(r))return 1;if(zu(o))return-1}return o.length-r.length}function zu(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Yu={type:0,value:""},qu=/[a-zA-Z0-9_]/;function Ku(e,t,n){const r=function(e,t){const n=Hc({},Uu,t),r=[];let o=n.start?"^":"";const s=[];for(const i of e){const e=i.length?[]:[90];n.strict&&!i.length&&(o+="/");for(let t=0;t<i.length;t++){const r=i[t];let l=40+(n.sensitive?.25:0);if(0===r.type)t||(o+="/"),o+=r.value.replace(Hu,"\\$&"),l+=40;else if(1===r.type){const{value:e,repeatable:n,optional:c,regexp:u}=r;s.push({name:e,repeatable:n,optional:c});const f=u||Vu;if(f!==Vu){l+=10;try{new RegExp(`(${f})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+a.message)}}let p=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(p=c&&i.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),o+=p,l+=20,c&&(l+=-8),n&&(l+=-20),".*"===f&&(l+=-50)}e.push(l)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,n.sensitive?"":"i");return{re:l,score:r,keys:s,parse:function(e){const t=e.match(l),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",o=s[r-1];n[o.name]=e&&o.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:s,repeatable:l,optional:a}=e,i=s in t?t[s]:"";if(zc(i)&&!l)throw new Error(`Provided param "${s}" is an array but it is not repeatable (* or + modifiers)`);const c=zc(i)?i.join("/"):i;if(!c){if(!a)throw new Error(`Missing required param "${s}"`);o.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Yu]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,r=n;const o=[];let s;function l(){s&&o.push(s),s=[]}let a,i=0,c="",u="";function f(){c&&(0===n?s.push({type:0,value:c}):1===n||2===n||3===n?(s.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function p(){c+=a}for(;i<e.length;)if(a=e[i++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&f(),l()):":"===a?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===a?n=2:qu.test(a)?p():(f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&i--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&i--,u="";break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),l(),o}(e.path),n),o=Hc(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Ju(e,t){const n=[],r=new Map;function o(e,n,r){const a=!r,i=Qu(e);i.aliasOf=r&&r.record;const c=nf(t,e),u=[i];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Qu(Hc({},i,{components:r?r.record.components:i.components,path:e,aliasOf:r?r.record:i})))}let f,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&r+u)}if(f=Ku(t,n,c),r?r.alias.push(f):(p=p||f,p!==f&&p.alias.push(f),a&&e.name&&!ef(f)&&s(e.name)),rf(f)&&l(f),i.children){const e=i.children;for(let t=0;t<e.length;t++)o(e[t],f,r&&r.children[t])}r=r||f}return p?()=>{s(p)}:Gc}function s(e){if(Mu(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(s),t.alias.forEach(s))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(s),e.alias.forEach(s))}}function l(e){const t=function(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Gu(e,t[o])<0?r=o:n=o+1}const o=function(e){let t=e;for(;t=t.parent;)if(rf(t)&&0===Gu(e,t))return t;return}(e);o&&(r=t.lastIndexOf(o,r-1));return r}(e,n);n.splice(t,0,e),e.record.name&&!ef(e)&&r.set(e.record.name,e)}return t=nf({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>o(e)),{addRoute:o,resolve:function(e,t){let o,s,l,a={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw Wu(1,{location:e});l=o.record.name,a=Hc(Zu(t.params,o.keys.filter(e=>!e.optional).concat(o.parent?o.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Zu(e.params,o.keys.map(e=>e.name))),s=o.stringify(a)}else if(null!=e.path)s=e.path,o=n.find(e=>e.re.test(s)),o&&(a=o.parse(s),l=o.record.name);else{if(o=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!o)throw Wu(1,{location:e,currentLocation:t});l=o.record.name,a=Hc({},t.params,e.params),s=o.stringify(a)}const i=[];let c=o;for(;c;)i.unshift(c.record),c=c.parent;return{name:l,path:s,params:a,matched:i,meta:tf(i)}},removeRoute:s,clearRoutes:function(){n.length=0,r.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function Zu(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Qu(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Xu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Xu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"==typeof n?n[r]:n;return t}function ef(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function tf(e){return e.reduce((e,t)=>Hc(e,t.meta),{})}function nf(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function rf({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function of(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const e=n[r].replace(Qc," "),o=e.indexOf("="),s=fu(o<0?e:e.slice(0,o)),l=o<0?null:fu(e.slice(o+1));if(s in t){let e=t[s];zc(e)||(e=t[s]=[e]),e.push(l)}else t[s]=l}return t}function sf(e){let t="";for(let n in e){const r=e[n];if(n=cu(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}(zc(r)?r.map(e=>e&&iu(e)):[r&&iu(r)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))})}return t}function lf(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=zc(r)?r.map(e=>null==e?null:""+e):null==r?r:""+r)}return t}const af=Symbol(""),cf=Symbol(""),uf=Symbol(""),ff=Symbol(""),pf=Symbol("");function df(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function hf(e,t,n,r,o,s=e=>e()){const l=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((a,i)=>{const c=e=>{var s;!1===e?i(Wu(4,{from:n,to:t})):e instanceof Error?i(e):"string"==typeof(s=e)||s&&"object"==typeof s?i(Wu(2,{from:t,to:e})):(l&&r.enterCallbacks[o]===l&&"function"==typeof e&&l.push(e),a())},u=s(()=>e.call(r&&r.instances[o],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(e=>i(e))})}function mf(e,t,n,r,o=e=>e()){const s=[];for(const l of e)for(const e in l.components){let a=l.components[e];if("beforeRouteEnter"===t||l.instances[e])if(Uc(a)){const i=(a.__vccOpts||a)[t];i&&s.push(hf(i,n,r,l,e,o))}else{let i=a();s.push(()=>i.then(s=>{if(!s)throw new Error(`Couldn't resolve component "${e}" at "${l.path}"`);const a=(i=s).__esModule||"Module"===i[Symbol.toStringTag]||i.default&&Uc(i.default)?s.default:s;var i;l.mods[e]=s,l.components[e]=a;const c=(a.__vccOpts||a)[t];return c&&hf(c,n,r,l,e,o)()}))}}return s}function gf(e){const t=br(uf),n=br(ff),r=Go(()=>{const n=Ot(e.to);return t.resolve(n)}),o=Go(()=>{const{matched:e}=r.value,{length:t}=e,o=e[t-1],s=n.matched;if(!o||!s.length)return-1;const l=s.findIndex(mu.bind(null,o));if(l>-1)return l;const a=_f(e[t-2]);return t>1&&_f(o)===a&&s[s.length-1].path!==a?s.findIndex(mu.bind(null,e[t-2])):l}),s=Go(()=>o.value>-1&&function(e,t){for(const n in t){const r=t[n],o=e[n];if("string"==typeof r){if(r!==o)return!1}else if(!zc(o)||o.length!==r.length||r.some((e,t)=>e!==o[t]))return!1}return!0}(n.params,r.value.params)),l=Go(()=>o.value>-1&&o.value===n.matched.length-1&&gu(n.params,r.value.params));return{route:r,href:Go(()=>r.value.href),isActive:s,isExactActive:l,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Ot(e.replace)?"replace":"push"](Ot(e.to)).catch(Gc);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}}}const vf=Sn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:gf,setup(e,{slots:t}){const n=ut(gf(e)),{options:r}=br(uf),o=Go(()=>({[yf(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[yf(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&(1===(s=t.default(n)).length?s[0]:s);var s;return e.custom?r:zo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}});function _f(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const yf=(e,t,n)=>null!=e?e:null!=t?t:n;function bf(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const kf=Sn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=br(pf),o=Go(()=>e.route||r.value),s=br(cf,0),l=Go(()=>{let e=Ot(s);const{matched:t}=o.value;let n;for(;(n=t[e])&&!n.components;)e++;return e}),a=Go(()=>o.value.matched[l.value]);yr(cf,Go(()=>l.value+1)),yr(af,a),yr(pf,o);const i=wt();return Br(()=>[i.value,a.value,e.name],([e,t,n],[r,o,s])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&mu(t,o)&&r||(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const r=o.value,s=e.name,l=a.value,c=l&&l.components[s];if(!c)return bf(n.default,{Component:c,route:r});const u=l.props[s],f=u?!0===u?r.params:"function"==typeof u?u(r):u:null,p=zo(c,Hc({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(l.instances[s]=null)},ref:i}));return bf(n.default,{Component:p,route:r})||p}}});function xf(e){const t=Ju(e.routes,e),n=e.parseQuery||of,r=e.stringifyQuery||sf,o=e.history,s=df(),l=df(),a=df(),i=St(yu);let c=yu;Vc&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Bc.bind(null,e=>""+e),f=Bc.bind(null,uu),p=Bc.bind(null,fu);function d(e,s){if(s=Hc({},s||i.value),"string"==typeof e){const r=du(n,e,s.path),l=t.resolve({path:r.path},s),a=o.createHref(r.fullPath);return Hc(r,l,{params:p(l.params),hash:fu(r.hash),redirectedFrom:void 0,href:a})}let l;if(null!=e.path)l=Hc({},e,{path:du(n,e.path,s.path).path});else{const t=Hc({},e.params);for(const e in t)null==t[e]&&delete t[e];l=Hc({},e,{params:f(t)}),s.params=f(s.params)}const a=t.resolve(l,s),c=e.hash||"";a.params=u(p(a.params));const d=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(r,Hc({},e,{hash:(h=c,au(h).replace(ru,"{").replace(su,"}").replace(tu,"^")),path:a.path}));var h;const m=o.createHref(d);return Hc({fullPath:d,hash:c,query:r===sf?lf(e.query):e.query||{}},a,{redirectedFrom:void 0,href:m})}function h(e){return"string"==typeof e?du(n,e,i.value.path):Hc({},e)}function m(e,t){if(c!==e)return Wu(8,{from:t,to:e})}function g(e){return _(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"==typeof n?n(e):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=h(r):{path:r},r.params={}),Hc({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function _(e,t){const n=c=d(e),o=i.value,s=e.state,l=e.force,a=!0===e.replace,u=v(n);if(u)return _(Hc(h(u),{state:"object"==typeof u?Hc({},s,u.state):s,force:l,replace:a}),t||n);const f=n;let p;return f.redirectedFrom=t,!l&&function(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&mu(t.matched[r],n.matched[o])&&gu(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(r,o,n)&&(p=Wu(16,{to:f,from:o}),P(o,o,!0,!1)),(p?Promise.resolve(p):k(f,o)).catch(e=>Du(e)?Du(e,2)?e:T(e):F(e,f,o)).then(e=>{if(e){if(Du(e,2))return _(Hc({replace:a},h(e.to),{state:"object"==typeof e.to?Hc({},s,e.to.state):s,force:l}),t||f)}else e=w(f,o,!0,a,s);return x(f,o,e),e})}function y(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function b(e){const t=R.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function k(e,t){let n;const[r,o,a]=function(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let l=0;l<s;l++){const s=t.matched[l];s&&(e.matched.find(e=>mu(e,s))?r.push(s):n.push(s));const a=e.matched[l];a&&(t.matched.find(e=>mu(e,a))||o.push(a))}return[n,r,o]}(e,t);n=mf(r.reverse(),"beforeRouteLeave",e,t);for(const s of r)s.leaveGuards.forEach(r=>{n.push(hf(r,e,t))});const i=y.bind(null,e,t);return n.push(i),A(n).then(()=>{n=[];for(const r of s.list())n.push(hf(r,e,t));return n.push(i),A(n)}).then(()=>{n=mf(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach(r=>{n.push(hf(r,e,t))});return n.push(i),A(n)}).then(()=>{n=[];for(const r of a)if(r.beforeEnter)if(zc(r.beforeEnter))for(const o of r.beforeEnter)n.push(hf(o,e,t));else n.push(hf(r.beforeEnter,e,t));return n.push(i),A(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=mf(a,"beforeRouteEnter",e,t,b),n.push(i),A(n))).then(()=>{n=[];for(const r of l.list())n.push(hf(r,e,t));return n.push(i),A(n)}).catch(e=>Du(e,8)?e:Promise.reject(e))}function x(e,t,n){a.list().forEach(r=>b(()=>r(e,t,n)))}function w(e,t,n,r,s){const l=m(e,t);if(l)return l;const a=t===yu,c=Vc?history.state:{};n&&(r||a?o.replace(e.fullPath,Hc({scroll:a&&c&&c.scroll},s)):o.push(e.fullPath,s)),i.value=e,P(e,t,n,a),T()}let S;function C(){S||(S=o.listen((e,t,n)=>{if(!M.listening)return;const r=d(e),s=v(r);if(s)return void _(Hc(s,{replace:!0,force:!0}),r).catch(Gc);c=r;const l=i.value;var a,u;Vc&&(a=Fu(l.fullPath,n.delta),u=Ou(),Tu.set(a,u)),k(r,l).catch(e=>Du(e,12)?e:Du(e,2)?(_(Hc(h(e.to),{force:!0}),r).then(e=>{Du(e,20)&&!n.delta&&n.type===bu.pop&&o.go(-1,!1)}).catch(Gc),Promise.reject()):(n.delta&&o.go(-n.delta,!1),F(e,r,l))).then(e=>{(e=e||w(r,l,!1))&&(n.delta&&!Du(e,8)?o.go(-n.delta,!1):n.type===bu.pop&&Du(e,20)&&o.go(-1,!1)),x(r,l,e)}).catch(Gc)}))}let L,O=df(),E=df();function F(e,t,n){T(e);const r=E.list();return r.length&&r.forEach(r=>r(e,t,n)),Promise.reject(e)}function T(e){return L||(L=!e,C(),O.list().forEach(([t,n])=>e?n(e):t()),O.reset()),e}function P(t,n,r,o){const{scrollBehavior:s}=e;if(!Vc||!s)return Promise.resolve();const l=!r&&function(e){const t=Tu.get(e);return Tu.delete(e),t}(Fu(t.fullPath,0))||(o||!r)&&history.state&&history.state.scroll||null;return Kt().then(()=>s(t,n,l)).then(e=>e&&Eu(e)).catch(e=>F(e,t,n))}const I=e=>o.go(e);let $;const R=new Set,M={currentRoute:i,listening:!0,addRoute:function(e,n){let r,o;return Mu(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:d,options:e,push:g,replace:function(e){return g(Hc(h(e),{replace:!0}))},go:I,back:()=>I(-1),forward:()=>I(1),beforeEach:s.add,beforeResolve:l.add,afterEach:a.add,onError:E.add,isReady:function(){return L&&i.value!==yu?Promise.resolve():new Promise((e,t)=>{O.add([e,t])})},install(e){e.component("RouterLink",vf),e.component("RouterView",kf),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Ot(i)}),Vc&&!$&&i.value===yu&&($=!0,g(o.location).catch(e=>{}));const t={};for(const r in yu)Object.defineProperty(t,r,{get:()=>i.value[r],enumerable:!0});e.provide(uf,this),e.provide(ff,ft(t)),e.provide(pf,i);const n=e.unmount;R.add(e),e.unmount=function(){R.delete(e),R.size<1&&(c=yu,S&&S(),S=null,i.value=yu,$=!1,L=!1),n()}}};function A(e){return e.reduce((e,t)=>e.then(()=>b(t)),Promise.resolve())}return M}function wf(){return br(uf)}function Sf(e){return br(ff)}export{So as A,Js as B,ln as C,Ds as D,mo as E,ro as F,qn as G,Kt as H,wf as I,ut as J,Bs as K,Vs as L,Hs as M,Sf as N,xf as O,Ru as P,Xs as Q,ll as R,ss as T,An as a,$o as b,Go as c,Mc as d,bl as e,Sn as f,ne as g,Ac as h,Dn as i,ho as j,bo as k,K as l,Zn as m,H as n,re as o,co as p,Co as q,wt as r,St as s,Et as t,Ot as u,ko as v,Br as w,zn as x,sn as y,wo as z};
