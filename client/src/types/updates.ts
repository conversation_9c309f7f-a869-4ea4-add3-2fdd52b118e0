/**
 * Types pour le système de mises à jour CMS
 */

export interface CmsVersion {
  id: string | number  // L'API retourne string
  version: string
  status: 'development' | 'beta' | 'stable' | 'deprecated'  // L'API utilise "status"
  channel?: string  // Canal de mise à jour
  release_date: string
  changelog?: string | null
  download_url?: string | null
  file_size?: number | null
  file_hash?: string | null
  minimum_php_version?: string | null
  required_extensions?: string | null
  created_at: string
  updated_at: string
}

export interface UpdateDownload {
  id: number
  license_id: number
  version_id: number
  download_token: string
  download_count: number
  last_download_at?: string
  expires_at: string
  created_at: string
}

export interface DownloadProgress {
  loaded: number
  total: number
  percentage: number
  speed: number
  timeRemaining: number
}

export interface UpdatesApiResponse {
  success: boolean
  versions?: CmsVersion[]
  version?: CmsVersion
  download?: {
    token: string
    url: string
    expires_at: string
  }
  message?: string
  errors?: string[]
}

export interface LicenseUpdateInfo {
  license_id: number
  license_key: string
  domain: string
  current_version?: string
  update_permissions: boolean
  channel: 'Dev' | 'Beta' | 'Stable' | 'Obsolète'
  last_update_check?: string
  available_updates: number
}

export interface UpdateStats {
  total_downloads: number
  last_download: string
  current_version: string
  available_versions: number
  channel: string
}

// Options pour les canaux de mise à jour
export const UPDATE_CHANNELS = [
  { 
    value: 'Stable', 
    label: 'Stable', 
    description: 'Versions stables et testées',
    color: 'success'
  },
  { 
    value: 'Beta', 
    label: 'Beta', 
    description: 'Versions en test avec nouvelles fonctionnalités',
    color: 'warning'
  },
  { 
    value: 'Dev', 
    label: 'Développement', 
    description: 'Versions de développement (non recommandées en production)',
    color: 'info'
  },
  { 
    value: 'Obsolète', 
    label: 'Obsolète', 
    description: 'Anciennes versions non maintenues',
    color: 'danger'
  }
] as const

// Types pour les événements de téléchargement
export interface DownloadEvent {
  type: 'start' | 'progress' | 'complete' | 'error' | 'cancel'
  version_id: number
  version: string
  progress?: DownloadProgress
  error?: string
  timestamp: string
}
