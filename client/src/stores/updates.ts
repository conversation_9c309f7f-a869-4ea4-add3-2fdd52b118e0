import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '@/services/api'
import logger from '@/services/logger'
import type {
  CmsVersion,
  UpdateDownload,
  DownloadProgress,
  LicenseUpdateInfo,
  UpdateStats
} from '@/types/updates'

export const useUpdatesStore = defineStore('updates', () => {
  // État
  const versions = ref<CmsVersion[]>([])
  const currentDownload = ref<UpdateDownload | null>(null)
  const downloadProgress = ref<DownloadProgress | null>(null)
  const licenseInfo = ref<LicenseUpdateInfo | null>(null)
  const stats = ref<UpdateStats | null>(null)
  const loading = ref(false)
  const downloading = ref(false)
  const error = ref<string | null>(null)

  // Filtres
  const selectedChannel = ref<string>('Stable')
  const showSecurityOnly = ref(false)

  // Getters
  const availableVersions = computed(() => {
    // Protection contre selectedChannel undefined
    if (!selectedChannel.value) {
      return []
    }

    // Mapper les canaux frontend vers les status API
    const channelToStatus: Record<string, string> = {
      'Stable': 'stable',
      'Beta': 'beta',
      'Dev': 'development',
      'Obsolète': 'deprecated'
    }

    const targetStatus = channelToStatus[selectedChannel.value] || selectedChannel.value.toLowerCase()
    const filtered = versions.value.filter(v => v.status === targetStatus)

    return filtered.sort((a, b) => new Date(b.release_date).getTime() - new Date(a.release_date).getTime())
  })

  const latestVersion = computed(() => {
    const stableVersions = versions.value.filter(v => v.status === 'stable')
    return stableVersions.sort((a, b) => new Date(b.release_date).getTime() - new Date(a.release_date).getTime())[0]
  })

  const hasUpdatePermissions = computed(() => {
    const result = licenseInfo.value?.update_permissions || false
    logger.info('[UpdatesStore] hasUpdatePermissions computed', {
      licenseInfo: licenseInfo.value,
      update_permissions: licenseInfo.value?.update_permissions,
      result: result
    })
    return result
  })

  const canDownload = computed(() => {
    return hasUpdatePermissions.value && !downloading.value
  })

  const downloadProgressPercentage = computed(() => {
    return downloadProgress.value?.percentage || 0
  })

  // Actions
  const fetchVersions = async (channel?: string) => {
    loading.value = true
    error.value = null

    try {
      const params = new URLSearchParams()
      if (channel) {
        params.append('channel', channel)
      }

      const response = await ApiService.routes.client.updates.getVersions(channel ? { channel } : {})

      if (response.data?.versions) {
        versions.value = response.data.versions

        logger.info('[UpdatesStore] Versions récupérées', {
          count: versions.value.length,
          channel
        })
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la récupération des versions')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la récupération des versions'
      logger.error('[UpdatesStore] Erreur fetchVersions', { error: err.message })
    } finally {
      loading.value = false
    }
  }

  const fetchLicenseInfo = async () => {
    try {
      logger.info('[UpdatesStore] Début fetchLicenseInfo')
      const response = await ApiService.routes.client.updates.getLicenseInfo()

      logger.info('[UpdatesStore] Réponse API license-info', {
        status: response.status,
        data: response.data
      })

      if (response.data?.license_info) {
        licenseInfo.value = response.data.license_info

        logger.info('[UpdatesStore] Informations de licence récupérées', {
          license_id: licenseInfo.value?.license_id,
          update_permissions: licenseInfo.value?.update_permissions,
          channel: licenseInfo.value?.channel
        })
      } else {
        logger.warn('[UpdatesStore] Réponse API license-info invalide', {
          has_license_info: !!response.data?.license_info,
          response_data: response.data
        })
      }
    } catch (err: any) {
      logger.error('[UpdatesStore] Erreur fetchLicenseInfo', {
        error: err.message,
        status: err.response?.status,
        response_data: err.response?.data
      })
    }
  }



  const fetchStats = async () => {
    try {
      const response = await ApiService.routes.client.updates.getStats()

      if (response.data?.success && response.data.stats) {
        stats.value = response.data.stats

        logger.info('[UpdatesStore] Statistiques récupérées', { stats: response.data.stats })
      }
    } catch (err: any) {
      logger.error('[UpdatesStore] Erreur fetchStats', { error: err.message })
    }
  }

  const downloadVersion = async (versionId: number) => {
    if (!canDownload.value) {
      error.value = 'Téléchargement non autorisé'
      return false
    }

    downloading.value = true
    downloadProgress.value = null
    error.value = null

    try {
      // Obtenir le token de téléchargement
      const tokenResponse = await ApiService.routes.client.updates.downloadToken({
        version_id: versionId
      })

      if (!tokenResponse.data?.download) {
        throw new Error(tokenResponse.data?.message || 'Impossible d\'obtenir le token de téléchargement')
      }

      const { url, filename } = tokenResponse.data.download
      const version = versions.value.find(v => v.id === versionId)

      logger.info('[UpdatesStore] Début du téléchargement', {
        version_id: versionId,
        version: version?.version,
        filename,
        url
      })

      // Télécharger le fichier avec suivi de progression
      const success = await downloadFile(url, filename || `techcms-${version?.version || 'update'}.zip`, (progress) => {
        downloadProgress.value = progress
      })

      if (success) {
        // Mettre à jour les statistiques
        await fetchStats()
        
        logger.info('[UpdatesStore] Téléchargement terminé avec succès', {
          version_id: versionId,
          version: version?.version
        })
        
        return true
      } else {
        throw new Error('Échec du téléchargement')
      }

    } catch (err: any) {
      // Récupérer le message d'erreur de l'API si disponible
      const apiMessage = err.response?.data?.message || err.message
      error.value = apiMessage || 'Erreur lors du téléchargement'

      logger.error('[UpdatesStore] Erreur downloadVersion', {
        version_id: versionId,
        error: err.message,
        api_message: apiMessage,
        status: err.response?.status,
        response_data: err.response?.data
      })
      return false
    } finally {
      downloading.value = false
      downloadProgress.value = null
    }
  }

  const cancelDownload = () => {
    if (downloading.value) {
      downloading.value = false
      downloadProgress.value = null
      
      logger.info('[UpdatesStore] Téléchargement annulé')
    }
  }

  const checkForUpdates = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.client.updates.check()

      if (response.data?.success) {
        // Recharger les versions après vérification
        await fetchVersions()
        await fetchLicenseInfo()

        logger.info('[UpdatesStore] Vérification des mises à jour terminée')
        return true
      } else {
        throw new Error(response.data?.message || 'Erreur lors de la vérification des mises à jour')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la vérification des mises à jour'
      logger.error('[UpdatesStore] Erreur checkForUpdates', { error: err.message })
      return false
    } finally {
      loading.value = false
    }
  }

  const setChannel = (channel: string) => {
    selectedChannel.value = channel
    fetchVersions(channel)
  }

  const clearError = () => {
    error.value = null
  }

  // Méthode utilitaire pour télécharger un fichier avec progression
  const downloadFile = (url: string, filename: string, onProgress: (progress: DownloadProgress) => void): Promise<boolean> => {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest()
      const startTime = Date.now()

      xhr.open('GET', url, true)
      xhr.responseType = 'blob'

      xhr.onprogress = (event) => {
        if (event.lengthComputable) {
          const loaded = event.loaded
          const total = event.total
          const percentage = Math.round((loaded / total) * 100)
          
          const elapsed = (Date.now() - startTime) / 1000
          const speed = loaded / elapsed // bytes per second
          const remaining = (total - loaded) / speed

          onProgress({
            loaded,
            total,
            percentage,
            speed,
            timeRemaining: remaining
          })
        }
      }

      xhr.onload = () => {
        if (xhr.status === 200) {
          // Créer un lien de téléchargement
          const blob = xhr.response
          const downloadUrl = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = downloadUrl
          // Si le filename contient déjà .zip, ne pas l'ajouter
          link.download = filename.endsWith('.zip') ? filename : `${filename}.zip`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(downloadUrl)
          
          resolve(true)
        } else {
          resolve(false)
        }
      }

      xhr.onerror = () => resolve(false)
      xhr.onabort = () => resolve(false)

      xhr.send()
    })
  }

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s'
  }

  const formatTimeRemaining = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`
    return `${Math.round(seconds / 3600)}h`
  }

  return {
    // État
    versions,
    currentDownload,
    downloadProgress,
    licenseInfo,
    stats,
    loading,
    downloading,
    error,
    selectedChannel,
    showSecurityOnly,
    
    // Getters
    availableVersions,
    latestVersion,
    hasUpdatePermissions,
    canDownload,
    downloadProgressPercentage,
    
    // Actions
    fetchVersions,
    fetchLicenseInfo,
    fetchStats,
    downloadVersion,
    cancelDownload,
    checkForUpdates,
    setChannel,
    clearError,
    
    // Utilitaires
    formatFileSize,
    formatSpeed,
    formatTimeRemaining
  }
})
