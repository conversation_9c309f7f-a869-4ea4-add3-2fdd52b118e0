import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ApiService } from '@/services/api'
import logger from '@/services/logger'
import type {
  ClientLicense,
  LicenseVerification,
  LicenseStats,
  LicenseUsage
} from '@/types/license'
import type { DashboardRealtimeEvent } from '@/types/realtime'

export const useLicensesStore = defineStore('licenses', () => {
  // État
  const licenses = ref<ClientLicense[]>([])
  const currentLicense = ref<ClientLicense | null>(null)
  const licenseUsage = ref<LicenseUsage[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdate = ref<string | null>(null)
  const isUpdating = ref(false)

  // Stats
  const stats = ref<LicenseStats>({
    total_licenses: 0,
    active_licenses: 0,
    expired_licenses: 0,
    expiring_soon: 0
  })

  // Getters
  const getLicenseById = computed(() => (id: number) => {
    return licenses.value.find(license => license.id === id) || null
  })

  const activeLicenses = computed(() => 
    licenses.value.filter(license => license.status === 'active')
  )

  const expiredLicenses = computed(() => 
    licenses.value.filter(license => license.status === 'expired')
  )

  const expiringLicenses = computed(() => {
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000))
    
    return licenses.value.filter(license => {
      if (!license.expires_at || license.status !== 'active') return false
      const expiryDate = new Date(license.expires_at)
      return expiryDate <= thirtyDaysFromNow && expiryDate > now
    })
  })

  const hasLicenses = computed(() => licenses.value.length > 0)

  // Actions
  const fetchLicenses = async () => {
    if (loading.value) return

    loading.value = true
    error.value = null

    try {
      logger.info('[LICENSES STORE] Récupération des licences...')

      const response = await ApiService.routes.client.license.list()

      if (response.data.success) {
        licenses.value = response.data.data
        updateStats()
        lastUpdate.value = new Date().toISOString()

        logger.info('[LICENSES STORE] Licences récupérées avec succès', {
          count: licenses.value.length
        })
      } else {
        throw new Error(response.data.message || 'Erreur lors de la récupération des licences')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la récupération des licences'
      logger.error('[LICENSES STORE] Erreur lors de la récupération des licences', err)
    } finally {
      loading.value = false
    }
  }

  const fetchLicenseDetail = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      logger.info('[LICENSES STORE] Récupération du détail de la licence', { id })

      const response = await ApiService.routes.client.license.get(id)

      if (response.data.success) {
        currentLicense.value = response.data.data
        licenseUsage.value = response.data.usage || []

        logger.info('[LICENSES STORE] Détail de la licence récupéré avec succès', { id })
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Erreur lors de la récupération du détail')
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la récupération du détail'
      logger.error('[LICENSES STORE] Erreur lors de la récupération du détail', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const verifyLicense = async (licenseKey: string, domain: string): Promise<LicenseVerification> => {
    try {
      logger.info('[LICENSES STORE] Vérification de licence', { licenseKey, domain })

      const response = await ApiService.routes.client.license.verify(licenseKey, domain)

      const verification: LicenseVerification = {
        success: response.data.success,
        message: response.data.message,
        license: response.data.license,
        verification_date: new Date().toISOString()
      }

      logger.info('[LICENSES STORE] Vérification terminée', verification)
      return verification
    } catch (err: any) {
      logger.error('[LICENSES STORE] Erreur lors de la vérification', err)
      return {
        success: false,
        message: err.message || 'Erreur lors de la vérification',
        verification_date: new Date().toISOString()
      }
    }
  }

  const updateStats = () => {
    stats.value = {
      total_licenses: licenses.value.length,
      active_licenses: activeLicenses.value.length,
      expired_licenses: expiredLicenses.value.length,
      expiring_soon: expiringLicenses.value.length
    }
  }

  // Gestion des événements temps réel
  const handleRealtimeEvent = (event: DashboardRealtimeEvent) => {
    if (isUpdating.value) return

    isUpdating.value = true
    
    try {
      logger.debug('[LICENSES STORE] Événement temps réel reçu', event)
      
      if (event.type === 'license_updated' && event.data) {
        const updatedLicense = event.data as ClientLicense
        const index = licenses.value.findIndex(l => l.id === updatedLicense.id)
        
        if (index !== -1) {
          licenses.value[index] = updatedLicense
          updateStats()
          
          if (currentLicense.value?.id === updatedLicense.id) {
            currentLicense.value = updatedLicense
          }
          
          logger.info('[LICENSES STORE] Licence mise à jour via temps réel', {
            id: updatedLicense.id
          })
        }
      }
    } catch (err) {
      logger.error('[LICENSES STORE] Erreur lors du traitement de l\'événement temps réel', err as Error)
    } finally {
      isUpdating.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentLicense = () => {
    currentLicense.value = null
    licenseUsage.value = []
  }

  // Formatage des dates
  const formatExpiryDate = (dateString: string | null) => {
    if (!dateString) return 'Aucune expiration'
    
    const date = new Date(dateString)
    const now = new Date()
    
    if (date < now) return 'Expirée'
    
    const diffTime = date.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays <= 30) {
      return `Expire dans ${diffDays} jour${diffDays > 1 ? 's' : ''}`
    }
    
    return date.toLocaleDateString('fr-FR')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'inactive': return 'warning'
      case 'expired': return 'danger'
      default: return 'secondary'
    }
  }

  return {
    // État
    licenses,
    currentLicense,
    licenseUsage,
    loading,
    error,
    lastUpdate,
    isUpdating,
    stats,
    
    // Getters
    getLicenseById,
    activeLicenses,
    expiredLicenses,
    expiringLicenses,
    hasLicenses,
    
    // Actions
    fetchLicenses,
    fetchLicenseDetail,
    verifyLicense,
    handleRealtimeEvent,
    clearError,
    clearCurrentLicense,
    
    // Utilitaires
    formatExpiryDate,
    getStatusColor
  }
})
