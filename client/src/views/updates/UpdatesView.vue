<template>
  <div class="updates-view">
    <!-- En-tête avec titre et actions -->
    <div class="header-section">
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-download"></i>
          Mises à jour CMS
        </h1>
        <p class="page-description">
          Téléchargez les dernières versions de TechCMS selon votre canal de mise à jour
        </p>
      </div>
      <div class="header-actions">
        <button 
          :disabled="updatesStore.loading" 
          class="btn btn-outline"
          @click="checkForUpdates"
        >
          <i class="fas fa-sync" :class="{ 'fa-spin': updatesStore.loading }"></i>
          Vérifier les mises à jour
        </button>
      </div>
    </div>

    <!-- Informations de licence -->
    <div v-if="updatesStore.licenseInfo" class="license-info-card">
      <div class="license-header">
        <h3>
          <i class="fas fa-key"></i>
          Licence utilisée pour les mises à jour
        </h3>
        <span
          class="permission-badge"
          :class="updatesStore.hasUpdatePermissions ? 'badge-success' : 'badge-danger'"
        >
          <i :class="updatesStore.hasUpdatePermissions ? 'fas fa-check' : 'fas fa-times'"></i>
          {{ updatesStore.hasUpdatePermissions ? 'Mises à jour autorisées' : 'Mises à jour non autorisées' }}
        </span>
      </div>

      <div class="auto-selection-info">
        <i class="fas fa-info-circle"></i>
        <span>Licence sélectionnée automatiquement selon vos droits de mise à jour</span>
      </div>
      
      <div class="license-details">
        <div class="detail-item">
          <span class="label">Licence :</span>
          <span class="value">{{ updatesStore.licenseInfo.license_key }}</span>
        </div>
        <div class="detail-item">
          <span class="label">Domaine :</span>
          <span class="value">{{ updatesStore.licenseInfo.domain }}</span>
        </div>
        <div class="detail-item">
          <span class="label">Canal :</span>
          <span class="value">
            <span class="channel-badge" :class="`channel-${(updatesStore.licenseInfo.channel || 'stable').toLowerCase()}`">
              {{ updatesStore.licenseInfo.channel || 'Stable' }}
            </span>
          </span>
        </div>
        <div v-if="updatesStore.licenseInfo.current_version" class="detail-item">
          <span class="label">Version actuelle :</span>
          <span class="value">{{ updatesStore.licenseInfo.current_version }}</span>
        </div>
      </div>
    </div>

    <!-- Statistiques -->
    <div v-if="updatesStore.stats" class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-download"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ updatesStore.stats.total_downloads }}</div>
          <div class="stat-label">Téléchargements</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-code-branch"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ updatesStore.stats.current_version || 'N/A' }}</div>
          <div class="stat-label">Version installée</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ formatDate(updatesStore.stats.last_download) }}</div>
          <div class="stat-label">Dernier téléchargement</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-layer-group"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ updatesStore.stats.available_versions }}</div>
          <div class="stat-label">Versions disponibles</div>
        </div>
      </div>
    </div>

    <!-- Filtres -->
    <div class="filters-section">
      <div class="filters-row">
        <div class="filter-group">
          <label>Canal de mise à jour :</label>
          <select 
            v-model="updatesStore.selectedChannel" 
            class="form-select"
            @change="changeChannel"
          >
            <option v-for="channel in UPDATE_CHANNELS" :key="channel.value" :value="channel.value">
              {{ channel.label }} - {{ channel.description }}
            </option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="checkbox-label">
            <input 
              v-model="updatesStore.showSecurityOnly"
              type="checkbox"
              class="form-checkbox"
            >
            <span class="checkbox-text">Mises à jour de sécurité uniquement</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Message d'erreur -->
    <div v-if="updatesStore.error" class="alert alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      {{ updatesStore.error }}
      <button class="alert-close" @click="updatesStore.clearError()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Message si pas d'autorisation -->
    <div v-if="!updatesStore.hasUpdatePermissions" class="alert alert-warning">
      <i class="fas fa-lock"></i>
      Votre licence ne permet pas le téléchargement de mises à jour. 
      Contactez le support pour plus d'informations.
    </div>

    <!-- Barre de progression de téléchargement -->
    <div v-if="updatesStore.downloading" class="download-progress-card">
      <div class="progress-header">
        <h3>
          <i class="fas fa-download"></i>
          Téléchargement en cours...
        </h3>
        <button class="btn btn-sm btn-danger" @click="cancelDownload">
          <i class="fas fa-times"></i>
          Annuler
        </button>
      </div>
      
      <div v-if="updatesStore.downloadProgress" class="progress-details">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: updatesStore.downloadProgressPercentage + '%' }"
          ></div>
        </div>
        
        <div class="progress-info">
          <span>{{ updatesStore.downloadProgressPercentage }}%</span>
          <span>{{ updatesStore.formatFileSize(updatesStore.downloadProgress.loaded) }} / {{ updatesStore.formatFileSize(updatesStore.downloadProgress.total) }}</span>
          <span>{{ updatesStore.formatSpeed(updatesStore.downloadProgress.speed) }}</span>
          <span>{{ updatesStore.formatTimeRemaining(updatesStore.downloadProgress.timeRemaining) }} restant</span>
        </div>
      </div>
    </div>

    <!-- Liste des versions -->
    <div class="versions-section">
      <div v-if="updatesStore.loading && !updatesStore.downloading" class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Chargement des versions...</span>
      </div>

      <div v-else-if="!hasVersions" class="empty-state">
        <i class="fas fa-box-open"></i>
        <h3>Aucune version disponible</h3>
        <p>Aucune mise à jour n'est disponible pour le canal sélectionné</p>
      </div>

      <div v-else class="versions-grid">
        <div 
          v-for="version in updatesStore.availableVersions" 
          :key="version.id"
          class="version-card"
        >
<!-- En-tête de version -->
          <div class="version-header">
            <div class="version-info">
              <h3 class="version-number">{{ version.version }}</h3>
              <span class="channel-badge" :class="`channel-${version.status.toLowerCase()}`">
                {{ version.status }}
              </span>
            </div>
            <div class="version-date">
              {{ formatDate(version.release_date) }}
            </div>
          </div>

          <!-- Détails de version -->
          <div class="version-details">
            <div class="detail-row">
              <i class="fas fa-hdd"></i>
              <span>Taille : {{ version.file_size ? updatesStore.formatFileSize(version.file_size) : 'Non disponible' }}</span>
            </div>
            
            <div v-if="version.minimum_php_version" class="detail-row">
              <i class="fas fa-arrow-up"></i>
              <span>PHP minimum requis : {{ version.minimum_php_version }}</span>
            </div>
            
            <div class="detail-row">
              <i class="fas fa-fingerprint"></i>
              <span>Checksum : {{ version.file_hash ? version.file_hash.substring(0, 16) + '...' : 'Non disponible' }}</span>
            </div>
          </div>

          <!-- Changelog -->
          <div v-if="version.changelog" class="version-changelog">
            <h4>Notes de version :</h4>
            <!-- eslint-disable-next-line vue/no-v-html -->
            <div class="changelog-content" v-html="formatChangelog(version.changelog)"></div>
          </div>

          <!-- Actions -->
          <div class="version-actions">
            <button 
              :disabled="!updatesStore.canDownload"
              class="btn btn-primary btn-block"
              @click="downloadVersion(version)"
            >
              <i class="fas fa-download"></i>
              Télécharger
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useUpdatesStore } from '@/stores/updates'
import { UPDATE_CHANNELS } from '@/types/updates'
import type { CmsVersion } from '@/types/updates'
import logger from '@/services/logger'

// Store
const updatesStore = useUpdatesStore()

// Computed
const hasVersions = computed(() => updatesStore.availableVersions.length > 0)

// Méthodes utilitaires
const formatDate = (dateString: string) => {
  if (!dateString || dateString === 'N/A') return 'N/A'
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatChangelog = (changelog: string) => {
  // Convertir le markdown simple en HTML
  return changelog
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

// Actions
const checkForUpdates = async () => {
  await updatesStore.checkForUpdates()
}

const changeChannel = () => {
  updatesStore.setChannel(updatesStore.selectedChannel)
}

const downloadVersion = async (version: CmsVersion) => {
  const success = await updatesStore.downloadVersion(Number(version.id))
  if (success) {
    logger.info('[UpdatesView] Version téléchargée avec succès', {
      version: version.version,
      channel: version.channel
    })
  }
}

const cancelDownload = () => {
  updatesStore.cancelDownload()
}

// Lifecycle
onMounted(async () => {
  logger.info('[UpdatesView] Vue montée')

  // Charger les informations de licence (sélection automatique côté backend)
  await updatesStore.fetchLicenseInfo()
  
  // Charger les statistiques
  await updatesStore.fetchStats()
  
  // Charger les versions pour le canal par défaut
  if (updatesStore.licenseInfo) {
    updatesStore.selectedChannel = updatesStore.licenseInfo.channel
  }
  await updatesStore.fetchVersions(updatesStore.selectedChannel)
})
</script>

<style scoped>
.updates-view {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* En-tête */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.header-actions {
  flex-shrink: 0;
}

/* Carte d'informations de licence */
.license-info-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.license-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.license-header h3 {
  margin: 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.permission-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.auto-selection-info {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-size: 0.875rem;
}

.auto-selection-info i {
  color: var(--primary-color);
}

.license-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.detail-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-item .value {
  color: var(--text-primary);
  font-weight: 600;
}

/* Badges de canal */
.channel-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.channel-stable {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.channel-beta {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.channel-dev {
  background: rgba(6, 182, 212, 0.1);
  color: var(--info-color);
}

.channel-obsolète {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Statistiques */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  font-size: 1.25rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Filtres */
.filters-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filters-row {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.filter-group label {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

.form-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  margin: 0;
}

.form-checkbox {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.checkbox-text {
  color: var(--text-primary);
  font-weight: normal;
}

/* Barre de progression */
.download-progress-card {
  background: var(--card-bg);
  border: 1px solid var(--primary-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.progress-header h3 {
  margin: 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), #1e40af);
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-secondary);
  flex-wrap: wrap;
  gap: 1rem;
}

/* Versions */
.versions-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
}

.versions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.version-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  transition: all 0.2s ease;
}

.version-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.version-card.security-update {
  border-color: var(--danger-color);
  background: linear-gradient(135deg, var(--card-bg) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.security-badge {
  position: absolute;
  top: -12px;
  right: 20px;
  background: var(--danger-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.version-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.version-date {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.version-details {
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.detail-row i {
  color: var(--primary-color);
  width: 16px;
}

.version-changelog {
  margin-bottom: 1.5rem;
}

.version-changelog h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.75rem 0;
}

.changelog-content {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  max-height: 100px;
  overflow-y: auto;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 6px;
}

.version-actions {
  margin-top: auto;
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: 0.875rem;
}

.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
}

.btn-block {
  width: 100%;
  justify-content: center;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1e40af;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: var(--text-secondary);
  cursor: not-allowed;
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--border-color);
}

.btn-outline:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

/* États */
.loading-state, .empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.loading-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.alert-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
}

.alert-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .updates-view {
    padding: 1rem;
  }

  .header-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .license-details {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .versions-grid {
    grid-template-columns: 1fr;
  }

  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
