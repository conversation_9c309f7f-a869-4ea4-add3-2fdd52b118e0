<template>
  <div id="client-licenses">
    <div class="header-box">
      <h1>{{ $t('licenses.title') }}</h1>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-key"></i>
        </div>
        <div class="stat-number">{{ licensesStore.stats.total_licenses }}</div>
        <div class="stat-label">{{ $t('licenses.stats.total') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ licensesStore.stats.active_licenses }}</div>
        <div class="stat-label">{{ $t('licenses.stats.active') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stat-number">{{ licensesStore.stats.expiring_soon }}</div>
        <div class="stat-label">{{ $t('licenses.stats.expiring_soon') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-number">{{ licensesStore.stats.expired_licenses }}</div>
        <div class="stat-label">{{ $t('licenses.stats.expired') }}</div>
      </div>
    </div>

    <!-- Liste des licences -->
    <div class="card card-box">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-list"></i>
          {{ $t('licenses.my_licenses') }}
        </h3>
      </div>

      <div class="card-body">
        <!-- État de chargement -->
        <div v-if="licensesStore.loading" class="loading-state">
          <div class="spinner"></div>
          <p>{{ $t('common.loading') }}</p>
        </div>

        <!-- État d'erreur -->
        <div v-else-if="licensesStore.error" class="error-state">
          <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <p>{{ licensesStore.error }}</p>
          <button class="btn btn-primary" @click="retryLoad">
            {{ $t('common.retry') }}
          </button>
        </div>

        <!-- Aucune licence -->
        <div v-else-if="!licensesStore.hasLicenses" class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-key"></i>
          </div>
          <h4>{{ $t('licenses.no_licenses.title') }}</h4>
          <p>{{ $t('licenses.no_licenses.description') }}</p>
        </div>

        <!-- Liste des licences -->
        <div v-else class="licenses-list">
          <div 
            v-for="license in licensesStore.licenses" 
            :key="license.id"
            class="license-item"
            @click="viewLicenseDetail(license.id)"
          >
            <div class="license-header">
              <div class="license-key">
                <i class="fas fa-key"></i>
                <code>{{ license.license_key }}</code>
              </div>
              <div class="license-status">
                <span 
                  class="status-badge"
                  :class="`status-${licensesStore.getStatusColor(license.status)}`"
                >
                  {{ $t(`licenses.status.${license.status}`) }}
                </span>
              </div>
            </div>

            <div class="license-details">
              <div class="license-info">
                <div class="info-item">
                  <i class="fas fa-globe"></i>
                  <span>{{ $t('licenses.domain_limit') }}: {{ license.domain_limit }}</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-download"></i>
                  <span>{{ $t('licenses.installation_limit') }}: {{ license.installation_limit }}</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-calendar"></i>
                  <span>{{ licensesStore.formatExpiryDate(license.expires_at) }}</span>
                </div>
              </div>

              <div class="license-actions">
                <button 
                  class="btn btn-sm btn-outline"
                  @click.stop="viewLicenseDetail(license.id)"
                >
                  {{ $t('licenses.view_details') }}
                </button>
              </div>
            </div>

            <!-- Barre de progression pour les limites -->
            <div v-if="license.current_installations !== undefined" class="license-usage">
              <div class="usage-bar">
                <div class="usage-label">
                  {{ $t('licenses.installations_used') }}
                </div>
                <div class="progress">
                  <div 
                    class="progress-bar"
                    :class="{
                      'progress-bar-warning': (license.current_installations / license.installation_limit) > 0.8,
                      'progress-bar-danger': (license.current_installations / license.installation_limit) >= 1
                    }"
                    :style="{ width: Math.min(100, (license.current_installations / license.installation_limit) * 100) + '%' }"
                  ></div>
                </div>
                <div class="usage-text">
                  {{ license.current_installations }} / {{ license.installation_limit }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
// import { useI18n } from 'vue-i18n'
import { useLicensesStore } from '@/stores/licenses'
// import { useRealtimeStore } from '@/stores/realtime' // Non utilisé
import logger from '@/services/logger'

// Composables
const router = useRouter()
// const { t } = useI18n()
const licensesStore = useLicensesStore()
// const realtimeStore = useRealtimeStore() // Non utilisé

// Méthodes
const loadLicenses = async () => {
  try {
    await licensesStore.fetchLicenses()
  } catch (error) {
    logger.error('[LicensesView] Erreur lors du chargement des licences', error as Error)
  }
}

const retryLoad = () => {
  licensesStore.clearError()
  loadLicenses()
}

const viewLicenseDetail = (licenseId: number) => {
  router.push(`/licenses/${licenseId}`)
}

/*
// Gestion des événements temps réel
const handleRealtimeEvent = (event: any) => {
  if (event.type.startsWith('license_')) {
    licensesStore.handleRealtimeEvent(event)
  }
}
*/

// Lifecycle
onMounted(async () => {
  logger.info('[LicensesView] Composant monté')
  
  // Charger les licences
  await loadLicenses()
  
  // S'abonner aux événements temps réel
  // realtimeStore.subscribe('licenses', handleRealtimeEvent) // Méthode non disponible
})

onUnmounted(() => {
  logger.info('[LicensesView] Composant démonté')
  
  // Se désabonner des événements temps réel
  // realtimeStore.unsubscribe('licenses', handleRealtimeEvent) // Méthode non disponible
  
  // Nettoyer les erreurs
  licensesStore.clearError()
})
</script>

<style scoped>
.licenses-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.license-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--card-bg);
}

.license-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.license-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.license-key {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.license-key code {
  background: var(--code-bg);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-success {
  background: var(--success-bg);
  color: var(--success-color);
}

.status-warning {
  background: var(--warning-bg);
  color: var(--warning-color);
}

.status-danger {
  background: var(--danger-bg);
  color: var(--danger-color);
}

.license-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.license-info {
  display: flex;
  gap: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.license-usage {
  margin-top: 1rem;
}

.usage-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.usage-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  min-width: 120px;
}

.progress {
  flex: 1;
  height: 8px;
  background: var(--progress-bg);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--success-color);
  transition: width 0.3s ease;
}

.progress-bar-warning {
  background: var(--warning-color);
}

.progress-bar-danger {
  background: var(--danger-color);
}

.usage-text {
  font-size: 0.875rem;
  color: var(--text-muted);
  min-width: 60px;
  text-align: right;
}

.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .license-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .license-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .usage-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .usage-label {
    min-width: auto;
  }

  .usage-text {
    min-width: auto;
    text-align: left;
  }
}
</style>
