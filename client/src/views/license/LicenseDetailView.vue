<template>
  <div id="license-detail">
    <div class="header-box">
      <div class="header-content">
        <button class="btn btn-outline btn-sm" @click="goBack">
          <i class="fas fa-arrow-left"></i>
          {{ $t('common.back') }}
        </button>
        <h1>{{ $t('licenses.license_details') }}</h1>
      </div>
    </div>

    <!-- État de chargement -->
    <div v-if="licensesStore.loading" class="loading-state">
      <div class="spinner"></div>
      <p>{{ $t('common.loading') }}</p>
    </div>

    <!-- État d'erreur -->
    <div v-else-if="licensesStore.error" class="error-state">
      <div class="error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <p>{{ licensesStore.error }}</p>
      <button class="btn btn-primary" @click="retryLoad">
        {{ $t('common.retry') }}
      </button>
    </div>

    <!-- Détails de la licence -->
    <div v-else-if="license" class="license-detail-content">
      <!-- Informations principales -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-key"></i>
            {{ $t('licenses.license_information') }}
          </h3>
          <span 
            class="status-badge"
            :class="`status-${licensesStore.getStatusColor(license.status)}`"
          >
            {{ $t(`licenses.status.${license.status}`) }}
          </span>
        </div>

        <div class="card-body">
          <div class="license-info-grid">
            <div class="info-group">
              <label>{{ $t('licenses.license_key') }}</label>
              <div class="license-key-display">
                <code>{{ license.license_key }}</code>
                <button class="btn btn-sm btn-outline" :title="$t('common.copy')" @click="copyLicenseKey">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
            </div>

            <div class="info-group">
              <label>{{ $t('licenses.status.label') }}</label>
              <span 
                class="status-badge"
                :class="`status-${licensesStore.getStatusColor(license.status)}`"
              >
                {{ $t(`licenses.status.${license.status}`) }}
              </span>
            </div>

            <div class="info-group">
              <label>{{ $t('licenses.domain_limit') }}</label>
              <span>{{ license.domain_limit }}</span>
            </div>

            <div class="info-group">
              <label>{{ $t('licenses.installation_limit') }}</label>
              <span>{{ license.installation_limit }}</span>
            </div>

            <div class="info-group">
              <label>{{ $t('licenses.expires_at') }}</label>
              <span>{{ licensesStore.formatExpiryDate(license.expires_at) }}</span>
            </div>

            <div class="info-group">
              <label>{{ $t('licenses.created_at') }}</label>
              <span>{{ formatDate(license.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Domaines et IPs autorisés -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-shield-alt"></i>
            {{ $t('licenses.restrictions') }}
          </h3>
        </div>

        <div class="card-body">
          <div class="restrictions-grid">
            <div class="restriction-group">
              <h4>{{ $t('licenses.allowed_domains') }}</h4>
              <div v-if="allowedDomains.length > 0" class="restriction-list">
                <div v-for="domain in allowedDomains" :key="domain" class="restriction-item">
                  <i class="fas fa-globe"></i>
                  <span>{{ domain }}</span>
                </div>
              </div>
              <div v-else class="no-restrictions">
                {{ $t('licenses.no_domain_restrictions') }}
              </div>
            </div>

            <div class="restriction-group">
              <h4>{{ $t('licenses.allowed_ips') }}</h4>
              <div v-if="allowedIps.length > 0" class="restriction-list">
                <div v-for="ip in allowedIps" :key="ip" class="restriction-item">
                  <i class="fas fa-network-wired"></i>
                  <span>{{ ip }}</span>
                </div>
              </div>
              <div v-else class="no-restrictions">
                {{ $t('licenses.no_ip_restrictions') }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Utilisation actuelle -->
      <div v-if="licensesStore.licenseUsage.length > 0" class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ $t('licenses.current_usage') }}
          </h3>
        </div>

        <div class="card-body">
          <div class="usage-list">
            <div 
              v-for="usage in licensesStore.licenseUsage" 
              :key="`${usage.domain}-${usage.ip}`"
              class="usage-item"
            >
              <div class="usage-info">
                <div class="usage-domain">
                  <i class="fas fa-globe"></i>
                  <span>{{ usage.domain }}</span>
                </div>
                <div class="usage-ip">
                  <i class="fas fa-network-wired"></i>
                  <span>{{ usage.ip }}</span>
                </div>
                <div class="usage-count">
                  <i class="fas fa-download"></i>
                  <span>{{ usage.installation_count }} installation{{ usage.installation_count > 1 ? 's' : '' }}</span>
                </div>
              </div>
              <div class="usage-date">
                <i class="fas fa-clock"></i>
                <span>{{ formatDate(usage.last_seen) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-tools"></i>
            {{ $t('licenses.actions') }}
          </h3>
        </div>

        <div class="card-body">
          <div class="actions-grid">
            <button class="btn btn-primary" :disabled="verifying" @click="verifyLicense">
              <i class="fas fa-check-circle"></i>
              <span v-if="verifying">{{ $t('licenses.verifying') }}</span>
              <span v-else>{{ $t('licenses.verify_license') }}</span>
            </button>

            <button class="btn btn-outline" :disabled="licensesStore.loading" @click="refreshData">
              <i class="fas fa-sync-alt"></i>
              {{ $t('licenses.refresh') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// import { useI18n } from 'vue-i18n'
import { useLicensesStore } from '@/stores/licenses'
// import { useRealtimeStore } from '@/stores/realtime' // Non utilisé
import logger from '@/services/logger'

// Composables
const route = useRoute()
const router = useRouter()
// const { t } = useI18n()
const licensesStore = useLicensesStore()
// const realtimeStore = useRealtimeStore() // Non utilisé

// État local
const verifying = ref(false)

// Computed
const licenseId = computed(() => parseInt(route.params.id as string))
const license = computed(() => licensesStore.currentLicense)

const allowedDomains = computed(() => {
  if (!license.value?.allowed_domains) return []
  return license.value.allowed_domains.split(',').filter(d => d.trim())
})

const allowedIps = computed(() => {
  if (!license.value?.allowed_ips) return []
  return license.value.allowed_ips.split(',').filter(ip => ip.trim())
})

// Méthodes
const loadLicenseDetail = async () => {
  try {
    await licensesStore.fetchLicenseDetail(licenseId.value)
  } catch (error) {
    logger.error('[LicenseDetailView] Erreur lors du chargement du détail', error as Error)
  }
}

const retryLoad = () => {
  licensesStore.clearError()
  loadLicenseDetail()
}

const goBack = () => {
  router.push('/licenses')
}

const copyLicenseKey = async () => {
  if (!license.value) return
  
  try {
    await navigator.clipboard.writeText(license.value.license_key)
    // TODO: Ajouter une notification de succès
  } catch (error) {
    logger.error('[LicenseDetailView] Erreur lors de la copie', error as Error)
  }
}

const verifyLicense = async () => {
  if (!license.value) return
  
  verifying.value = true
  
  try {
    const result = await licensesStore.verifyLicense(
      license.value.license_key,
      window.location.hostname
    )
    
    // TODO: Afficher le résultat de la vérification
    logger.info('[LicenseDetailView] Résultat de la vérification', result)
  } catch (error) {
    logger.error('[LicenseDetailView] Erreur lors de la vérification', error as Error)
  } finally {
    verifying.value = false
  }
}

const refreshData = () => {
  loadLicenseDetail()
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Gestion des événements temps réel
/*
const handleRealtimeEvent = (event: any) => {
  if (event.type === 'license_updated' && event.data?.id === licenseId.value) {
    licensesStore.handleRealtimeEvent(event)
  }
}
*/

// Lifecycle
onMounted(async () => {
  logger.info('[LicenseDetailView] Composant monté', { licenseId: licenseId.value })
  
  // Charger le détail de la licence
  await loadLicenseDetail()
  
  // S'abonner aux événements temps réel
  // realtimeStore.subscribe('license-detail', handleRealtimeEvent) // Méthode non disponible
})

onUnmounted(() => {
  logger.info('[LicenseDetailView] Composant démonté')
  
  // Se désabonner des événements temps réel
  // realtimeStore.unsubscribe('license-detail', handleRealtimeEvent) // Méthode non disponible
  
  // Nettoyer les données
  licensesStore.clearError()
  licensesStore.clearCurrentLicense()
})
</script>

<style scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.license-detail-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.license-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-group label {
  font-weight: 600;
  color: var(--text-muted);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.license-key-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.license-key-display code {
  background: var(--code-bg);
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  flex: 1;
}

.restrictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.restriction-group h4 {
  margin-bottom: 1rem;
  color: var(--text-color);
}

.restriction-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.restriction-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--bg-light);
  border-radius: 4px;
}

.no-restrictions {
  color: var(--text-muted);
  font-style: italic;
  padding: 1rem;
  text-align: center;
  background: var(--bg-light);
  border-radius: 4px;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.usage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-light);
}

.usage-info {
  display: flex;
  gap: 1.5rem;
}

.usage-domain,
.usage-ip,
.usage-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.usage-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.actions-grid {
  display: flex;
  gap: 1rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-success {
  background: var(--success-bg);
  color: var(--success-color);
}

.status-warning {
  background: var(--warning-bg);
  color: var(--warning-color);
}

.status-danger {
  background: var(--danger-bg);
  color: var(--danger-color);
}

.loading-state,
.error-state {
  text-align: center;
  padding: 3rem 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .license-info-grid {
    grid-template-columns: 1fr;
  }

  .restrictions-grid {
    grid-template-columns: 1fr;
  }

  .usage-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .usage-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .actions-grid {
    flex-direction: column;
  }
}
</style>
