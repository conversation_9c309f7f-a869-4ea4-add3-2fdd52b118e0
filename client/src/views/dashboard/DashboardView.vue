<template>
  <div id="client-dashboard">
    <div class="header-box">
      <h1>{{ $t('dashboard.title') }}</h1>
    </div>

    <div class="stats-grid box-grid">
      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-key"></i>
        </div>
        <div class="stat-number">{{ dashboardStore.totalLicenses }}</div>
        <div class="stat-label">{{ $t('dashboard.my_licenses') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-file-invoice"></i>
        </div>
        <div class="stat-number">{{ dashboardStore.stats?.invoices.unpaid || 0 }}</div>
        <div class="stat-label">{{ $t('dashboard.unpaid_invoices') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-headset"></i>
        </div>
        <div class="stat-number">{{ dashboardStore.openTicketsCount }}</div>
        <div class="stat-label">{{ $t('dashboard.open_tickets') }}</div>
      </div>

      <div class="stat-card card-box">
        <div class="stat-icon">
          <i class="fas fa-credit-card"></i>
        </div>
        <div class="stat-number">{{ formatCurrency(dashboardStore.totalUnpaidAmount) }}</div>
        <div class="stat-label">{{ $t('dashboard.total_due') }}</div>
      </div>
    </div>

    <div class="dashboard-grid box-grid">
      <!-- Mes Licences -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-key"></i>
            {{ $t('dashboard.my_licenses') }}
          </h3>
          <button class="btn btn-sm btn-outline" @click="navigateToLicenses">
            {{ $t('dashboard.view_all') }}
          </button>
        </div>
        <div class="card-body">
          <div v-if="dashboardStore.loading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <div v-else class="license-list">
            <div v-for="license in dashboardStore.recentLicenses" :key="license.id" class="license-item">
              <div class="license-info">
                <div class="license-icon">
                  <i class="fas fa-key"></i>
                </div>
                <div class="license-details">
                  <div class="license-key">
                    <a href="#" class="license-link" @click.prevent="navigateToLicenseDetail(license.id)">
                      {{ license.license_key }}
                    </a>
                  </div>
                  <div class="license-domains">
                    {{ license.allowed_domains }}
                  </div>
                </div>
              </div>
              <div class="license-meta">
                <div class="license-date">
                  {{ formatDate(license.created_at) }}
                </div>
                <div class="license-status">
                  <span :class="getStatusClass(license.status)">
                    {{ $t('status.' + license.status) }}
                  </span>
                </div>
              </div>
            </div>
            <div v-if="dashboardStore.recentLicenses.length === 0" class="no-data">
              {{ $t('licenses.no_licenses') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Factures Récentes -->
      <div class="card card-box">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-file-invoice"></i>
            {{ $t('dashboard.recent_invoices') }}
          </h3>
          <button class="btn btn-sm btn-outline" @click="navigateToBilling">
            {{ $t('dashboard.view_all') }}
          </button>
        </div>
        <div class="card-body">
          <div v-if="dashboardStore.loading" class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <div v-else class="invoice-list">
            <div v-for="invoice in dashboardStore.recentInvoices" :key="invoice.id" class="invoice-item">
              <div class="invoice-info">
                <div class="invoice-icon">
                  <i class="fas fa-file-invoice"></i>
                </div>
                <div class="invoice-details">
                  <div class="invoice-number">
                    <a href="#" class="invoice-link" @click.prevent="navigateToInvoiceDetail(invoice.id)">
                      {{ $t('billing.invoice_number') }} #{{ invoice.number }}
                    </a>
                  </div>
                  <div class="invoice-date">
                    {{ formatDate(invoice.created_at) }}
                  </div>
                </div>
              </div>
              <div class="invoice-meta">
                <div class="invoice-amount">
                  {{ formatCurrency(invoice.amount) }}
                </div>
                <div class="invoice-status">
                  <span :class="getStatusClass(invoice.status)">
                    {{ $t('status.' + invoice.status) }}
                  </span>
                </div>
              </div>
            </div>
            <div v-if="dashboardStore.recentInvoices.length === 0" class="no-data">
              {{ $t('billing.no_invoices') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tickets Récents - Section complète -->
    <div class="card card-box">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-headset"></i>
          {{ $t('dashboard.recent_tickets') }}
        </h3>
        <button class="btn btn-sm btn-outline" @click="navigateToSupport">
          {{ $t('dashboard.view_all') }}
        </button>
      </div>
      <div class="card-body">
        <div v-if="dashboardStore.loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <div v-else class="ticket-list">
          <div v-for="ticket in dashboardStore.recentTickets" :key="ticket.id" class="ticket-item">
            <div class="ticket-info">
              <div class="ticket-icon">
                <i class="fas fa-headset"></i>
              </div>
              <div class="ticket-details">
                <div class="ticket-title">
                  <a href="#" class="ticket-link" @click.prevent="navigateToTicketDetail(ticket.id)">
                    {{ ticket.title }}
                  </a>
                </div>
                <div class="ticket-id">
                  #{{ ticket.id }}
                </div>
              </div>
            </div>
            <div class="ticket-meta">
              <div class="ticket-date">
                {{ formatDate(ticket.created_at) }}
              </div>
              <div class="ticket-status">
                <span :class="getStatusClass(ticket.status)">
                  {{ $t('status.' + ticket.status) }}
                </span>
              </div>
            </div>
          </div>
          <div v-if="dashboardStore.recentTickets.length === 0" class="no-data">
            {{ $t('support.no_tickets') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDashboardStore } from '@/stores/dashboard'
import { useRealtimeStore } from '@/stores/realtime'
import { useAuthStore } from '@/stores/auth'
import logger from '@/services/logger'

// Router et Stores
const router = useRouter()
const dashboardStore = useDashboardStore()
const realtimeStore = useRealtimeStore()
const authStore = useAuthStore()

// Charger les données et initialiser le temps réel
onMounted(async () => {
  try {
    logger.info('[DASHBOARD] Chargement des données du dashboard')

    // Charger les données initiales
    await dashboardStore.fetchDashboardData()
    logger.info('[DASHBOARD] Données du dashboard chargées avec succès')

    // Initialiser le temps réel si l'utilisateur est authentifié et le service n'est pas déjà initialisé
    if (authStore.isAuthenticated && !realtimeStore.initialized) {
      await realtimeStore.init()
      logger.info('[DASHBOARD] Service temps réel initialisé')
    }

    // Initialiser les mises à jour temps réel du dashboard
    if (authStore.isAuthenticated && !dashboardStore.realtimeInitialized) {
      await dashboardStore.initRealtimeUpdates()
      logger.info('[DASHBOARD] Temps réel dashboard initialisé avec succès')
    }
  } catch (err: any) {
    logger.error('[DASHBOARD] Erreur lors du chargement des données', { error: err })
    // En cas d'erreur, on peut afficher un message d'erreur ou garder les données vides
  }
})

// Nettoyage lors de la destruction du composant
onUnmounted(() => {
  if (dashboardStore.realtimeInitialized) {
    dashboardStore.stopRealtimeUpdates()
    logger.info('[DASHBOARD] Nettoyage du composant dashboard')
  }
})

// Utilitaires
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(value)
}

function formatDate(date: string): string {
  const dateObj = new Date(date)
  return new Intl.DateTimeFormat('fr-FR', {
    day: 'numeric',
    month: 'long',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

function getStatusClass(status: string): string {
  const classes: Record<string, string> = {
    active: 'status-badge status-success',
    suspended: 'status-badge status-danger',
    paid: 'status-badge status-success',
    unpaid: 'status-badge status-warning',
    open: 'status-badge status-info',
    in_progress: 'status-badge status-warning',
    resolved: 'status-badge status-success',
    closed: 'status-badge status-secondary'
  }
  return classes[status] || 'status-badge status-default'
}



// Fonctions de navigation
function navigateToLicenses() {
  router.push('/licenses')
}

function navigateToBilling() {
  router.push('/billing')
}

function navigateToSupport() {
  router.push('/support')
}

function navigateToLicenseDetail(licenseId: number) {
  router.push(`/licenses/${licenseId}`)
}

function navigateToInvoiceDetail(invoiceId: number) {
  router.push(`/billing/invoice/${invoiceId}`)
}

function navigateToTicketDetail(ticketId: number) {
  router.push(`/support/ticket/${ticketId}`)
}
</script>

<style scoped>
@import '@/assets/css/pages/dashboard.css';
</style>