<?php
/**
 * Contrôleur API pour les commandes depuis la vitrine
 * 
 * @package TechCMS
 * @version 1.1.0
 */

namespace TechCMS\Api\V1\Controllers\website;

use TechCMS\Api\V1\Controllers\Website\WebsiteBaseController;
use TechCMS\Common\Models\OrderModel;
use TechCMS\Common\Models\OrderItemModel;
use TechCMS\Common\Models\LicenseTemplateModel;
use TechCMS\Common\Models\ClientModel;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\LicenseProvisioningService;
use TechCMS\Api\V1\Core\JWT;

/**
 * Contrôleur de commandes pour le website/boutique
 */
class WebsiteOrderController extends WebsiteBaseController
{
    private $orderModel;
    private $orderItemModel;
    private $templateModel;
    private $clientModel;
    private $provisioningService;

    public function __construct()
    {
        parent::__construct();
        $this->orderModel = new OrderModel();
        $this->orderItemModel = new OrderItemModel();
        $this->templateModel = new LicenseTemplateModel();
        $this->clientModel = new ClientModel();
        $this->provisioningService = LicenseProvisioningService::getInstance();
    }

    /**
     * Créer une nouvelle commande
     */
    public function create()
    {
        try {
            Logger::channel('api')->info('[WebsiteOrderController] Début création de commande');

            // Vérifier l'authentification (obligatoire pour créer une commande)
            $user = $this->getAuthenticatedUser();
            if (!$user || $user['user_type'] !== 'client') {
                Logger::channel('api')->warning('[WebsiteOrderController] Authentification échouée', [
                    'user' => $user,
                    'user_type' => $user['user_type'] ?? 'none'
                ]);
                $this->sendError('Authentification requise pour créer une commande', 401);
                return;
            }

            Logger::channel('api')->info('[WebsiteOrderController] Utilisateur authentifié', [
                'user_id' => $user['user_id'],
                'user_type' => $user['user_type']
            ]);

            $data = $this->getRequestData();

            Logger::channel('api')->info('[WebsiteOrderController] Données reçues', [
                'data_keys' => array_keys($data),
                'items_count' => count($data['items'] ?? []),
                'payment_method' => $data['payment_method'] ?? 'none',
                'has_billing_address' => isset($data['billing_address']),
                'raw_data' => $data
            ]);
            
            // Validation des données requises
            if (empty($data['items']) || !is_array($data['items'])) {
                $this->sendError('Items de commande requis', 400);
                return;
            }

            if (empty($data['billing_address'])) {
                $this->sendError('Adresse de facturation requise', 400);
                return;
            }

            if (empty($data['payment_method'])) {
                $this->sendError('Méthode de paiement requise', 400);
                return;
            }

            // Valider les items et domaines
            Logger::channel('api')->info('[WebsiteOrderController] Validation des items', [
                'items' => $data['items']
            ]);

            $validationResult = $this->validateOrderItems($data['items']);
            if (!$validationResult['valid']) {
                Logger::channel('api')->error('[WebsiteOrderController] Validation échouée', [
                    'message' => $validationResult['message'],
                    'items' => $data['items']
                ]);
                $this->sendError($validationResult['message'], 400);
                return;
            }

            Logger::channel('api')->info('[WebsiteOrderController] Validation réussie');

            // Valider et calculer les totaux
            $orderCalculation = $this->calculateOrderTotals($data['items']);
            if (!$orderCalculation) {
                $this->sendError('Erreur lors du calcul des totaux', 400);
                return;
            }

            // Calculer les taxes
            $country = $data['billing_address']['country'] ?? 'FR';
            $taxAmount = $this->calculateTax($orderCalculation['subtotal'], $country);

            // Préparer les données de commande
            $orderData = [
                'client_id' => $user['user_id'],
                'status' => 'pending',
                'subtotal' => $orderCalculation['subtotal'],
                'tax_amount' => $taxAmount,
                'total' => $orderCalculation['subtotal'] + $taxAmount,
                'currency' => 'EUR',
                'payment_method' => $data['payment_method'],
                'payment_status' => 'pending',
                'billing_address' => $data['billing_address'],
                'notes' => $data['notes'] ?? null
            ];

            // Créer la commande
            $order = $this->orderModel->create($orderData);
            if (!$order) {
                $this->sendError('Erreur lors de la création de la commande', 500);
                return;
            }

            // Créer les items de commande
            $orderItems = $this->orderItemModel->createMultiple($order['id'], $orderCalculation['items']);
            if (!$orderItems) {
                $this->sendError('Erreur lors de la création des items de commande', 500);
                return;
            }

            // Récupérer la commande complète
            $completeOrder = $this->orderModel->getOrderWithItems($order['id']);

            Logger::channel('api')->info('Commande créée depuis la vitrine', [
                'order_id' => $order['id'],
                'order_number' => $order['order_number'],
                'client_id' => $user['user_id'],
                'total' => $orderData['total'],
                'items_count' => count($orderItems)
            ]);

            // Vérifier si la commande est gratuite (total = 0) pour provisionnement automatique
            if ($completeOrder['total'] == 0) {
                Logger::channel('api')->info('[WebsiteOrderController] Commande gratuite détectée, provisionnement automatique', [
                    'order_id' => $completeOrder['id'],
                    'total' => $completeOrder['total']
                ]);

                try {
                    // Marquer comme payée et provisionner
                    $this->orderModel->update($completeOrder['id'], [
                        'payment_status' => 'paid',
                        'status' => 'completed'
                    ]);

                    // Déclencher le provisionnement
                    $provisioningService = \TechCMS\Common\Core\LicenseProvisioningService::getInstance();
                    $provisioningResult = $provisioningService->provisionOrder($completeOrder['id']);

                    Logger::channel('api')->info('[WebsiteOrderController] Provisionnement automatique terminé', [
                        'order_id' => $completeOrder['id'],
                        'provisioning_success' => $provisioningResult['success'],
                        'licenses_created' => count($provisioningResult['licenses'])
                    ]);

                    // Récupérer la commande mise à jour
                    $completeOrder = $this->orderModel->getOrderWithItems($completeOrder['id']);

                    $this->sendResponse([
                        'success' => true,
                        'message' => 'Commande créée et provisionnée automatiquement',
                        'order' => $completeOrder,
                        'provisioning' => $provisioningResult,
                        'payment_url' => null
                    ], 201);

                } catch (\Exception $e) {
                    Logger::channel('api')->error('[WebsiteOrderController] Erreur provisionnement automatique', [
                        'order_id' => $completeOrder['id'],
                        'error' => $e->getMessage()
                    ]);

                    // Retourner la commande même si le provisionnement échoue
                    $this->sendResponse([
                        'success' => true,
                        'message' => 'Commande créée avec succès',
                        'order' => $completeOrder,
                        'payment_url' => null,
                        'provisioning_error' => $e->getMessage()
                    ], 201);
                }
            } else {
                // Commande payante, générer l'URL de paiement
                $paymentUrl = $this->generatePaymentUrl($completeOrder, $data['payment_method']);

                $this->sendResponse([
                    'success' => true,
                    'message' => 'Commande créée avec succès',
                    'order' => $completeOrder,
                    'payment_url' => $paymentUrl
                ], 201);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la création de commande', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError('Erreur lors de la création de la commande', 500);
        }
    }

    /**
     * Récupérer une commande par son numéro
     */
    public function getByNumber($orderNumber)
    {
        try {
            // Vérifier l'authentification
            $user = $this->getAuthenticatedUser();
            if (!$user || $user['user_type'] !== 'client') {
                $this->sendError('Authentification requise', 401);
                return;
            }

            $order = $this->orderModel->getByOrderNumber($orderNumber);
            if (!$order) {
                $this->sendError('Commande non trouvée', 404);
                return;
            }

            // Vérifier que la commande appartient au client connecté
            if ($order['client_id'] != $user['user_id']) {
                $this->sendError('Accès non autorisé à cette commande', 403);
                return;
            }

            // Récupérer les items
            $order['items'] = $this->orderItemModel->getOrderItems($order['id']);

            $this->sendResponse([
                'success' => true,
                'order' => $order
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération de commande', [
                'order_number' => $orderNumber,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération de la commande', 500);
        }
    }

    /**
     * Récupérer les commandes du client connecté
     */
    public function getClientOrders()
    {
        try {
            // Vérifier l'authentification
            $user = $this->getAuthenticatedUser();
            if (!$user || $user['user_type'] !== 'client') {
                $this->sendError('Authentification requise', 401);
                return;
            }

            $page = (int)($_GET['page'] ?? 1);
            $perPage = min(50, (int)($_GET['per_page'] ?? 15));

            $result = $this->orderModel->getClientOrders($user['user_id'], $page, $perPage);

            $this->sendResponse([
                'success' => true,
                'orders' => $result['orders'],
                'pagination' => [
                    'current_page' => $result['page'],
                    'per_page' => $result['per_page'],
                    'total' => $result['total'],
                    'total_pages' => $result['total_pages']
                ]
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des commandes client', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération des commandes', 500);
        }
    }

    /**
     * Vérifier le statut de paiement d'une commande
     */
    public function checkPaymentStatus($orderId)
    {
        try {
            // Vérifier l'authentification
            $user = $this->getAuthenticatedUser();
            if (!$user || $user['user_type'] !== 'client') {
                $this->sendError('Authentification requise', 401);
                return;
            }

            $order = $this->orderModel->find($orderId);
            if (!$order) {
                $this->sendError('Commande non trouvée', 404);
                return;
            }

            // Vérifier que la commande appartient au client connecté
            if ($order['client_id'] != $user['user_id']) {
                $this->sendError('Accès non autorisé à cette commande', 403);
                return;
            }

            $this->sendResponse([
                'success' => true,
                'status' => $order['payment_status'],
                'order_status' => $order['status']
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la vérification du statut de paiement', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la vérification du statut', 500);
        }
    }

    /**
     * Calculer les totaux de commande
     */
    private function calculateOrderTotals($items)
    {
        try {
            $subtotal = 0;
            $calculatedItems = [];

            foreach ($items as $item) {
                // Valider les données de l'item
                if (empty($item['template_id']) || empty($item['quantity'])) {
                    return false;
                }

                // Récupérer le template
                $template = $this->templateModel->find($item['template_id']);
                if (!$template || $template['status'] !== 'active') {
                    return false;
                }

                // Calculer le total de l'item
                $itemCalculation = $this->orderItemModel->calculateItemTotal(
                    $item['template_id'],
                    $item['quantity'],
                    $item['customizations'] ?? []
                );

                if (!$itemCalculation) {
                    return false;
                }

                // Normaliser et préparer les customisations
                $customizations = $item['customizations'] ?? [];
                if (isset($customizations['primary_domain'])) {
                    $customizations['primary_domain'] = $this->normalizeDomain($customizations['primary_domain']);
                }
                if (isset($customizations['additional_domains_list']) && is_array($customizations['additional_domains_list'])) {
                    $customizations['additional_domains_list'] = array_map([$this, 'normalizeDomain'],
                        array_filter($customizations['additional_domains_list']));
                }

                $calculatedItems[] = [
                    'template_id' => $item['template_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $itemCalculation['unit_price'],
                    'total_price' => $itemCalculation['total_price'],
                    'customizations' => $customizations
                ];

                $subtotal += $itemCalculation['total_price'];
            }

            return [
                'subtotal' => $subtotal,
                'items' => $calculatedItems
            ];

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du calcul des totaux', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Calculer les taxes
     */
    private function calculateTax($subtotal, $country)
    {
        // Taux de TVA par pays
        $taxRates = [
            'FR' => 0.20, // France: 20%
            'BE' => 0.21, // Belgique: 21%
            'DE' => 0.19, // Allemagne: 19%
            'ES' => 0.21, // Espagne: 21%
            'IT' => 0.22, // Italie: 22%
            'NL' => 0.21, // Pays-Bas: 21%
            'CH' => 0.077, // Suisse: 7.7%
            'GB' => 0.20, // Royaume-Uni: 20%
            'US' => 0, // États-Unis: pas de TVA
            'CA' => 0.05 // Canada: 5% (GST)
        ];

        $rate = $taxRates[$country] ?? 0;
        return round($subtotal * $rate, 2);
    }

    /**
     * Générer l'URL de paiement
     */
    private function generatePaymentUrl($order, $paymentMethod)
    {
        // TODO: Intégrer avec les passerelles de paiement (Stripe, PayPal, etc.)
        // Pour l'instant, retourner null (paiement manuel)
        
        switch ($paymentMethod) {
            case 'stripe':
                // TODO: Intégrer Stripe
                return null;
            case 'paypal':
                // TODO: Intégrer PayPal
                return null;
            case 'bank_transfer':
                // Paiement par virement - pas d'URL
                return null;
            default:
                return null;
        }
    }

    /**
     * Récupérer le token client depuis les cookies ou headers
     */
    private function getClientToken()
    {
        // Vérifier dans les cookies
        if (isset($_COOKIE['client_token'])) {
            return $_COOKIE['client_token'];
        }

        // Vérifier dans les headers Authorization
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * Obtient les données de l'utilisateur connecté
     */
    protected function getAuthenticatedUser()
    {
        try {
            $token = $this->getClientToken();
            if (!$token) {
                return null;
            }

            $payload = JWT::verify($token);
            if (!$payload || $payload['role'] !== 'client') {
                return null;
            }

            return [
                'user_id' => $payload['sub'],
                'user_type' => 'client'
            ];
        } catch (\Exception $e) {
            Logger::channel('api')->debug('Erreur lors de la vérification du token', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Webhook pour confirmer le paiement et déclencher le provisionnement
     * Note: Cet endpoint est accessible sans authentification pour les webhooks externes
     */
    public function confirmPayment($orderId)
    {
        try {
            $data = $this->getRequestData();

            // Valider les données du webhook
            if (empty($data['payment_status']) || empty($data['payment_method'])) {
                $this->sendError('Données de paiement incomplètes', 400);
                return;
            }

            // Récupérer la commande
            $order = $this->orderModel->find($orderId);
            if (!$order) {
                $this->sendError('Commande non trouvée', 404);
                return;
            }

            // Mettre à jour le statut de paiement
            $result = $this->orderModel->updatePaymentStatus(
                $orderId,
                $data['payment_status'],
                $data['payment_method']
            );

            if (!$result) {
                $this->sendError('Erreur lors de la mise à jour du paiement', 500);
                return;
            }

            // Si le paiement est confirmé, déclencher le provisionnement automatique
            if ($data['payment_status'] === 'paid') {
                $provisioningResult = $this->provisioningService->provisionOrder($orderId);

                Logger::channel('api')->info('Provisionnement automatique déclenché', [
                    'order_id' => $orderId,
                    'provisioning_success' => $provisioningResult['success'],
                    'licenses_created' => count($provisioningResult['licenses'])
                ]);

                $this->sendResponse([
                    'success' => true,
                    'message' => 'Paiement confirmé et provisionnement déclenché',
                    'payment_status' => $data['payment_status'],
                    'provisioning' => $provisioningResult
                ]);
            } else {
                $this->sendResponse([
                    'success' => true,
                    'message' => 'Statut de paiement mis à jour',
                    'payment_status' => $data['payment_status']
                ]);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la confirmation de paiement', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la confirmation de paiement', 500);
        }
    }

    /**
     * Déclencher manuellement le provisionnement d'une commande
     */
    public function triggerProvisioning($orderId)
    {
        try {
            // Vérifier l'authentification admin (optionnel - pour usage interne)
            $user = $this->getAuthenticatedUser();
            if (!$user) {
                $this->sendError('Authentification requise', 401);
                return;
            }

            // Vérifier que la commande peut être provisionnée
            if (!$this->provisioningService->canProvisionOrder($orderId)) {
                $this->sendError('Cette commande ne peut pas être provisionnée', 400);
                return;
            }

            // Déclencher le provisionnement
            $result = $this->provisioningService->provisionOrder($orderId);

            Logger::channel('api')->info('Provisionnement manuel déclenché', [
                'order_id' => $orderId,
                'user_id' => $user['user_id'],
                'success' => $result['success']
            ]);

            $this->sendResponse([
                'success' => $result['success'],
                'message' => $result['message'],
                'licenses' => $result['licenses'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du provisionnement manuel', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors du provisionnement', 500);
        }
    }

    /**
     * Récupère les données de la requête (JSON, POST ou GET)
     */
    private function getRequestData() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        // Fallback vers $_POST si pas de JSON
        if (!$data) {
            $data = $_POST;
        }

        // Fallback vers $_GET pour certaines requêtes
        if (!$data) {
            $data = $_GET;
        }

        return $data ?: [];
    }

    /**
     * Valide les items de commande et leurs domaines
     */
    private function validateOrderItems($items) {
        Logger::channel('api')->info('[WebsiteOrderController] Début validation items', [
            'items_count' => is_array($items) ? count($items) : 0,
            'items_type' => gettype($items)
        ]);

        if (empty($items) || !is_array($items)) {
            Logger::channel('api')->error('[WebsiteOrderController] Items invalides', [
                'items' => $items,
                'is_array' => is_array($items),
                'is_empty' => empty($items)
            ]);
            return ['valid' => false, 'message' => 'Items de commande requis'];
        }

        foreach ($items as $index => $item) {
            Logger::channel('api')->info('[WebsiteOrderController] Validation item', [
                'index' => $index,
                'item' => $item,
                'has_customizations' => isset($item['customizations'])
            ]);
            // Validation des champs requis
            if (empty($item['template_id']) || empty($item['quantity'])) {
                return ['valid' => false, 'message' => "Template ID et quantité requis pour l'item " . ($index + 1)];
            }

            // Validation des customisations
            if (isset($item['customizations'])) {
                $customizations = $item['customizations'];

                // Validation du domaine principal (requis)
                if (empty($customizations['primary_domain'])) {
                    Logger::channel('api')->error('[WebsiteOrderController] Domaine principal manquant', [
                        'item_index' => $index,
                        'customizations' => $customizations
                    ]);
                    return ['valid' => false, 'message' => "Domaine principal requis pour l'item " . ($index + 1)];
                }

                Logger::channel('api')->info('[WebsiteOrderController] Validation domaine principal', [
                    'item_index' => $index,
                    'primary_domain' => $customizations['primary_domain']
                ]);

                // Validation du format du domaine principal
                if (!$this->isValidDomain($customizations['primary_domain'])) {
                    Logger::channel('api')->error('[WebsiteOrderController] Format domaine invalide', [
                        'item_index' => $index,
                        'primary_domain' => $customizations['primary_domain']
                    ]);
                    return ['valid' => false, 'message' => "Format de domaine invalide pour '{$customizations['primary_domain']}'"];
                }

                // Validation des domaines supplémentaires si présents
                if (isset($customizations['additional_domains_list']) && is_array($customizations['additional_domains_list'])) {
                    foreach ($customizations['additional_domains_list'] as $domain) {
                        if (!empty($domain) && !$this->isValidDomain($domain)) {
                            return ['valid' => false, 'message' => "Format de domaine supplémentaire invalide pour '{$domain}'"];
                        }
                    }
                }

                // Validation des valeurs numériques
                if (isset($customizations['additional_domains']) && !is_numeric($customizations['additional_domains'])) {
                    return ['valid' => false, 'message' => "Nombre de domaines supplémentaires invalide"];
                }

                if (isset($customizations['additional_installations']) && !is_numeric($customizations['additional_installations'])) {
                    return ['valid' => false, 'message' => "Nombre d'installations supplémentaires invalide"];
                }
            } else {
                return ['valid' => false, 'message' => "Domaine principal requis pour l'item " . ($index + 1)];
            }
        }

        return ['valid' => true, 'message' => 'Validation réussie'];
    }

    /**
     * Valide le format d'un nom de domaine
     */
    private function isValidDomain($domain) {
        // Nettoyer le domaine
        $domain = strtolower(trim($domain));

        // Supprimer le protocole si présent
        $domain = preg_replace('/^https?:\/\//', '', $domain);

        // Supprimer le chemin si présent
        $domain = preg_replace('/\/.*$/', '', $domain);

        // Supprimer www. si présent
        $domain = preg_replace('/^www\./', '', $domain);

        // Vérifications de base
        if (empty($domain) || strlen($domain) > 253) {
            return false;
        }

        // Vérifier le format avec une regex
        $pattern = '/^[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?)*$/';
        if (!preg_match($pattern, $domain)) {
            return false;
        }

        // Vérifier qu'il y a au moins un point (TLD requis)
        if (strpos($domain, '.') === false) {
            return false;
        }

        // Vérifier que le TLD fait au moins 2 caractères
        $parts = explode('.', $domain);
        $tld = end($parts);
        if (strlen($tld) < 2) {
            return false;
        }

        // Vérifier avec filter_var comme validation finale
        return filter_var('http://' . $domain, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Normalise un nom de domaine
     */
    private function normalizeDomain($domain) {
        $domain = strtolower(trim($domain));
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = preg_replace('/\/.*$/', '', $domain);
        $domain = preg_replace('/^www\./', '', $domain);
        return $domain;
    }
}
